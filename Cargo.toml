[package]
name = "xml-check"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "跨平台XML变速数据检测工具"
license = "MIT"
repository = "https://github.com/yourusername/xml-check"
keywords = ["xml", "validation", "data-analysis", "cross-platform"]
categories = ["command-line-utilities", "parsing"]

[[bin]]
name = "xml-check"
path = "src/main.rs"

[[bin]]
name = "xml-check-gui"
path = "src/gui/main.rs"

[dependencies]
# XML处理
roxmltree = "0.19"
quick-xml = "0.31"
serde = { version = "1.0", features = ["derive"] }
serde-xml-rs = "0.6"

# CLI支持
clap = { version = "4.4", features = ["derive", "color"] }
colored = "2.0"
indicatif = "0.17"

# GUI支持
egui = "0.24"
eframe = { version = "0.24", default-features = false, features = [
    "accesskit",
    "default_fonts",
    "glow",
    "persistence",
] }

# 错误处理和日志
anyhow = "1.0"
thiserror = "1.0"
log = "0.4"
env_logger = "0.10"

# 文件处理
walkdir = "2.4"
glob = "0.3"

# 序列化和配置
toml = "0.8"
serde_json = "1.0"

# 异步支持
tokio = { version = "1.0", features = ["full"] }

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# GUI额外依赖
rfd = "0.12"  # 文件对话框

# 测试工具
[dev-dependencies]
tempfile = "3.8"
assert_cmd = "2.0"
predicates = "3.0"

[build-dependencies]
chrono = "0.4"

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true

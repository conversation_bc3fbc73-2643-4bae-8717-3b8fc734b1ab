# XML变速数据检测工具

一个跨平台的XML数据检测工具，专门用于检测和验证XML文件中的变速数据。

## 功能特性

- 🔍 **智能检测**: 自动识别XML中的变速数据模式
- 📊 **数据分析**: 分析速度变化趋势和异常点
- ✅ **数据验证**: 多层次的数据完整性和一致性检查
- 📋 **详细报告**: 支持JSON、XML、HTML、文本、CSV等多种报告格式
- 🖥️ **双重界面**: 提供命令行和图形用户界面
- 🌐 **跨平台**: 支持Windows、macOS和Linux
- ⚡ **高性能**: 基于Rust开发，处理大文件性能优异

## 快速开始

### 安装

#### 从源码编译

```bash
# 克隆仓库
git clone https://github.com/yourusername/xml-check.git
cd xml-check

# 编译发布版本
cargo build --release

# 编译CLI版本
cargo build --release --bin xml-check

# 编译GUI版本
cargo build --release --bin xml-check-gui
```

#### 预编译二进制文件

从[Releases页面](https://github.com/yourusername/xml-check/releases)下载适合您操作系统的预编译版本。

### 使用方法

#### 命令行界面 (CLI)

```bash
# 检测单个XML文件
./xml-check sample.xml

# 检测目录中的所有XML文件
./xml-check /path/to/xml/files --recursive

# 指定输出格式和文件
./xml-check sample.xml --format json --output report.json

# 使用自定义配置
./xml-check sample.xml --config custom_config.toml

# 显示详细输出
./xml-check sample.xml --verbose

# 查看帮助
./xml-check --help
```

#### 图形用户界面 (GUI)

```bash
# 启动GUI应用
./xml-check-gui
```

或者直接双击可执行文件。

## 配置

工具使用TOML格式的配置文件。默认配置文件位于`config/default.toml`。

### 配置示例

```toml
[parser]
ignore_namespaces = false
preserve_whitespace = false
max_file_size = 104857600  # 100MB
supported_encodings = ["UTF-8", "UTF-16", "ISO-8859-1"]

[detector]
speed_data_patterns = [
    "//data[@speed]",
    "//velocity",
    "//*[@speed]"
]
speed_attributes = ["speed", "velocity", "rate"]
time_attributes = ["time", "timestamp", "t"]
min_speed = 0.0
max_speed = 1000.0
speed_change_threshold = 50.0

[validator]
enable_integrity_check = true
enable_range_check = true
enable_consistency_check = true

[reporter]
output_format = "Json"
include_details = true
include_statistics = true
language = "zh-CN"
```

## XML数据格式

工具支持多种XML格式的变速数据：

### 基本格式

```xml
<?xml version="1.0" encoding="UTF-8"?>
<root>
    <data speed="10.5" time="1.0"/>
    <data speed="15.2" time="2.0"/>
    <data speed="8.3" time="3.0"/>
</root>
```

### 复杂格式

```xml
<?xml version="1.0" encoding="UTF-8"?>
<vehicle_data>
    <speed_measurements>
        <data_point speed="0.0" time="0.0" acceleration="0.0"/>
        <data_point speed="10.5" time="1.0" acceleration="10.5"/>
    </speed_measurements>
</vehicle_data>
```

### 不同属性名

```xml
<measurements>
    <record velocity="45.2" timestamp="1.0"/>
    <point rate="25.0" t="2.0"/>
</measurements>
```

## 检测功能

### 变速数据检测

- 自动识别速度相关的XML元素和属性
- 支持多种命名约定（speed、velocity、rate等）
- 提取时间序列信息
- 计算速度统计信息（最小值、最大值、平均值、标准差）

### 异常检测

- **范围异常**: 超出预设速度范围的数据点
- **变化异常**: 速度变化超过阈值的情况
- **统计异常**: 偏离平均值超过3个标准差的数据点
- **格式异常**: 数据格式错误或缺失

### 数据验证

- **完整性检查**: 验证必需的数据字段是否存在
- **一致性检查**: 检查时间序列的单调性
- **范围检查**: 验证数据值是否在合理范围内
- **自定义规则**: 支持用户定义的验证规则

## 报告格式

### JSON报告

```json
{
  "metadata": {
    "generated_at": "2024-01-15T10:30:00Z",
    "tool_version": "0.1.0",
    "format": "Json"
  },
  "summary": {
    "overall_status": "Pass",
    "speed_data_points_count": 16,
    "anomalies_count": 0,
    "validation_errors_count": 0,
    "validation_warnings_count": 1
  },
  "recommendations": [
    {
      "title": "数据质量建议",
      "description": "建议添加更多时间戳信息",
      "priority": "Medium"
    }
  ]
}
```

### HTML报告

生成包含图表和详细分析的HTML报告，便于在浏览器中查看。

### 文本报告

简洁的文本格式报告，适合命令行环境和日志记录。

## 开发

### 项目结构

```
xml-check/
├── src/
│   ├── main.rs          # CLI主程序
│   ├── lib.rs           # 核心库
│   ├── config.rs        # 配置管理
│   ├── parser.rs        # XML解析
│   ├── detector.rs      # 变速数据检测
│   ├── validator.rs     # 数据验证
│   ├── reporter.rs      # 报告生成
│   ├── error.rs         # 错误处理
│   └── gui/
│       └── main.rs      # GUI主程序
├── config/              # 配置文件
├── examples/            # 示例XML文件
├── tests/               # 测试文件
└── docs/                # 文档
```

### 运行测试

```bash
# 运行所有测试
cargo test

# 运行特定模块测试
cargo test parser

# 运行集成测试
cargo test --test integration
```

### 代码格式化

```bash
# 格式化代码
cargo fmt

# 检查代码质量
cargo clippy
```

## 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork本仓库
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 许可证

本项目采用MIT许可证。详见[LICENSE](LICENSE)文件。

## 支持

如果您遇到问题或有建议，请：

1. 查看[文档](docs/)
2. 搜索[已有问题](https://github.com/yourusername/xml-check/issues)
3. 创建[新问题](https://github.com/yourusername/xml-check/issues/new)

## 更新日志

### v0.1.0 (2024-01-15)

- 初始版本发布
- 基本的XML解析和变速数据检测功能
- 命令行和图形用户界面
- 多种报告格式支持
- 跨平台兼容性

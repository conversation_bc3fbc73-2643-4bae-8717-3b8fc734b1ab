use std::env;
use std::fs;
use std::path::Path;

fn main() {
    // 告诉Cargo在这些文件改变时重新运行构建脚本
    println!("cargo:rerun-if-changed=build.rs");
    println!("cargo:rerun-if-changed=config/");
    println!("cargo:rerun-if-changed=examples/");

    // 创建输出目录
    let out_dir = env::var("OUT_DIR").unwrap();
    let dest_path = Path::new(&out_dir);

    // 复制配置文件到构建输出目录
    if Path::new("config").exists() {
        copy_dir_all("config", dest_path.join("config")).unwrap();
    }

    // 复制示例文件到构建输出目录
    if Path::new("examples").exists() {
        copy_dir_all("examples", dest_path.join("examples")).unwrap();
    }

    // 设置版本信息
    println!("cargo:rustc-env=BUILD_TIME={}", chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC"));
    
    // 获取Git信息（如果可用）
    if let Ok(output) = std::process::Command::new("git")
        .args(&["rev-parse", "--short", "HEAD"])
        .output()
    {
        if output.status.success() {
            let git_hash = String::from_utf8_lossy(&output.stdout);
            println!("cargo:rustc-env=GIT_HASH={}", git_hash.trim());
        }
    }
}

fn copy_dir_all(src: impl AsRef<Path>, dst: impl AsRef<Path>) -> std::io::Result<()> {
    fs::create_dir_all(&dst)?;
    for entry in fs::read_dir(src)? {
        let entry = entry?;
        let ty = entry.file_type()?;
        if ty.is_dir() {
            copy_dir_all(entry.path(), dst.as_ref().join(entry.file_name()))?;
        } else {
            fs::copy(entry.path(), dst.as_ref().join(entry.file_name()))?;
        }
    }
    Ok(())
}

@echo off
echo Creating desktop shortcut for XML Speed Data Detection Tool...

set TOOL_PATH=%~dp0release\xml-check.exe
set DESKTOP=%USERPROFILE%\Desktop
set SHORTCUT_NAME=XML Speed Detector.lnk

REM Create VBS script to create shortcut
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%DESKTOP%\%SHORTCUT_NAME%" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "%TOOL_PATH%" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "%~dp0release" >> CreateShortcut.vbs
echo oLink.Description = "XML Speed Data Detection Tool - Drag XML files here" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs

REM Execute VBS script
cscript CreateShortcut.vbs

REM Clean up
del CreateShortcut.vbs

echo.
echo Desktop shortcut created successfully!
echo You can now drag XML files to the desktop shortcut.
echo.
pause

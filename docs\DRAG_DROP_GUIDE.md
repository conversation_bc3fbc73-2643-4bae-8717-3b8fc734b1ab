# 拖拽功能使用指南

XML变速数据检测工具支持多种拖拽方式，让您能够快速便捷地检测XML文件。

## 🎯 支持的拖拽方式

### 1. GUI窗口拖拽

**功能**：直接拖拽文件到GUI应用窗口

**支持的操作**：
- 单个XML文件拖拽
- 多个XML文件同时拖拽
- 文件夹拖拽（自动检测其中的XML文件）
- 混合拖拽（文件和文件夹）

**使用步骤**：
1. 启动GUI应用：`./xml-check-gui`
2. 从文件管理器中选择XML文件
3. 拖拽到GUI窗口中
4. 释放鼠标，自动开始检测
5. 查看检测结果

**视觉反馈**：
- 拖拽悬停时显示蓝色提示区域
- 显示"📁 拖拽XML文件到这里"提示
- 支持单个文件或多个文件的提示

### 2. 可执行文件拖拽

**功能**：拖拽XML文件到工具的可执行文件图标

**支持的操作**：
- 拖拽到 `xml-check.exe` (CLI版本)
- 拖拽到 `xml-check-gui.exe` (GUI版本)
- 支持单个文件拖拽

**使用步骤**：
1. 在文件管理器中找到XML文件
2. 拖拽到工具的可执行文件图标上
3. 释放鼠标
4. 工具自动启动并开始检测

### 3. 桌面快捷方式拖拽

**功能**：拖拽到桌面快捷方式图标

**前提条件**：需要先运行文件关联脚本创建快捷方式

**使用步骤**：
1. 运行文件关联脚本（见下文）
2. 拖拽XML文件到桌面快捷方式
3. 工具自动启动并检测

## 🔗 文件关联设置

### Windows系统

```cmd
# 以管理员身份运行
scripts\setup_file_association.bat
```

**设置后的功能**：
- 右键菜单：右键XML文件 → "使用XML检测工具分析"
- 拖拽支持：拖拽到工具图标
- 双击关联：双击XML文件时可选择工具打开

### Linux系统

```bash
# 运行设置脚本
chmod +x scripts/setup_file_association.sh
./scripts/setup_file_association.sh

# 如需系统级安装（可选）
sudo ./scripts/setup_file_association.sh
```

**设置后的功能**：
- 桌面集成：在应用程序菜单中显示
- 文件关联：XML文件默认关联到工具
- 右键菜单：右键XML文件选择工具

### macOS系统

```bash
# 需要管理员权限
sudo scripts/setup_file_association.sh
```

**设置后的功能**：
- 应用程序包：在Applications文件夹中创建.app包
- 文件关联：XML文件可选择工具打开
- 拖拽支持：拖拽到Dock中的工具图标

## 📁 支持的文件类型

### 直接支持
- `.xml` - 标准XML文件
- 任何以`.xml`结尾的文件

### 文件夹处理
- 拖拽文件夹时自动扫描其中的XML文件
- 支持递归扫描（可配置）
- 忽略非XML文件

### 批量处理
- 同时拖拽多个XML文件
- 自动生成批量处理报告
- 支持混合文件和文件夹拖拽

## 🎨 用户体验特性

### 视觉反馈
```
拖拽悬停时：
┌─────────────────────────────────┐
│                                 │
│     📁 拖拽XML文件到这里        │
│                                 │
│    支持单个文件或多个文件       │
│                                 │
└─────────────────────────────────┘
```

### 智能识别
- 自动过滤非XML文件
- 显示找到的XML文件数量
- 错误提示：如果没有找到XML文件

### 进度显示
- 批量处理时显示进度
- 实时更新处理状态
- 完成后自动显示结果

## 🚀 使用场景

### 场景1：单文件快速检测
```
操作：拖拽 sample.xml 到GUI窗口
结果：立即开始检测，显示结果
用时：< 1秒
```

### 场景2：批量文件检测
```
操作：选择多个XML文件，拖拽到GUI窗口
结果：批量处理，生成汇总报告
用时：根据文件数量和大小
```

### 场景3：文件夹检测
```
操作：拖拽包含XML文件的文件夹
结果：自动扫描并检测所有XML文件
用时：根据文件夹大小
```

### 场景4：右键快速检测
```
操作：右键XML文件 → 选择检测工具
结果：CLI模式快速检测，显示结果
用时：< 2秒
```

## ⚙️ 配置选项

### 拖拽行为配置

在配置文件中可以调整拖拽相关设置：

```toml
[gui]
# 拖拽时是否自动开始检测
auto_start_on_drop = true

# 是否支持文件夹递归扫描
recursive_folder_scan = true

# 批量处理时的最大文件数
max_batch_files = 100

[detector]
# 文件大小限制（影响拖拽文件的处理）
max_file_size = 104857600  # 100MB
```

## 🛠️ 故障排除

### 问题1：拖拽无响应
**可能原因**：
- GUI应用未正确启动
- 系统权限问题
- 文件被其他程序占用

**解决方案**：
```bash
# 重新启动GUI应用
./xml-check-gui

# 检查文件权限
ls -la your_file.xml

# 确保文件未被占用
lsof your_file.xml  # Linux/macOS
```

### 问题2：文件关联失效
**可能原因**：
- 关联脚本未正确执行
- 系统权限不足
- 工具路径发生变化

**解决方案**：
```bash
# 重新运行关联脚本
# Windows
scripts\setup_file_association.bat

# Linux/macOS
./scripts/setup_file_association.sh
```

### 问题3：拖拽文件夹无效果
**可能原因**：
- 文件夹中没有XML文件
- 文件夹权限问题
- 递归扫描被禁用

**解决方案**：
- 检查文件夹中是否包含.xml文件
- 确保文件夹可读权限
- 检查配置文件中的递归设置

## 📝 最佳实践

### 1. 文件组织
```
project/
├── data/
│   ├── test1.xml
│   ├── test2.xml
│   └── subfolder/
│       └── test3.xml
└── reports/
```

### 2. 批量处理工作流
1. 将所有XML文件放在同一文件夹
2. 拖拽整个文件夹到GUI
3. 设置输出路径到reports文件夹
4. 一次性生成所有报告

### 3. 快速验证工作流
1. 设置文件关联
2. 右键XML文件快速检测
3. 查看控制台输出
4. 必要时保存详细报告

## 🎯 高级技巧

### 技巧1：命令行与拖拽结合
```bash
# 先用拖拽快速检测
# 然后用命令行生成详细报告
./xml-check detected_file.xml --format html --output detailed_report.html
```

### 技巧2：批量处理优化
- 按文件大小分组处理
- 使用配置文件统一设置
- 设置合适的输出格式

### 技巧3：自动化工作流
- 创建桌面快捷方式
- 设置默认输出目录
- 配置自动保存报告

通过这些拖拽功能，您可以大大提高XML文件检测的效率和便利性！

# XML变速数据检测工具 - 项目总结

## 项目概述

这是一个基于Rust开发的跨平台XML变速数据检测工具，具备以下核心功能：

- **XML解析**: 支持多种XML格式和编码
- **变速数据检测**: 智能识别XML中的速度相关数据
- **数据验证**: 多层次的数据完整性和一致性检查
- **异常检测**: 自动识别数据异常和格式问题
- **报告生成**: 支持JSON、XML、HTML、文本、CSV等多种格式
- **双重界面**: 提供命令行(CLI)和图形用户界面(GUI)
- **跨平台**: 支持Windows、macOS和Linux

## 技术架构

### 核心技术栈

- **编程语言**: Rust 2021 Edition
- **XML处理**: roxmltree, quick-xml, serde-xml-rs
- **CLI框架**: clap 4.4
- **GUI框架**: egui + eframe
- **序列化**: serde + serde_json + toml
- **错误处理**: thiserror + anyhow
- **异步支持**: tokio
- **测试**: 内置测试框架 + tempfile

### 模块架构

```
xml-check/
├── src/
│   ├── lib.rs           # 核心库入口，提供XmlChecker主接口
│   ├── main.rs          # CLI应用入口
│   ├── config.rs        # 配置管理模块
│   ├── error.rs         # 统一错误处理
│   ├── parser.rs        # XML解析模块
│   ├── detector.rs      # 变速数据检测模块
│   ├── validator.rs     # 数据验证模块
│   ├── reporter.rs      # 报告生成模块
│   └── gui/
│       └── main.rs      # GUI应用入口
├── config/              # 配置文件
├── examples/            # 示例XML文件
├── tests/               # 集成测试
├── scripts/             # 构建脚本
└── docs/                # 文档
```

## 核心功能模块

### 1. XML解析器 (parser.rs)

**功能**:
- 解析XML文档并转换为内部数据结构
- 支持多种编码格式
- 提供节点查找和遍历功能
- 文件大小限制和安全检查

**关键结构**:
- `XmlParser`: 主解析器类
- `ParsedXml`: 解析后的XML文档
- `XmlNode`: XML节点表示
- `XmlMetadata`: 文档元数据

### 2. 变速数据检测器 (detector.rs)

**功能**:
- 自动识别XML中的速度数据模式
- 支持多种属性名称(speed, velocity, rate等)
- 计算速度统计信息
- 检测速度变化和异常

**关键结构**:
- `VariableSpeedDetector`: 主检测器类
- `DetectionResults`: 检测结果
- `SpeedDataPoint`: 速度数据点
- `SpeedAnalysis`: 速度分析结果

### 3. 数据验证器 (validator.rs)

**功能**:
- 数据完整性检查
- 数据范围验证
- 数据一致性检查
- 自定义验证规则支持

**关键结构**:
- `DataValidator`: 主验证器类
- `ValidationResults`: 验证结果
- `ValidationError`: 验证错误
- `ValidationRule`: 自定义规则

### 4. 报告生成器 (reporter.rs)

**功能**:
- 生成多种格式的检测报告
- 提供详细的统计信息
- 生成改进建议
- 支持自定义报告模板

**关键结构**:
- `ReportGenerator`: 主报告生成器
- `Report`: 检测报告
- `ReportSummary`: 报告摘要
- `Recommendation`: 改进建议

### 5. 配置管理 (config.rs)

**功能**:
- TOML格式配置文件支持
- 模块化配置结构
- 配置验证和默认值
- 运行时配置更新

**关键结构**:
- `Config`: 主配置结构
- `ParserConfig`: 解析器配置
- `DetectorConfig`: 检测器配置
- `ValidatorConfig`: 验证器配置

## 用户界面

### 命令行界面 (CLI)

**特性**:
- 丰富的命令行参数支持
- 彩色输出和进度显示
- 批量文件处理
- 多种输出格式

**主要命令**:
```bash
xml-check <input> [OPTIONS]
  -c, --config <FILE>     配置文件路径
  -o, --output <FILE>     输出报告文件
  -f, --format <FORMAT>   输出格式
  -r, --recursive         递归处理目录
  -v, --verbose           详细输出
  -q, --quiet             静默模式
```

### 图形用户界面 (GUI)

**特性**:
- 基于egui的现代GUI
- 文件/目录选择对话框
- 实时进度显示
- 结果可视化
- 配置管理界面

## 配置系统

### 默认配置 (config/default.toml)

```toml
[parser]
ignore_namespaces = false
preserve_whitespace = false
max_file_size = 104857600  # 100MB
supported_encodings = ["UTF-8", "UTF-16", "ISO-8859-1"]

[detector]
speed_data_patterns = ["//data[@speed]", "//velocity", "//*[@speed]"]
speed_attributes = ["speed", "velocity", "rate"]
time_attributes = ["time", "timestamp", "t"]
min_speed = 0.0
max_speed = 1000.0
speed_change_threshold = 50.0

[validator]
enable_integrity_check = true
enable_range_check = true
enable_consistency_check = true

[reporter]
output_format = "Json"
include_details = true
include_statistics = true
language = "zh-CN"
```

## 示例数据

### 正常数据示例 (examples/sample_speed_data.xml)
包含标准的车辆速度测试数据，展示正常的加速和减速过程。

### 异常数据示例 (examples/anomaly_data.xml)
包含各种类型的数据异常，用于测试检测和验证功能：
- 速度突变
- 超出范围的值
- 缺失数据
- 格式错误
- 时间序列错误

## 构建和部署

### 构建要求
- Rust 1.70+
- Cargo包管理器

### 构建脚本
- `scripts/build.sh` - Linux/macOS构建脚本
- `scripts/build.bat` - Windows构建脚本

### 构建步骤
1. 安装Rust: https://rustup.rs/
2. 克隆项目
3. 运行构建脚本
4. 在`release/`目录中找到可执行文件

## 测试

### 单元测试
每个模块都包含完整的单元测试，覆盖核心功能和边界情况。

### 集成测试
`tests/integration_test.rs`包含端到端的集成测试：
- 基本XML检测流程
- 文件处理
- 异常检测
- 验证错误处理
- 批量处理
- 报告序列化

### 运行测试
```bash
cargo test                    # 运行所有测试
cargo test parser            # 运行特定模块测试
cargo test --test integration # 运行集成测试
```

## 性能特性

- **内存效率**: 流式XML解析，避免加载整个文档到内存
- **处理速度**: 基于Rust的高性能实现
- **文件大小限制**: 可配置的文件大小限制防止内存溢出
- **并发支持**: 支持批量文件的并发处理

## 扩展性

### 自定义验证规则
支持通过配置文件添加自定义验证规则：
```toml
[[validator.custom_rules]]
name = "custom_rule"
description = "自定义规则描述"
xpath = "//custom[@attr]"
expected_type = "Float"
required = true
```

### 插件架构
模块化设计支持未来添加新的：
- 数据检测算法
- 验证规则
- 报告格式
- 数据源支持

## 安全考虑

- **输入验证**: 严格的XML解析和数据验证
- **文件大小限制**: 防止DoS攻击
- **内存安全**: Rust的内存安全保证
- **错误处理**: 完善的错误处理避免崩溃

## 未来发展方向

1. **Web界面**: 基于Web的用户界面
2. **API服务**: RESTful API支持
3. **数据库集成**: 支持数据库存储和查询
4. **机器学习**: 智能异常检测算法
5. **实时处理**: 流式数据处理支持
6. **云部署**: 容器化和云原生支持

## 许可证

MIT License - 详见LICENSE文件

## 贡献指南

欢迎贡献代码！请遵循：
1. Fork项目
2. 创建特性分支
3. 编写测试
4. 提交Pull Request

项目遵循Rust社区的最佳实践和代码规范。

# 快速开始指南

## 环境准备

### 1. 安装Rust

访问 [https://rustup.rs/](https://rustup.rs/) 并按照说明安装Rust。

**Windows:**
```cmd
# 下载并运行rustup-init.exe
# 或使用Chocolatey
choco install rust
```

**macOS:**
```bash
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
```

**Linux:**
```bash
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env
```

### 2. 验证安装

```bash
rustc --version
cargo --version
```

## 构建项目

### 方法一：使用构建脚本（推荐）

**Linux/macOS:**
```bash
chmod +x scripts/build.sh
./scripts/build.sh
```

**Windows:**
```cmd
scripts\build.bat
```

### 方法二：手动构建

```bash
# 克隆项目（如果从Git仓库）
git clone <repository-url>
cd xml-check

# 运行测试
cargo test

# 构建发布版本
cargo build --release

# 构建CLI版本
cargo build --release --bin xml-check

# 构建GUI版本
cargo build --release --bin xml-check-gui
```

## 快速测试

### 1. 测试CLI版本

```bash
# 使用示例文件测试
./target/release/xml-check examples/sample_speed_data.xml

# 或者使用发布目录中的文件
./release/xml-check examples/sample_speed_data.xml
```

### 2. 测试GUI版本

```bash
# 启动GUI应用
./target/release/xml-check-gui

# 或者
./release/xml-check-gui
```

## 基本使用

### CLI命令示例

```bash
# 检测单个XML文件
./xml-check examples/sample_speed_data.xml

# 检测包含异常的文件
./xml-check examples/anomaly_data.xml

# 输出JSON格式报告到文件
./xml-check examples/sample_speed_data.xml --format json --output report.json

# 递归检测目录中的所有XML文件
./xml-check examples/ --recursive

# 使用自定义配置
./xml-check examples/sample_speed_data.xml --config config/default.toml

# 显示详细输出
./xml-check examples/sample_speed_data.xml --verbose

# 静默模式（只显示错误）
./xml-check examples/sample_speed_data.xml --quiet

# 查看帮助
./xml-check --help
```

### GUI使用步骤

**传统方式**：
1. 启动GUI应用：`./xml-check-gui`
2. 点击"浏览..."选择XML文件或"选择目录"选择包含XML文件的目录
3. 选择输出格式（JSON、XML、HTML、文本、CSV）
4. 可选：设置输出文件路径
5. 点击"开始检测"
6. 查看结果摘要
7. 可选：点击"保存报告"保存详细报告

**拖拽方式**：
1. 启动GUI应用：`./xml-check-gui`
2. 直接拖拽XML文件到窗口中
3. 支持单个文件或多个文件同时拖拽
4. 支持拖拽文件夹（自动检测其中的XML文件）
5. 自动开始检测并显示结果

**文件关联方式**：
1. 运行文件关联脚本：
   - Windows: `scripts\setup_file_association.bat`
   - Linux/macOS: `scripts/setup_file_association.sh`
2. 右键点击XML文件，选择"使用XML检测工具分析"
3. 或双击XML文件直接打开工具
4. 或拖拽XML文件到工具图标

## 示例输出

### 成功检测示例

```
XML变速数据检测工具
==================================================
📄 检测文件: examples/sample_speed_data.xml
✅ 检测完成，共处理 1 个文件

检测结果摘要
------------------------------
✅ 通过: 1 个文件

数据统计
------------------------------
📊 总数据点: 16
⚡ 异常数量: 0
❗ 验证错误: 0
⚠️  验证警告: 0
```

### 异常检测示例

```
XML变速数据检测工具
==================================================
📄 检测文件: examples/anomaly_data.xml
✅ 检测完成，共处理 1 个文件

检测结果摘要
------------------------------
❌ 失败: 1 个文件

数据统计
------------------------------
📊 总数据点: 14
⚡ 异常数量: 5
❗ 验证错误: 2
⚠️  验证警告: 3
```

### JSON报告示例

```json
{
  "metadata": {
    "generated_at": "2024-01-15T10:30:00Z",
    "tool_version": "0.1.0",
    "format": "Json",
    "language": "zh-CN"
  },
  "file_info": {
    "file_path": "examples/sample_speed_data.xml",
    "file_size": 1024,
    "xml_version": "1.0",
    "xml_encoding": "UTF-8"
  },
  "summary": {
    "overall_status": "Pass",
    "speed_data_points_count": 16,
    "anomalies_count": 0,
    "validation_errors_count": 0,
    "validation_warnings_count": 0,
    "processing_time_ms": 15
  },
  "recommendations": []
}
```

## 配置自定义

### 创建自定义配置文件

```bash
# 复制默认配置
cp config/default.toml my_config.toml

# 编辑配置文件
nano my_config.toml
```

### 配置示例修改

```toml
[detector]
# 调整速度范围
min_speed = 5.0
max_speed = 200.0

# 调整变化阈值
speed_change_threshold = 30.0

# 添加自定义速度属性
speed_attributes = ["speed", "velocity", "rate", "v"]

[reporter]
# 更改默认输出格式
output_format = "Html"

# 设置语言
language = "en-US"
```

### 使用自定义配置

```bash
./xml-check examples/sample_speed_data.xml --config my_config.toml
```

## 常见问题

### Q: 如何处理大型XML文件？

A: 工具默认支持最大100MB的文件。如需处理更大文件，可在配置中调整：

```toml
[parser]
max_file_size = 209715200  # 200MB
```

### Q: 如何添加新的速度属性名称？

A: 在配置文件中添加：

```toml
[detector]
speed_attributes = ["speed", "velocity", "rate", "your_custom_attr"]
```

### Q: 如何自定义验证规则？

A: 在配置文件中添加：

```toml
[[validator.custom_rules]]
name = "my_rule"
description = "我的自定义规则"
xpath = "//my_element[@my_attr]"
expected_type = "Float"
required = true
```

### Q: 构建失败怎么办？

A: 常见解决方案：
1. 确保Rust版本 >= 1.70
2. 运行 `cargo clean` 清理缓存
3. 检查网络连接（下载依赖）
4. 查看具体错误信息

### Q: GUI无法启动？

A: 可能的原因：
1. 缺少图形环境（Linux服务器）
2. 缺少系统依赖库
3. 权限问题

Linux解决方案：
```bash
# Ubuntu/Debian
sudo apt-get install libgtk-3-dev libxcb-render0-dev libxcb-shape0-dev libxcb-xfixes0-dev

# CentOS/RHEL
sudo yum install gtk3-devel
```

## 下一步

- 阅读完整的[README.md](../README.md)
- 查看[项目总结](PROJECT_SUMMARY.md)了解技术细节
- 尝试修改配置文件以适应您的需求
- 使用自己的XML文件进行测试
- 查看源代码了解实现细节

## 获取帮助

如果遇到问题：
1. 查看命令行帮助：`./xml-check --help`
2. 检查配置文件格式
3. 查看示例文件格式
4. 提交Issue到项目仓库

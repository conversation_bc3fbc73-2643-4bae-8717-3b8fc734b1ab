# XML变速数据检测工具默认配置文件

[parser]
# 是否忽略XML命名空间
ignore_namespaces = false
# 是否保留空白字符
preserve_whitespace = false
# 最大文件大小（字节）- 100MB
max_file_size = 104857600
# 支持的编码格式
supported_encodings = ["UTF-8", "UTF-16", "ISO-8859-1"]

[detector]
# 变速数据的XML路径模式（简化的XPath）
speed_data_patterns = [
    "//data[@speed]",
    "//speed",
    "//velocity", 
    "//*[@speed]",
    "//record[@speed]",
    "//measurement[@velocity]",
    "//point[@rate]"
]

# 速度属性名称
speed_attributes = ["speed", "velocity", "rate"]

# 时间属性名称  
time_attributes = ["time", "timestamp", "t"]

# 最小速度值（km/h或其他单位）
min_speed = 0.0

# 最大速度值（km/h或其他单位）
max_speed = 1000.0

# 速度变化阈值（百分比）- 超过此值认为是显著变化
speed_change_threshold = 50.0

[validator]
# 是否启用数据完整性检查
enable_integrity_check = true

# 是否启用数据范围检查
enable_range_check = true

# 是否启用数据一致性检查
enable_consistency_check = true

# 自定义验证规则
[[validator.custom_rules]]
name = "speed_data_required"
description = "速度数据必须存在"
xpath = "//data[@speed]"
expected_type = "Float"
required = true

[[validator.custom_rules]]
name = "time_data_recommended"
description = "建议包含时间信息"
xpath = "//data[@time]"
expected_type = "Float"
required = false

[reporter]
# 输出格式：Json, Xml, Html, Text, Csv
output_format = "Json"

# 是否包含详细信息
include_details = true

# 是否包含统计信息
include_statistics = true

# 报告语言
language = "zh-CN"

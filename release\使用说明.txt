XML变速数据检测工具 - 使用说明
====================================

🎉 恭喜！工具已成功安装并可以使用！

📁 文件位置：
- 主程序：xml-check.exe
- 示例文件：examples/ 文件夹
- 配置文件：config/ 文件夹

🚀 使用方法：

方法1：命令行使用
  在命令提示符中运行：
  xml-check.exe examples\sample_speed_data.xml

方法2：拖拽使用（推荐）
  直接拖拽XML文件到 xml-check.exe 图标上

方法3：右键菜单（如果已设置文件关联）
  右键点击XML文件 → 选择"Analyze with XML Speed Detector"

📊 功能特性：
- ✅ 自动检测XML中的速度数据（speed、velocity、rate属性）
- ✅ 统计分析：最小值、最大值、平均值
- ✅ 异常检测：负值、超范围值、突变点
- ✅ 数据质量评估：优秀/良好/一般/较差
- ✅ 支持时间序列数据检测

📝 示例文件说明：
- sample_speed_data.xml：正常的车辆速度测试数据
- anomaly_data.xml：包含各种异常的测试数据

🔧 如需设置文件关联：
  运行 scripts\setup_simple.bat（可能需要管理员权限）

💡 提示：
- 支持拖拽多种格式的XML文件
- 检测结果会显示详细的统计信息
- 发现异常时会给出具体的异常类型和数量
- 按任意键可退出程序

🆘 如遇问题：
1. 确保XML文件格式正确
2. 检查文件是否包含speed、velocity或rate属性
3. 确保文件路径中没有中文字符（如有问题）

版本：v0.1.0
开发：XML Speed Data Detection Tool

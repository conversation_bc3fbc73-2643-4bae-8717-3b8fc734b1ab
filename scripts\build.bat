@echo off
REM XML变速数据检测工具Windows构建脚本

echo 🚀 开始构建XML变速数据检测工具...

REM 检查Rust环境
where cargo >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 错误: 未找到Cargo。请先安装Rust: https://rustup.rs/
    exit /b 1
)

REM 清理之前的构建
echo 🧹 清理之前的构建...
cargo clean

REM 运行测试
echo 🧪 运行测试...
cargo test
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 测试失败
    exit /b 1
)

REM 检查代码质量
echo 🔍 检查代码质量...
cargo clippy -- -D warnings
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 代码质量检查失败
    exit /b 1
)

REM 构建发布版本
echo 🔨 构建发布版本...

REM 构建CLI版本
echo   📦 构建CLI版本...
cargo build --release --bin xml-check
if %ERRORLEVEL% NEQ 0 (
    echo ❌ CLI构建失败
    exit /b 1
)

REM 构建GUI版本
echo   🖥️ 构建GUI版本...
cargo build --release --bin xml-check-gui
if %ERRORLEVEL% NEQ 0 (
    echo ❌ GUI构建失败
    exit /b 1
)

REM 创建发布目录
set RELEASE_DIR=release
if exist %RELEASE_DIR% rmdir /s /q %RELEASE_DIR%
mkdir %RELEASE_DIR%

REM 复制可执行文件
echo 📋 复制文件到发布目录...
copy target\release\xml-check.exe %RELEASE_DIR%\
copy target\release\xml-check-gui.exe %RELEASE_DIR%\

REM 复制配置和示例文件
xcopy config %RELEASE_DIR%\config\ /e /i
xcopy examples %RELEASE_DIR%\examples\ /e /i
copy README.md %RELEASE_DIR%\
copy LICENSE %RELEASE_DIR%\

REM 创建使用说明
echo XML变速数据检测工具使用说明 > %RELEASE_DIR%\USAGE.txt
echo ============================ >> %RELEASE_DIR%\USAGE.txt
echo. >> %RELEASE_DIR%\USAGE.txt
echo 命令行版本 (xml-check.exe): >> %RELEASE_DIR%\USAGE.txt
echo   xml-check.exe examples\sample_speed_data.xml >> %RELEASE_DIR%\USAGE.txt
echo   xml-check.exe examples\ --recursive --format json --output report.json >> %RELEASE_DIR%\USAGE.txt
echo. >> %RELEASE_DIR%\USAGE.txt
echo 图形界面版本 (xml-check-gui.exe): >> %RELEASE_DIR%\USAGE.txt
echo   xml-check-gui.exe >> %RELEASE_DIR%\USAGE.txt
echo. >> %RELEASE_DIR%\USAGE.txt
echo 配置文件: >> %RELEASE_DIR%\USAGE.txt
echo   config\default.toml - 默认配置 >> %RELEASE_DIR%\USAGE.txt
echo. >> %RELEASE_DIR%\USAGE.txt
echo 示例文件: >> %RELEASE_DIR%\USAGE.txt
echo   examples\sample_speed_data.xml - 正常的速度数据示例 >> %RELEASE_DIR%\USAGE.txt
echo   examples\anomaly_data.xml - 包含异常的测试数据 >> %RELEASE_DIR%\USAGE.txt
echo. >> %RELEASE_DIR%\USAGE.txt
echo 更多信息请查看 README.md >> %RELEASE_DIR%\USAGE.txt

echo ✅ 构建完成!
echo.
echo 📁 发布文件位于: %RELEASE_DIR%\
echo.
echo 🎉 可以开始使用了!
echo   命令行: %RELEASE_DIR%\xml-check.exe --help
echo   图形界面: %RELEASE_DIR%\xml-check-gui.exe
echo.
echo 💡 提示：
echo   - 运行 'scripts\setup_file_association.bat' 设置文件关联
echo   - 设置后可直接拖拽XML文件到工具进行检测
echo   - 支持右键菜单快速检测XML文件

pause

#!/bin/bash

# XML变速数据检测工具构建脚本

set -e

echo "🚀 开始构建XML变速数据检测工具..."

# 检查Rust环境
if ! command -v cargo &> /dev/null; then
    echo "❌ 错误: 未找到Cargo。请先安装Rust: https://rustup.rs/"
    exit 1
fi

# 清理之前的构建
echo "🧹 清理之前的构建..."
cargo clean

# 运行测试
echo "🧪 运行测试..."
cargo test

# 检查代码质量
echo "🔍 检查代码质量..."
cargo clippy -- -D warnings

# 格式化代码
echo "📝 格式化代码..."
cargo fmt --check

# 构建发布版本
echo "🔨 构建发布版本..."

# 构建CLI版本
echo "  📦 构建CLI版本..."
cargo build --release --bin xml-check

# 构建GUI版本
echo "  🖥️ 构建GUI版本..."
cargo build --release --bin xml-check-gui

# 创建发布目录
RELEASE_DIR="release"
rm -rf $RELEASE_DIR
mkdir -p $RELEASE_DIR

# 复制可执行文件
echo "📋 复制文件到发布目录..."
cp target/release/xml-check $RELEASE_DIR/
cp target/release/xml-check-gui $RELEASE_DIR/

# 复制配置和示例文件
cp -r config $RELEASE_DIR/
cp -r examples $RELEASE_DIR/
cp README.md $RELEASE_DIR/
cp LICENSE $RELEASE_DIR/

# 创建使用说明
cat > $RELEASE_DIR/USAGE.txt << EOF
XML变速数据检测工具使用说明
============================

命令行版本 (xml-check):
  ./xml-check examples/sample_speed_data.xml
  ./xml-check examples/ --recursive --format json --output report.json

图形界面版本 (xml-check-gui):
  ./xml-check-gui

配置文件:
  config/default.toml - 默认配置
  
示例文件:
  examples/sample_speed_data.xml - 正常的速度数据示例
  examples/anomaly_data.xml - 包含异常的测试数据

更多信息请查看 README.md
EOF

# 显示构建信息
echo "✅ 构建完成!"
echo ""
echo "📊 构建统计:"
echo "  CLI可执行文件: $(ls -lh target/release/xml-check | awk '{print $5}')"
echo "  GUI可执行文件: $(ls -lh target/release/xml-check-gui | awk '{print $5}')"
echo ""
echo "📁 发布文件位于: $RELEASE_DIR/"
echo ""
echo "🎉 可以开始使用了!"
echo "  命令行: ./$RELEASE_DIR/xml-check --help"
echo "  图形界面: ./$RELEASE_DIR/xml-check-gui"

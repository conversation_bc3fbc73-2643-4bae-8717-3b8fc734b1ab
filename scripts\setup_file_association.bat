@echo off
REM XML变速数据检测工具 - Windows文件关联设置脚本
REM 运行此脚本将设置XML文件与工具的关联，支持右键菜单和拖拽功能

echo 设置XML文件关联...

REM 获取当前目录
set CURRENT_DIR=%~dp0
set TOOL_PATH=%CURRENT_DIR%..\release\xml-check.exe
set GUI_TOOL_PATH=%CURRENT_DIR%..\release\xml-check-gui.exe

REM 检查工具是否存在
if not exist "%TOOL_PATH%" (
    echo 错误: 找不到 xml-check.exe
    echo 请先运行 build.bat 编译工具
    pause
    exit /b 1
)

if not exist "%GUI_TOOL_PATH%" (
    echo 错误: 找不到 xml-check-gui.exe  
    echo 请先运行 build.bat 编译工具
    pause
    exit /b 1
)

echo 正在设置注册表项...

REM 创建文件类型关联
reg add "HKEY_CLASSES_ROOT\.xml\shell\XMLCheck" /ve /d "使用XML检测工具分析" /f
reg add "HKEY_CLASSES_ROOT\.xml\shell\XMLCheck\command" /ve /d "\"%TOOL_PATH%\" \"%%1\"" /f

reg add "HKEY_CLASSES_ROOT\.xml\shell\XMLCheckGUI" /ve /d "使用XML检测工具分析(GUI)" /f
reg add "HKEY_CLASSES_ROOT\.xml\shell\XMLCheckGUI\command" /ve /d "\"%GUI_TOOL_PATH%\" \"%%1\"" /f

REM 设置默认图标
reg add "HKEY_CLASSES_ROOT\.xml\shell\XMLCheck" /v "Icon" /d "%TOOL_PATH%,0" /f
reg add "HKEY_CLASSES_ROOT\.xml\shell\XMLCheckGUI" /v "Icon" /d "%GUI_TOOL_PATH%,0" /f

REM 创建拖拽支持
reg add "HKEY_CLASSES_ROOT\*\shell\XMLCheck" /ve /d "XML变速数据检测" /f
reg add "HKEY_CLASSES_ROOT\*\shell\XMLCheck\command" /ve /d "\"%TOOL_PATH%\" \"%%1\"" /f

echo.
echo ✅ 文件关联设置完成！
echo.
echo 现在您可以：
echo 1. 右键点击XML文件，选择"使用XML检测工具分析"
echo 2. 直接拖拽XML文件到 xml-check.exe 或 xml-check-gui.exe
echo 3. 双击XML文件时会显示关联选项
echo.
echo 注意：某些操作可能需要管理员权限
echo.
pause

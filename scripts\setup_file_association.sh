#!/bin/bash

# XML变速数据检测工具 - Linux/macOS文件关联设置脚本

set -e

echo "🔧 设置XML文件关联..."

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TOOL_PATH="$SCRIPT_DIR/../release/xml-check"
GUI_TOOL_PATH="$SCRIPT_DIR/../release/xml-check-gui"

# 检查工具是否存在
if [ ! -f "$TOOL_PATH" ]; then
    echo "❌ 错误: 找不到 xml-check"
    echo "请先运行 build.sh 编译工具"
    exit 1
fi

if [ ! -f "$GUI_TOOL_PATH" ]; then
    echo "❌ 错误: 找不到 xml-check-gui"
    echo "请先运行 build.sh 编译工具"
    exit 1
fi

# 确保工具可执行
chmod +x "$TOOL_PATH"
chmod +x "$GUI_TOOL_PATH"

# 检测操作系统
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "🐧 检测到Linux系统，设置桌面文件..."
    
    # 创建桌面应用文件
    DESKTOP_DIR="$HOME/.local/share/applications"
    mkdir -p "$DESKTOP_DIR"
    
    # CLI版本桌面文件
    cat > "$DESKTOP_DIR/xml-check.desktop" << EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=XML变速数据检测工具
Comment=检测和验证XML文件中的变速数据
Exec=$TOOL_PATH %f
Icon=text-xml
Terminal=true
MimeType=text/xml;application/xml;
Categories=Development;Utility;
EOF

    # GUI版本桌面文件
    cat > "$DESKTOP_DIR/xml-check-gui.desktop" << EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=XML变速数据检测工具(GUI)
Comment=检测和验证XML文件中的变速数据(图形界面)
Exec=$GUI_TOOL_PATH %f
Icon=text-xml
Terminal=false
MimeType=text/xml;application/xml;
Categories=Development;Utility;
EOF

    # 更新MIME数据库
    if command -v update-desktop-database &> /dev/null; then
        update-desktop-database "$DESKTOP_DIR"
    fi
    
    # 设置默认应用程序
    if command -v xdg-mime &> /dev/null; then
        xdg-mime default xml-check-gui.desktop text/xml
        xdg-mime default xml-check-gui.desktop application/xml
    fi
    
    echo "✅ Linux桌面集成设置完成"
    
elif [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🍎 检测到macOS系统，设置应用程序关联..."
    
    # 创建应用程序包目录
    APP_DIR="/Applications/XMLCheck.app"
    CONTENTS_DIR="$APP_DIR/Contents"
    MACOS_DIR="$CONTENTS_DIR/MacOS"
    RESOURCES_DIR="$CONTENTS_DIR/Resources"
    
    # 需要管理员权限
    if [ "$EUID" -ne 0 ]; then
        echo "⚠️  macOS文件关联需要管理员权限"
        echo "请使用: sudo $0"
        exit 1
    fi
    
    # 创建目录结构
    mkdir -p "$MACOS_DIR"
    mkdir -p "$RESOURCES_DIR"
    
    # 复制可执行文件
    cp "$GUI_TOOL_PATH" "$MACOS_DIR/XMLCheck"
    chmod +x "$MACOS_DIR/XMLCheck"
    
    # 创建Info.plist
    cat > "$CONTENTS_DIR/Info.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>XMLCheck</string>
    <key>CFBundleIdentifier</key>
    <string>com.xmlcheck.app</string>
    <key>CFBundleName</key>
    <string>XML变速数据检测工具</string>
    <key>CFBundleVersion</key>
    <string>1.0</string>
    <key>CFBundleDocumentTypes</key>
    <array>
        <dict>
            <key>CFBundleTypeExtensions</key>
            <array>
                <string>xml</string>
            </array>
            <key>CFBundleTypeName</key>
            <string>XML Document</string>
            <key>CFBundleTypeRole</key>
            <string>Viewer</string>
        </dict>
    </array>
</dict>
</plist>
EOF
    
    echo "✅ macOS应用程序包创建完成"
    
else
    echo "⚠️  未知操作系统，仅设置基本可执行权限"
fi

# 创建符号链接到系统路径（可选）
echo "🔗 创建系统路径符号链接..."

# 检查是否有写入权限
if [ -w "/usr/local/bin" ]; then
    ln -sf "$TOOL_PATH" "/usr/local/bin/xml-check"
    ln -sf "$GUI_TOOL_PATH" "/usr/local/bin/xml-check-gui"
    echo "✅ 系统路径链接创建完成"
    echo "现在可以在任何地方使用 'xml-check' 和 'xml-check-gui' 命令"
else
    echo "⚠️  无法创建系统路径链接（权限不足）"
    echo "如需全局访问，请运行: sudo $0"
fi

echo ""
echo "🎉 文件关联设置完成！"
echo ""
echo "现在您可以："
echo "1. 右键点击XML文件选择相应的应用程序"
echo "2. 拖拽XML文件到工具图标"
echo "3. 在终端中使用 xml-check 和 xml-check-gui 命令"
echo ""
echo "测试命令："
echo "  xml-check examples/sample_speed_data.xml"
echo "  xml-check-gui"
echo ""

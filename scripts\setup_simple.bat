@echo off
REM XML Speed Data Detection Tool - Simple File Association Setup

echo Setting up XML file association...

REM Get current directory
set CURRENT_DIR=%~dp0
set TOOL_PATH=%CURRENT_DIR%..\release\xml-check.exe

REM Check if tool exists
if not exist "%TOOL_PATH%" (
    echo Error: xml-check.exe not found
    echo Please run build.bat first
    pause
    exit /b 1
)

echo Tool found at: %TOOL_PATH%

REM Create registry entries for XML file association
echo Adding registry entries...

reg add "HKEY_CLASSES_ROOT\.xml\shell\XMLCheck" /ve /d "Analyze with XML Speed Detector" /f
reg add "HKEY_CLASSES_ROOT\.xml\shell\XMLCheck\command" /ve /d "\"%TOOL_PATH%\" \"%%1\"" /f

echo.
echo File association setup complete!
echo.
echo You can now:
echo 1. Right-click XML files and select "Analyze with XML Speed Detector"
echo 2. Drag XML files to xml-check.exe
echo.
echo Note: Some operations may require administrator privileges
echo.
pause

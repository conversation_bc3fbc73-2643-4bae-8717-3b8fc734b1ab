//! 配置管理模块
//! 
//! 管理XML检测工具的所有配置选项

use serde::{Deserialize, Serialize};
use std::path::Path;
use crate::error::{Result, XmlCheckError};

/// 主配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    /// XML解析器配置
    pub parser: ParserConfig,
    /// 变速数据检测器配置
    pub detector: DetectorConfig,
    /// 数据验证器配置
    pub validator: ValidatorConfig,
    /// 报告生成器配置
    pub reporter: ReporterConfig,
}

/// XML解析器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParserConfig {
    /// 是否忽略命名空间
    pub ignore_namespaces: bool,
    /// 是否保留空白字符
    pub preserve_whitespace: bool,
    /// 最大文件大小（字节）
    pub max_file_size: usize,
    /// 支持的编码格式
    pub supported_encodings: Vec<String>,
}

/// 变速数据检测器配置
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct DetectorConfig {
    /// 变速数据的XML路径模式
    pub speed_data_patterns: Vec<String>,
    /// 速度属性名称
    pub speed_attributes: Vec<String>,
    /// 时间属性名称
    pub time_attributes: Vec<String>,
    /// 最小速度值
    pub min_speed: f64,
    /// 最大速度值
    pub max_speed: f64,
    /// 速度变化阈值（百分比）
    pub speed_change_threshold: f64,
}

/// 数据验证器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidatorConfig {
    /// 是否启用数据完整性检查
    pub enable_integrity_check: bool,
    /// 是否启用数据范围检查
    pub enable_range_check: bool,
    /// 是否启用数据一致性检查
    pub enable_consistency_check: bool,
    /// 自定义验证规则
    pub custom_rules: Vec<ValidationRule>,
}

/// 验证规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationRule {
    /// 规则名称
    pub name: String,
    /// 规则描述
    pub description: String,
    /// XPath表达式
    pub xpath: String,
    /// 期望的数据类型
    pub expected_type: DataType,
    /// 是否必需
    pub required: bool,
}

/// 数据类型枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DataType {
    String,
    Integer,
    Float,
    Boolean,
    DateTime,
}

/// 报告生成器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReporterConfig {
    /// 输出格式
    pub output_format: OutputFormat,
    /// 是否包含详细信息
    pub include_details: bool,
    /// 是否包含统计信息
    pub include_statistics: bool,
    /// 报告语言
    pub language: String,
}

/// 输出格式枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum OutputFormat {
    Json,
    Xml,
    Html,
    Text,
    Csv,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            parser: ParserConfig::default(),
            detector: DetectorConfig::default(),
            validator: ValidatorConfig::default(),
            reporter: ReporterConfig::default(),
        }
    }
}

impl Default for ParserConfig {
    fn default() -> Self {
        Self {
            ignore_namespaces: false,
            preserve_whitespace: false,
            max_file_size: 100 * 1024 * 1024, // 100MB
            supported_encodings: vec![
                "UTF-8".to_string(),
                "UTF-16".to_string(),
                "ISO-8859-1".to_string(),
            ],
        }
    }
}

impl Default for DetectorConfig {
    fn default() -> Self {
        Self {
            speed_data_patterns: vec![
                "//data[@speed]".to_string(),
                "//speed".to_string(),
                "//velocity".to_string(),
                "//*[@speed]".to_string(),
            ],
            speed_attributes: vec![
                "speed".to_string(),
                "velocity".to_string(),
                "rate".to_string(),
            ],
            time_attributes: vec![
                "time".to_string(),
                "timestamp".to_string(),
                "t".to_string(),
            ],
            min_speed: 0.0,
            max_speed: 1000.0,
            speed_change_threshold: 50.0, // 50%变化阈值
        }
    }
}

impl Default for ValidatorConfig {
    fn default() -> Self {
        Self {
            enable_integrity_check: true,
            enable_range_check: true,
            enable_consistency_check: true,
            custom_rules: vec![],
        }
    }
}

impl Default for ReporterConfig {
    fn default() -> Self {
        Self {
            output_format: OutputFormat::Json,
            include_details: true,
            include_statistics: true,
            language: "zh-CN".to_string(),
        }
    }
}

impl Config {
    /// 从文件加载配置
    pub fn from_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        let content = std::fs::read_to_string(path)?;
        let config: Config = toml::from_str(&content)?;
        Ok(config)
    }

    /// 保存配置到文件
    pub fn save_to_file<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        let content = toml::to_string_pretty(self)
            .map_err(|e| XmlCheckError::general(format!("Failed to serialize config: {}", e)))?;
        std::fs::write(path, content)?;
        Ok(())
    }

    /// 验证配置的有效性
    pub fn validate(&self) -> Result<()> {
        // 验证解析器配置
        if self.parser.max_file_size == 0 {
            return Err(XmlCheckError::config("Max file size must be greater than 0"));
        }

        // 验证检测器配置
        if self.detector.min_speed >= self.detector.max_speed {
            return Err(XmlCheckError::config("Min speed must be less than max speed"));
        }

        if self.detector.speed_change_threshold < 0.0 || self.detector.speed_change_threshold > 100.0 {
            return Err(XmlCheckError::config("Speed change threshold must be between 0 and 100"));
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::NamedTempFile;
    use std::io::Write;

    #[test]
    fn test_default_config() {
        let config = Config::default();
        assert!(config.validate().is_ok());
    }

    #[test]
    fn test_config_serialization() {
        let config = Config::default();
        let toml_str = toml::to_string(&config).unwrap();
        let deserialized: Config = toml::from_str(&toml_str).unwrap();
        
        assert_eq!(config.parser.max_file_size, deserialized.parser.max_file_size);
    }

    #[test]
    fn test_config_file_operations() {
        let config = Config::default();
        let mut temp_file = NamedTempFile::new().unwrap();
        
        // 保存配置
        config.save_to_file(temp_file.path()).unwrap();
        
        // 加载配置
        let loaded_config = Config::from_file(temp_file.path()).unwrap();
        assert_eq!(config.parser.max_file_size, loaded_config.parser.max_file_size);
    }

    #[test]
    fn test_config_validation() {
        let mut config = Config::default();
        
        // 测试无效的文件大小
        config.parser.max_file_size = 0;
        assert!(config.validate().is_err());
        
        // 测试无效的速度范围
        config.parser.max_file_size = 1024;
        config.detector.min_speed = 100.0;
        config.detector.max_speed = 50.0;
        assert!(config.validate().is_err());
    }
}

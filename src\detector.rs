//! 变速数据检测模块
//! 
//! 检测XML中的变速数据并分析速度变化模式

use crate::config::DetectorConfig;
use crate::error::{Result, XmlCheckError};
use crate::parser::{ParsedXml, XmlNode};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 变速数据检测器
pub struct VariableSpeedDetector {
    config: DetectorConfig,
}

/// 检测结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetectionResults {
    /// 检测到的变速数据点
    pub speed_data_points: Vec<SpeedDataPoint>,
    /// 速度变化分析
    pub speed_analysis: SpeedAnalysis,
    /// 检测统计信息
    pub statistics: DetectionStatistics,
}

/// 速度数据点
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpeedDataPoint {
    /// 节点路径
    pub node_path: String,
    /// 速度值
    pub speed: f64,
    /// 时间值（如果有）
    pub time: Option<f64>,
    /// 其他相关属性
    pub attributes: HashMap<String, String>,
    /// 节点位置
    pub position: crate::parser::NodePosition,
}

/// 速度分析结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpeedAnalysis {
    /// 最小速度
    pub min_speed: f64,
    /// 最大速度
    pub max_speed: f64,
    /// 平均速度
    pub average_speed: f64,
    /// 速度标准差
    pub speed_std_dev: f64,
    /// 速度变化点
    pub speed_changes: Vec<SpeedChange>,
    /// 异常速度点
    pub anomalies: Vec<SpeedAnomaly>,
}

/// 速度变化
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpeedChange {
    /// 变化前的数据点索引
    pub from_index: usize,
    /// 变化后的数据点索引
    pub to_index: usize,
    /// 变化前的速度
    pub from_speed: f64,
    /// 变化后的速度
    pub to_speed: f64,
    /// 变化百分比
    pub change_percentage: f64,
    /// 变化类型
    pub change_type: SpeedChangeType,
}

/// 速度变化类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SpeedChangeType {
    /// 加速
    Acceleration,
    /// 减速
    Deceleration,
    /// 急剧变化
    Sudden,
}

/// 速度异常
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpeedAnomaly {
    /// 异常数据点索引
    pub data_point_index: usize,
    /// 异常类型
    pub anomaly_type: AnomalyType,
    /// 异常描述
    pub description: String,
    /// 严重程度
    pub severity: AnomalySeverity,
}

/// 异常类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AnomalyType {
    /// 超出范围
    OutOfRange,
    /// 异常变化
    AbnormalChange,
    /// 数据缺失
    MissingData,
    /// 数据格式错误
    InvalidFormat,
}

/// 异常严重程度
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AnomalySeverity {
    Low,
    Medium,
    High,
    Critical,
}

/// 检测统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetectionStatistics {
    /// 总数据点数
    pub total_data_points: usize,
    /// 有效数据点数
    pub valid_data_points: usize,
    /// 异常数据点数
    pub anomaly_count: usize,
    /// 速度变化次数
    pub speed_change_count: usize,
    /// 检测耗时（毫秒）
    pub detection_time_ms: u64,
}

impl VariableSpeedDetector {
    /// 创建新的变速数据检测器
    pub fn new(config: &DetectorConfig) -> Self {
        Self {
            config: config.clone(),
        }
    }

    /// 检测XML中的变速数据
    pub fn detect(&self, parsed_xml: &ParsedXml) -> Result<DetectionResults> {
        let start_time = std::time::Instant::now();

        // 查找所有可能的速度数据点
        let speed_data_points = self.find_speed_data_points(&parsed_xml.root)?;

        // 分析速度数据
        let speed_analysis = self.analyze_speed_data(&speed_data_points)?;

        // 生成统计信息
        let detection_time_ms = start_time.elapsed().as_millis() as u64;
        let statistics = DetectionStatistics {
            total_data_points: speed_data_points.len(),
            valid_data_points: speed_data_points.iter().filter(|p| p.speed >= 0.0).count(),
            anomaly_count: speed_analysis.anomalies.len(),
            speed_change_count: speed_analysis.speed_changes.len(),
            detection_time_ms,
        };

        Ok(DetectionResults {
            speed_data_points,
            speed_analysis,
            statistics,
        })
    }

    /// 查找速度数据点
    fn find_speed_data_points(&self, root: &XmlNode) -> Result<Vec<SpeedDataPoint>> {
        let mut data_points = Vec::new();
        self.find_speed_data_recursive(root, "", &mut data_points)?;
        Ok(data_points)
    }

    /// 递归查找速度数据
    fn find_speed_data_recursive(
        &self,
        node: &XmlNode,
        path: &str,
        data_points: &mut Vec<SpeedDataPoint>,
    ) -> Result<()> {
        let current_path = if path.is_empty() {
            node.name.clone()
        } else {
            format!("{}/{}", path, node.name)
        };

        // 检查当前节点是否包含速度数据
        if let Some(speed_data) = self.extract_speed_data(node, &current_path)? {
            data_points.push(speed_data);
        }

        // 递归检查子节点
        for child in &node.children {
            self.find_speed_data_recursive(child, &current_path, data_points)?;
        }

        Ok(())
    }

    /// 从节点提取速度数据
    fn extract_speed_data(&self, node: &XmlNode, path: &str) -> Result<Option<SpeedDataPoint>> {
        // 检查是否匹配速度数据模式
        let matches_pattern = self.config.speed_data_patterns.iter().any(|pattern| {
            // 简单的模式匹配（实际应用中可能需要更复杂的XPath支持）
            path.contains(&pattern.replace("//", "").replace("[@speed]", ""))
        });

        if !matches_pattern && !self.has_speed_attribute(node) {
            return Ok(None);
        }

        // 提取速度值
        let speed = self.extract_speed_value(node)?;
        if speed.is_none() {
            return Ok(None);
        }

        // 提取时间值
        let time = self.extract_time_value(node);

        // 收集所有属性
        let attributes = node.attributes.clone();

        Ok(Some(SpeedDataPoint {
            node_path: path.to_string(),
            speed: speed.unwrap(),
            time,
            attributes,
            position: node.position.clone(),
        }))
    }

    /// 检查节点是否有速度属性
    fn has_speed_attribute(&self, node: &XmlNode) -> bool {
        self.config.speed_attributes.iter().any(|attr| node.has_attribute(attr))
    }

    /// 提取速度值
    fn extract_speed_value(&self, node: &XmlNode) -> Result<Option<f64>> {
        for attr_name in &self.config.speed_attributes {
            if let Some(value_str) = node.get_attribute(attr_name) {
                match value_str.parse::<f64>() {
                    Ok(value) => return Ok(Some(value)),
                    Err(_) => continue,
                }
            }
        }

        // 如果属性中没有找到，尝试从文本内容中提取
        if let Some(text) = &node.text {
            if let Ok(value) = text.trim().parse::<f64>() {
                return Ok(Some(value));
            }
        }

        Ok(None)
    }

    /// 提取时间值
    fn extract_time_value(&self, node: &XmlNode) -> Option<f64> {
        for attr_name in &self.config.time_attributes {
            if let Some(value_str) = node.get_attribute(attr_name) {
                if let Ok(value) = value_str.parse::<f64>() {
                    return Some(value);
                }
            }
        }
        None
    }

    /// 分析速度数据
    fn analyze_speed_data(&self, data_points: &[SpeedDataPoint]) -> Result<SpeedAnalysis> {
        if data_points.is_empty() {
            return Ok(SpeedAnalysis {
                min_speed: 0.0,
                max_speed: 0.0,
                average_speed: 0.0,
                speed_std_dev: 0.0,
                speed_changes: Vec::new(),
                anomalies: Vec::new(),
            });
        }

        let speeds: Vec<f64> = data_points.iter().map(|p| p.speed).collect();
        
        let min_speed = speeds.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let max_speed = speeds.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
        let average_speed = speeds.iter().sum::<f64>() / speeds.len() as f64;
        
        // 计算标准差
        let variance = speeds.iter()
            .map(|&x| (x - average_speed).powi(2))
            .sum::<f64>() / speeds.len() as f64;
        let speed_std_dev = variance.sqrt();

        // 检测速度变化
        let speed_changes = self.detect_speed_changes(data_points)?;

        // 检测异常
        let anomalies = self.detect_anomalies(data_points, average_speed, speed_std_dev)?;

        Ok(SpeedAnalysis {
            min_speed,
            max_speed,
            average_speed,
            speed_std_dev,
            speed_changes,
            anomalies,
        })
    }

    /// 检测速度变化
    fn detect_speed_changes(&self, data_points: &[SpeedDataPoint]) -> Result<Vec<SpeedChange>> {
        let mut changes = Vec::new();

        for i in 1..data_points.len() {
            let prev_speed = data_points[i - 1].speed;
            let curr_speed = data_points[i].speed;
            
            if prev_speed == 0.0 {
                continue; // 避免除零
            }

            let change_percentage = ((curr_speed - prev_speed) / prev_speed).abs() * 100.0;
            
            if change_percentage >= self.config.speed_change_threshold {
                let change_type = if curr_speed > prev_speed {
                    if change_percentage > 100.0 {
                        SpeedChangeType::Sudden
                    } else {
                        SpeedChangeType::Acceleration
                    }
                } else {
                    if change_percentage > 100.0 {
                        SpeedChangeType::Sudden
                    } else {
                        SpeedChangeType::Deceleration
                    }
                };

                changes.push(SpeedChange {
                    from_index: i - 1,
                    to_index: i,
                    from_speed: prev_speed,
                    to_speed: curr_speed,
                    change_percentage,
                    change_type,
                });
            }
        }

        Ok(changes)
    }

    /// 检测异常
    fn detect_anomalies(&self, data_points: &[SpeedDataPoint], avg: f64, std_dev: f64) -> Result<Vec<SpeedAnomaly>> {
        let mut anomalies = Vec::new();

        for (i, data_point) in data_points.iter().enumerate() {
            // 检查范围异常
            if data_point.speed < self.config.min_speed || data_point.speed > self.config.max_speed {
                anomalies.push(SpeedAnomaly {
                    data_point_index: i,
                    anomaly_type: AnomalyType::OutOfRange,
                    description: format!(
                        "Speed {} is outside valid range [{}, {}]",
                        data_point.speed, self.config.min_speed, self.config.max_speed
                    ),
                    severity: AnomalySeverity::High,
                });
            }

            // 检查统计异常（超过3个标准差）
            if (data_point.speed - avg).abs() > 3.0 * std_dev {
                anomalies.push(SpeedAnomaly {
                    data_point_index: i,
                    anomaly_type: AnomalyType::AbnormalChange,
                    description: format!(
                        "Speed {} is {} standard deviations from average {}",
                        data_point.speed,
                        ((data_point.speed - avg) / std_dev).abs(),
                        avg
                    ),
                    severity: AnomalySeverity::Medium,
                });
            }
        }

        Ok(anomalies)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::DetectorConfig;
    use crate::parser::XmlParser;

    #[test]
    fn test_detector_creation() {
        let config = DetectorConfig::default();
        let detector = VariableSpeedDetector::new(&config);
        assert_eq!(detector.config.min_speed, config.min_speed);
    }

    #[test]
    fn test_speed_detection() {
        let detector_config = DetectorConfig::default();
        let detector = VariableSpeedDetector::new(&detector_config);
        
        let parser_config = crate::config::ParserConfig::default();
        let parser = XmlParser::new(&parser_config);
        
        let xml = r#"<root>
            <data speed="10.5" time="1.0"/>
            <data speed="15.2" time="2.0"/>
            <data speed="8.3" time="3.0"/>
        </root>"#;

        let parsed = parser.parse(xml).unwrap();
        let results = detector.detect(&parsed).unwrap();
        
        assert_eq!(results.speed_data_points.len(), 3);
        assert!(results.speed_analysis.min_speed > 0.0);
        assert!(results.speed_analysis.max_speed > results.speed_analysis.min_speed);
    }
}

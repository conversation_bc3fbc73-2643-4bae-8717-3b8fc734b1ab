//! 错误处理模块
//! 
//! 定义了XML检测工具中使用的所有错误类型

use thiserror::Error;

/// XML检测工具的结果类型
pub type Result<T> = std::result::Result<T, XmlCheckError>;

/// XML检测工具的错误类型
#[derive(Error, Debug)]
pub enum XmlCheckError {
    /// XML解析错误
    #[error("XML parsing error: {0}")]
    XmlParse(String),

    /// 文件I/O错误
    #[error("File I/O error: {0}")]
    Io(#[from] std::io::Error),

    /// 配置错误
    #[error("Configuration error: {0}")]
    Config(String),

    /// 数据验证错误
    #[error("Data validation error: {0}")]
    Validation(String),

    /// 变速数据检测错误
    #[error("Variable speed detection error: {0}")]
    Detection(String),

    /// 报告生成错误
    #[error("Report generation error: {0}")]
    Report(String),

    /// 序列化/反序列化错误
    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),

    /// TOML解析错误
    #[error("TOML parsing error: {0}")]
    Toml(#[from] toml::de::Error),

    /// 目录遍历错误
    #[error("Directory traversal error: {0}")]
    WalkDir(#[from] walkdir::Error),

    /// 正则表达式错误
    #[error("Regex error: {0}")]
    Regex(String),

    /// 数值解析错误
    #[error("Number parsing error: {0}")]
    ParseNumber(#[from] std::num::ParseFloatError),

    /// 通用错误
    #[error("General error: {0}")]
    General(String),
}

impl XmlCheckError {
    /// 创建XML解析错误
    pub fn xml_parse<S: Into<String>>(msg: S) -> Self {
        Self::XmlParse(msg.into())
    }

    /// 创建配置错误
    pub fn config<S: Into<String>>(msg: S) -> Self {
        Self::Config(msg.into())
    }

    /// 创建数据验证错误
    pub fn validation<S: Into<String>>(msg: S) -> Self {
        Self::Validation(msg.into())
    }

    /// 创建变速数据检测错误
    pub fn detection<S: Into<String>>(msg: S) -> Self {
        Self::Detection(msg.into())
    }

    /// 创建报告生成错误
    pub fn report<S: Into<String>>(msg: S) -> Self {
        Self::Report(msg.into())
    }

    /// 创建正则表达式错误
    pub fn regex<S: Into<String>>(msg: S) -> Self {
        Self::Regex(msg.into())
    }

    /// 创建通用错误
    pub fn general<S: Into<String>>(msg: S) -> Self {
        Self::General(msg.into())
    }
}

// 为roxmltree错误实现转换
impl From<roxmltree::Error> for XmlCheckError {
    fn from(err: roxmltree::Error) -> Self {
        Self::XmlParse(err.to_string())
    }
}

// 为quick-xml错误实现转换
impl From<quick_xml::Error> for XmlCheckError {
    fn from(err: quick_xml::Error) -> Self {
        Self::XmlParse(err.to_string())
    }
}

// 为serde-xml-rs错误实现转换
impl From<serde_xml_rs::Error> for XmlCheckError {
    fn from(err: serde_xml_rs::Error) -> Self {
        Self::XmlParse(err.to_string())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_creation() {
        let err = XmlCheckError::xml_parse("Test XML parse error");
        assert!(matches!(err, XmlCheckError::XmlParse(_)));
        assert_eq!(err.to_string(), "XML parsing error: Test XML parse error");
    }

    #[test]
    fn test_error_from_io() {
        let io_err = std::io::Error::new(std::io::ErrorKind::NotFound, "File not found");
        let xml_err = XmlCheckError::from(io_err);
        assert!(matches!(xml_err, XmlCheckError::Io(_)));
    }

    #[test]
    fn test_error_chain() {
        let config_err = XmlCheckError::config("Invalid configuration");
        let result: Result<()> = Err(config_err);
        assert!(result.is_err());
    }
}

//! XML变速数据检测工具 - 图形用户界面
//! 
//! 基于egui的跨平台GUI应用

use eframe::egui;
use std::path::PathBuf;
use xml_check::{config::Config, XmlChe<PERSON>};

fn main() -> Result<(), eframe::Error> {
    env_logger::init();

    // 检查命令行参数，支持拖拽文件
    let args: Vec<String> = std::env::args().collect();
    let initial_file = if args.len() > 1 {
        Some(args[1].clone())
    } else {
        None
    };

    let options = eframe::NativeOptions {
        viewport: egui::ViewportBuilder::default()
            .with_inner_size([800.0, 600.0])
            .with_min_inner_size([600.0, 400.0])
            .with_drag_and_drop(true)  // 启用拖拽支持
            .with_icon(
                // 可以在这里添加应用图标
                eframe::icon_data::from_png_bytes(&[]).unwrap_or_default(),
            ),
        ..Default::default()
    };

    eframe::run_native(
        "XML变速数据检测工具",
        options,
        Box::new(|cc| {
            // 设置字体以支持中文
            setup_custom_fonts(&cc.egui_ctx);
            Box::new(XmlCheckApp::new_with_file(initial_file))
        }),
    )
}

fn setup_custom_fonts(ctx: &egui::Context) {
    let mut fonts = egui::FontDefinitions::default();
    
    // 添加中文字体支持
    fonts.font_data.insert(
        "noto_sans_sc".to_owned(),
        egui::FontData::from_static(include_bytes!("../../assets/NotoSansSC-Regular.ttf")),
    );

    fonts
        .families
        .entry(egui::FontFamily::Proportional)
        .or_default()
        .insert(0, "noto_sans_sc".to_owned());

    fonts
        .families
        .entry(egui::FontFamily::Monospace)
        .or_default()
        .push("noto_sans_sc".to_owned());

    ctx.set_fonts(fonts);
}

struct XmlCheckApp {
    // 应用状态
    input_path: String,
    config_path: String,
    output_path: String,
    recursive: bool,
    output_format: String,

    // 检测状态
    is_checking: bool,
    check_progress: f32,

    // 结果
    reports: Vec<xml_check::reporter::Report>,
    error_message: Option<String>,

    // UI状态
    show_config_dialog: bool,
    show_results_dialog: bool,
    config: Config,
    checker: Option<XmlChecker>,

    // 拖拽状态
    drag_hover: bool,
    last_dropped_files: Vec<PathBuf>,
}

impl XmlCheckApp {
    fn new() -> Self {
        Self {
            input_path: String::new(),
            config_path: String::new(),
            output_path: String::new(),
            recursive: false,
            output_format: "json".to_string(),

            is_checking: false,
            check_progress: 0.0,

            reports: Vec::new(),
            error_message: None,

            show_config_dialog: false,
            show_results_dialog: false,
            config: Config::default(),
            checker: None,

            drag_hover: false,
            last_dropped_files: Vec::new(),
        }
    }

    fn new_with_file(initial_file: Option<String>) -> Self {
        let mut app = Self::new();
        if let Some(file_path) = initial_file {
            app.input_path = file_path;
            // 如果是通过拖拽或命令行参数传入的文件，自动开始检测
            app.start_check();
        }
        app
    }

    fn start_check(&mut self) {
        if self.input_path.is_empty() {
            self.error_message = Some("请选择输入文件或目录".to_string());
            return;
        }

        // 更新配置
        self.config.reporter.output_format = match self.output_format.as_str() {
            "json" => xml_check::config::OutputFormat::Json,
            "xml" => xml_check::config::OutputFormat::Xml,
            "html" => xml_check::config::OutputFormat::Html,
            "text" => xml_check::config::OutputFormat::Text,
            "csv" => xml_check::config::OutputFormat::Csv,
            _ => xml_check::config::OutputFormat::Json,
        };

        self.checker = Some(XmlChecker::with_config(self.config.clone()));
        self.is_checking = true;
        self.check_progress = 0.0;
        self.error_message = None;
        self.reports.clear();

        // 在实际应用中，这里应该使用异步任务来避免阻塞UI
        // 这里为了简化，直接执行检测
        self.perform_check();
    }

    fn perform_check(&mut self) {
        if let Some(checker) = &self.checker {
            let input_path = PathBuf::from(&self.input_path);
            
            let result = if input_path.is_file() {
                checker.check_file(&input_path).map(|report| vec![report])
            } else if input_path.is_dir() {
                checker.check_directory(&input_path, self.recursive)
            } else {
                Err(xml_check::error::XmlCheckError::general("输入路径不存在"))
            };

            match result {
                Ok(reports) => {
                    self.reports = reports;
                    self.show_results_dialog = true;
                }
                Err(e) => {
                    self.error_message = Some(e.to_string());
                }
            }
        }

        self.is_checking = false;
        self.check_progress = 1.0;
    }

    fn save_reports(&self) -> Result<(), Box<dyn std::error::Error>> {
        if self.output_path.is_empty() || self.reports.is_empty() {
            return Ok(());
        }

        if let Some(checker) = &self.checker {
            let generator = xml_check::reporter::ReportGenerator::new(&checker.config().reporter);
            
            if self.reports.len() == 1 {
                let content = generator.serialize_report(&self.reports[0])?;
                std::fs::write(&self.output_path, content)?;
            } else {
                // 创建汇总报告
                let combined_report = self.create_combined_report();
                let content = generator.serialize_report(&combined_report)?;
                std::fs::write(&self.output_path, content)?;
            }
        }

        Ok(())
    }

    fn create_combined_report(&self) -> xml_check::reporter::Report {
        // 简化的汇总报告创建
        xml_check::reporter::Report {
            metadata: xml_check::reporter::ReportMetadata {
                generated_at: "2024-01-01T00:00:00Z".to_string(),
                tool_version: env!("CARGO_PKG_VERSION").to_string(),
                format: self.config.reporter.output_format.clone(),
                language: "zh-CN".to_string(),
            },
            file_info: None,
            summary: xml_check::reporter::ReportSummary {
                overall_status: xml_check::reporter::OverallStatus::Pass,
                speed_data_points_count: self.reports.iter().map(|r| r.summary.speed_data_points_count).sum(),
                anomalies_count: self.reports.iter().map(|r| r.summary.anomalies_count).sum(),
                validation_errors_count: self.reports.iter().map(|r| r.summary.validation_errors_count).sum(),
                validation_warnings_count: self.reports.iter().map(|r| r.summary.validation_warnings_count).sum(),
                processing_time_ms: self.reports.iter().map(|r| r.summary.processing_time_ms).sum(),
            },
            detection_results: None,
            validation_results: None,
            recommendations: vec![],
        }
    }

    fn handle_drag_and_drop(&mut self, ctx: &egui::Context) {
        // 检查是否有文件被拖拽
        if let Some(dropped_files) = ctx.input(|i| {
            if !i.raw.dropped_files.is_empty() {
                Some(i.raw.dropped_files.clone())
            } else {
                None
            }
        }) {
            self.process_dropped_files(dropped_files);
        }

        // 检查拖拽悬停状态
        ctx.input(|i| {
            self.drag_hover = i.raw.hovered_files.len() > 0;
        });
    }

    fn process_dropped_files(&mut self, dropped_files: Vec<egui::DroppedFile>) {
        let mut xml_files = Vec::new();

        for file in dropped_files {
            if let Some(path) = &file.path {
                if path.extension().and_then(|ext| ext.to_str()) == Some("xml") {
                    xml_files.push(path.clone());
                } else if path.is_dir() {
                    // 如果拖拽的是目录，查找其中的XML文件
                    if let Ok(entries) = std::fs::read_dir(path) {
                        for entry in entries.flatten() {
                            let entry_path = entry.path();
                            if entry_path.extension().and_then(|ext| ext.to_str()) == Some("xml") {
                                xml_files.push(entry_path);
                            }
                        }
                    }
                }
            }
        }

        if xml_files.is_empty() {
            self.error_message = Some("拖拽的文件中没有找到XML文件".to_string());
            return;
        }

        // 处理拖拽的文件
        if xml_files.len() == 1 {
            // 单个文件
            self.input_path = xml_files[0].to_string_lossy().to_string();
            self.start_check();
        } else {
            // 多个文件，批量处理
            self.process_multiple_files(xml_files);
        }
    }

    fn process_multiple_files(&mut self, files: Vec<PathBuf>) {
        self.error_message = None;
        self.reports.clear();
        self.is_checking = true;

        // 更新配置
        self.config.reporter.output_format = match self.output_format.as_str() {
            "json" => xml_check::config::OutputFormat::Json,
            "xml" => xml_check::config::OutputFormat::Xml,
            "html" => xml_check::config::OutputFormat::Html,
            "text" => xml_check::config::OutputFormat::Text,
            "csv" => xml_check::config::OutputFormat::Csv,
            _ => xml_check::config::OutputFormat::Json,
        };

        let checker = XmlChecker::with_config(self.config.clone());

        // 批量处理文件
        match checker.check_files(&files) {
            Ok(reports) => {
                self.reports = reports;
                self.show_results_dialog = true;
                self.input_path = format!("批量处理 {} 个文件", files.len());
            }
            Err(e) => {
                self.error_message = Some(format!("批量处理失败: {}", e));
            }
        }

        self.is_checking = false;
        self.last_dropped_files = files;
    }
}

impl eframe::App for XmlCheckApp {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {
        // 处理拖拽事件
        self.handle_drag_and_drop(ctx);

        egui::CentralPanel::default().show(ctx, |ui| {
            // 拖拽区域提示
            if self.drag_hover {
                ui.allocate_ui_with_layout(
                    ui.available_size(),
                    egui::Layout::centered_and_justified(egui::Direction::TopDown),
                    |ui| {
                        ui.add_space(50.0);
                        ui.label(
                            egui::RichText::new("📁 拖拽XML文件到这里")
                                .size(24.0)
                                .color(egui::Color32::from_rgb(100, 150, 255))
                        );
                        ui.label(
                            egui::RichText::new("支持单个文件或多个文件")
                                .size(16.0)
                                .color(egui::Color32::GRAY)
                        );
                    }
                );
                return;
            }

            ui.heading("XML变速数据检测工具");
            ui.separator();

            // 拖拽提示区域
            ui.group(|ui| {
                ui.set_min_height(60.0);
                ui.vertical_centered(|ui| {
                    ui.add_space(10.0);
                    ui.label("💡 提示：您可以直接拖拽XML文件到此窗口进行检测");
                    ui.label("或使用下方的文件选择功能");
                    ui.add_space(10.0);
                });
            });

            ui.separator();

            // 输入文件选择
            ui.horizontal(|ui| {
                ui.label("输入路径:");
                ui.text_edit_singleline(&mut self.input_path);
                if ui.button("浏览...").clicked() {
                    if let Some(path) = rfd::FileDialog::new()
                        .add_filter("XML文件", &["xml"])
                        .pick_file()
                    {
                        self.input_path = path.display().to_string();
                    }
                }
                if ui.button("选择目录").clicked() {
                    if let Some(path) = rfd::FileDialog::new().pick_folder() {
                        self.input_path = path.display().to_string();
                    }
                }
            });

            // 递归选项
            ui.checkbox(&mut self.recursive, "递归处理子目录");

            ui.separator();

            // 输出设置
            ui.horizontal(|ui| {
                ui.label("输出路径:");
                ui.text_edit_singleline(&mut self.output_path);
                if ui.button("浏览...").clicked() {
                    if let Some(path) = rfd::FileDialog::new()
                        .add_filter("JSON", &["json"])
                        .add_filter("XML", &["xml"])
                        .add_filter("HTML", &["html"])
                        .add_filter("文本", &["txt"])
                        .add_filter("CSV", &["csv"])
                        .save_file()
                    {
                        self.output_path = path.display().to_string();
                    }
                }
            });

            ui.horizontal(|ui| {
                ui.label("输出格式:");
                egui::ComboBox::from_label("")
                    .selected_text(&self.output_format)
                    .show_ui(ui, |ui| {
                        ui.selectable_value(&mut self.output_format, "json".to_string(), "JSON");
                        ui.selectable_value(&mut self.output_format, "xml".to_string(), "XML");
                        ui.selectable_value(&mut self.output_format, "html".to_string(), "HTML");
                        ui.selectable_value(&mut self.output_format, "text".to_string(), "文本");
                        ui.selectable_value(&mut self.output_format, "csv".to_string(), "CSV");
                    });
            });

            ui.separator();

            // 控制按钮
            ui.horizontal(|ui| {
                if ui.button("开始检测").clicked() && !self.is_checking {
                    self.start_check();
                }

                if ui.button("配置设置").clicked() {
                    self.show_config_dialog = true;
                }

                if ui.button("查看结果").clicked() && !self.reports.is_empty() {
                    self.show_results_dialog = true;
                }
            });

            // 进度条
            if self.is_checking {
                ui.separator();
                ui.label("检测中...");
                ui.add(egui::ProgressBar::new(self.check_progress).show_percentage());
            }

            // 错误消息
            if let Some(error) = &self.error_message {
                ui.separator();
                ui.colored_label(egui::Color32::RED, format!("错误: {}", error));
            }

            // 结果摘要
            if !self.reports.is_empty() {
                ui.separator();
                ui.heading("检测结果摘要");
                
                let total_files = self.reports.len();
                let total_data_points: usize = self.reports.iter().map(|r| r.summary.speed_data_points_count).sum();
                let total_anomalies: usize = self.reports.iter().map(|r| r.summary.anomalies_count).sum();
                let total_errors: usize = self.reports.iter().map(|r| r.summary.validation_errors_count).sum();
                let total_warnings: usize = self.reports.iter().map(|r| r.summary.validation_warnings_count).sum();

                ui.label(format!("处理文件数: {}", total_files));
                ui.label(format!("总数据点: {}", total_data_points));
                ui.label(format!("异常数量: {}", total_anomalies));
                ui.label(format!("验证错误: {}", total_errors));
                ui.label(format!("验证警告: {}", total_warnings));

                if ui.button("保存报告").clicked() {
                    if let Err(e) = self.save_reports() {
                        self.error_message = Some(format!("保存报告失败: {}", e));
                    }
                }
            }
        });

        // 配置对话框
        if self.show_config_dialog {
            egui::Window::new("配置设置")
                .collapsible(false)
                .resizable(true)
                .show(ctx, |ui| {
                    ui.label("配置功能待实现...");
                    if ui.button("关闭").clicked() {
                        self.show_config_dialog = false;
                    }
                });
        }

        // 结果对话框
        if self.show_results_dialog {
            egui::Window::new("检测结果")
                .collapsible(false)
                .resizable(true)
                .default_size([600.0, 400.0])
                .show(ctx, |ui| {
                    egui::ScrollArea::vertical().show(ui, |ui| {
                        for (i, report) in self.reports.iter().enumerate() {
                            ui.group(|ui| {
                                ui.heading(format!("报告 {}", i + 1));
                                
                                if let Some(file_info) = &report.file_info {
                                    ui.label(format!("文件: {}", file_info.file_path));
                                }
                                
                                ui.label(format!("状态: {:?}", report.summary.overall_status));
                                ui.label(format!("数据点: {}", report.summary.speed_data_points_count));
                                ui.label(format!("异常: {}", report.summary.anomalies_count));
                                ui.label(format!("错误: {}", report.summary.validation_errors_count));
                                ui.label(format!("警告: {}", report.summary.validation_warnings_count));
                            });
                            ui.separator();
                        }
                    });
                    
                    if ui.button("关闭").clicked() {
                        self.show_results_dialog = false;
                    }
                });
        }
    }
}

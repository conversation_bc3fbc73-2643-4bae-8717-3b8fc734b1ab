//! XML变速数据检测工具核心库
//! 
//! 这个库提供了XML文件解析、变速数据检测、数据验证和报告生成的核心功能。

pub mod config;
pub mod detector;
pub mod error;
pub mod parser;
pub mod reporter;
pub mod validator;

use std::path::Path;

pub use error::{XmlCheckError, Result};

/// XML检测工具的主要接口
#[derive(Debug, Clone)]
pub struct XmlChecker {
    config: config::Config,
}

impl XmlChecker {
    /// 创建新的XML检测器实例
    pub fn new() -> Result<Self> {
        let config = config::Config::default();
        Ok(Self { config })
    }

    /// 使用自定义配置创建XML检测器
    pub fn with_config(config: config::Config) -> Self {
        Self { config }
    }

    /// 从配置文件创建XML检测器
    pub fn from_config_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        let config = config::Config::from_file(path)?;
        Ok(Self { config })
    }

    /// 检测单个XML文件
    pub fn check_file<P: AsRef<Path>>(&self, path: P) -> Result<reporter::Report> {
        let xml_content = std::fs::read_to_string(&path)?;
        self.check_xml_content(&xml_content, Some(path.as_ref()))
    }

    /// 检测XML内容字符串
    pub fn check_xml_content(&self, content: &str, file_path: Option<&Path>) -> Result<reporter::Report> {
        // 解析XML
        let parsed_xml = parser::XmlParser::new(&self.config.parser)
            .parse(content)?;

        // 检测变速数据
        let detection_results = detector::VariableSpeedDetector::new(&self.config.detector)
            .detect(&parsed_xml)?;

        // 验证数据
        let validation_results = validator::DataValidator::new(&self.config.validator)
            .validate(&parsed_xml, &detection_results)?;

        // 生成报告
        let report = reporter::ReportGenerator::new(&self.config.reporter)
            .generate(file_path, &parsed_xml, &detection_results, &validation_results)?;

        Ok(report)
    }

    /// 批量检测多个XML文件
    pub fn check_files<P: AsRef<Path>>(&self, paths: &[P]) -> Result<Vec<reporter::Report>> {
        let mut reports = Vec::new();
        
        for path in paths {
            match self.check_file(path) {
                Ok(report) => reports.push(report),
                Err(e) => {
                    log::error!("Failed to check file {:?}: {}", path.as_ref(), e);
                    // 创建错误报告
                    let error_report = reporter::Report::error(
                        Some(path.as_ref()),
                        format!("Failed to process file: {}", e)
                    );
                    reports.push(error_report);
                }
            }
        }
        
        Ok(reports)
    }

    /// 检测目录中的所有XML文件
    pub fn check_directory<P: AsRef<Path>>(&self, dir_path: P, recursive: bool) -> Result<Vec<reporter::Report>> {
        let xml_files = self.find_xml_files(dir_path, recursive)?;
        self.check_files(&xml_files)
    }

    /// 查找目录中的XML文件
    fn find_xml_files<P: AsRef<Path>>(&self, dir_path: P, recursive: bool) -> Result<Vec<std::path::PathBuf>> {
        use walkdir::WalkDir;
        
        let mut xml_files = Vec::new();
        let walker = if recursive {
            WalkDir::new(dir_path)
        } else {
            WalkDir::new(dir_path).max_depth(1)
        };

        for entry in walker {
            let entry = entry?;
            if entry.file_type().is_file() {
                if let Some(extension) = entry.path().extension() {
                    if extension.to_string_lossy().to_lowercase() == "xml" {
                        xml_files.push(entry.path().to_path_buf());
                    }
                }
            }
        }

        Ok(xml_files)
    }

    /// 获取当前配置
    pub fn config(&self) -> &config::Config {
        &self.config
    }

    /// 更新配置
    pub fn update_config(&mut self, config: config::Config) {
        self.config = config;
    }
}

impl Default for XmlChecker {
    fn default() -> Self {
        Self::new().expect("Failed to create default XmlChecker")
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::NamedTempFile;
    use std::io::Write;

    #[test]
    fn test_xml_checker_creation() {
        let checker = XmlChecker::new();
        assert!(checker.is_ok());
    }

    #[test]
    fn test_check_xml_content() {
        let checker = XmlChecker::new().unwrap();
        let xml_content = r#"<?xml version="1.0" encoding="UTF-8"?>
<root>
    <data speed="10.5" time="1.0"/>
    <data speed="15.2" time="2.0"/>
</root>"#;

        let result = checker.check_xml_content(xml_content, None);
        assert!(result.is_ok());
    }

    #[test]
    fn test_check_file() {
        let checker = XmlChecker::new().unwrap();
        
        // 创建临时XML文件
        let mut temp_file = NamedTempFile::new().unwrap();
        let xml_content = r#"<?xml version="1.0" encoding="UTF-8"?>
<root>
    <data speed="10.5" time="1.0"/>
</root>"#;
        temp_file.write_all(xml_content.as_bytes()).unwrap();

        let result = checker.check_file(temp_file.path());
        assert!(result.is_ok());
    }
}

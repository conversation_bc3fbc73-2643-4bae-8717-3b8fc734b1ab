//! XML变速数据检测工具 - 自包含版本

use std::fs;

fn main() {
    println!("🚀 XML变速数据检测工具 v{}", env!("CARGO_PKG_VERSION"));
    println!("{}", "=".repeat(50));

    let args: Vec<String> = std::env::args().collect();

    if args.len() < 2 {
        println!("用法: {} <xml文件路径>", args[0]);
        println!("示例: {} examples/sample_speed_data.xml", args[0]);
        println!("\n支持的功能:");
        println!("  - 检测XML文件中的速度数据");
        println!("  - 支持speed、velocity、rate属性");
        println!("  - 统计分析和异常检测");
        println!("  - 拖拽文件到工具图标直接检测");
        return;
    }

    let file_path = &args[1];

    if !file_path.ends_with(".xml") {
        println!("❌ 错误: 请提供XML文件 (.xml扩展名)");
        return;
    }

    match analyze_xml_file(file_path) {
        Ok(()) => {
            println!("✅ 检测完成!");
            println!("\n💡 提示: 您可以直接拖拽XML文件到此工具进行检测");
        },
        Err(e) => {
            println!("❌ 检测失败: {}", e);
        }
    }

    // 如果是通过拖拽启动的，等待用户确认
    if args.len() == 2 {
        println!("\n按任意键退出...");
        let _ = std::io::stdin().read_line(&mut String::new());
    }
}

fn analyze_xml_file(file_path: &str) -> Result<(), Box<dyn std::error::Error>> {
    println!("📁 正在检测文件: {}", file_path);

    // 读取文件
    let content = fs::read_to_string(file_path)?;
    println!("📄 文件大小: {} 字节", content.len());

    // 简单的XML解析 - 查找速度相关属性
    let mut speed_count = 0;
    let mut speeds = Vec::new();
    let mut time_count = 0;

    // 使用简单的字符串匹配查找属性
    let speed_patterns = ["speed=", "velocity=", "rate="];
    let time_patterns = ["time=", "timestamp=", "t="];

    // 查找速度数据
    for line in content.lines() {
        for pattern in &speed_patterns {
            if let Some(values) = extract_attribute_values(line, pattern) {
                for value in values {
                    if let Ok(speed) = value.parse::<f64>() {
                        speeds.push(speed);
                        speed_count += 1;
                    }
                }
            }
        }

        // 查找时间数据
        for pattern in &time_patterns {
            if let Some(values) = extract_attribute_values(line, pattern) {
                time_count += values.len();
            }
        }
    }

    println!("🔍 XML解析完成");
    println!("📊 检测结果:");
    println!("   速度数据点数量: {}", speed_count);
    println!("   时间数据点数量: {}", time_count);

    if !speeds.is_empty() {
        let min_speed = speeds.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let max_speed = speeds.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
        let avg_speed = speeds.iter().sum::<f64>() / speeds.len() as f64;

        println!("   最小速度: {:.2}", min_speed);
        println!("   最大速度: {:.2}", max_speed);
        println!("   平均速度: {:.2}", avg_speed);

        // 检查异常
        let mut anomalies = 0;
        let mut out_of_range = 0;
        let mut negative_speeds = 0;

        for &speed in &speeds {
            if speed < 0.0 {
                negative_speeds += 1;
                anomalies += 1;
            } else if speed > 1000.0 {
                out_of_range += 1;
                anomalies += 1;
            }
        }

        // 检查速度变化异常
        let mut sudden_changes = 0;
        for i in 1..speeds.len() {
            let prev_speed = speeds[i-1];
            let curr_speed = speeds[i];
            if prev_speed > 0.0 {
                let change_percent = ((curr_speed - prev_speed) / prev_speed).abs() * 100.0;
                if change_percent > 100.0 {  // 超过100%的变化
                    sudden_changes += 1;
                }
            }
        }

        println!("🔍 异常检测:");
        if anomalies > 0 {
            println!("   ⚠️  发现 {} 个异常数据点", anomalies);
            if negative_speeds > 0 {
                println!("      - {} 个负速度值", negative_speeds);
            }
            if out_of_range > 0 {
                println!("      - {} 个超出范围值 (>1000)", out_of_range);
            }
            if sudden_changes > 0 {
                println!("      - {} 个突变点 (变化>100%)", sudden_changes);
            }
        } else {
            println!("   ✅ 未发现异常数据");
        }

        // 数据质量评估
        let quality_score = if anomalies == 0 && time_count > 0 {
            "优秀"
        } else if anomalies <= speeds.len() / 10 {
            "良好"
        } else if anomalies <= speeds.len() / 5 {
            "一般"
        } else {
            "较差"
        };

        println!("📈 数据质量评估: {}", quality_score);

    } else {
        println!("   ⚠️  未找到速度数据");
        println!("   💡 提示: 请确保XML文件包含speed、velocity或rate属性");
    }

    Ok(())
}

// 简单的属性值提取函数
fn extract_attribute_values(line: &str, pattern: &str) -> Option<Vec<String>> {
    let mut results = Vec::new();
    let mut start = 0;

    while let Some(pos) = line[start..].find(pattern) {
        let attr_start = start + pos + pattern.len();
        if let Some(quote_start) = line[attr_start..].find('"') {
            let value_start = attr_start + quote_start + 1;
            if let Some(quote_end) = line[value_start..].find('"') {
                let value = &line[value_start..value_start + quote_end];
                results.push(value.to_string());
                start = value_start + quote_end + 1;
            } else {
                break;
            }
        } else {
            break;
        }
    }

    if results.is_empty() {
        None
    } else {
        Some(results)
    }
}

//! XML变速数据检测工具 - 命令行界面
//! 
//! 这是一个跨平台的XML数据检测工具，专门用于检测和验证XML文件中的变速数据

use clap::{Arg, ArgAction, Command};
use colored::*;
use std::path::PathBuf;
use xml_check::{config::Config, XmlChecker};

fn main() {
    // 初始化日志
    env_logger::init();

    // 检查是否有拖拽文件参数（Windows文件关联）
    let args: Vec<String> = std::env::args().collect();

    // 如果只有一个参数且是XML文件，直接处理
    if args.len() == 2 && args[1].ends_with(".xml") {
        if let Err(e) = run_simple_detection(&args[1]) {
            eprintln!("{} {}", "错误:".red().bold(), e);
            println!("\n按任意键退出...");
            let _ = std::io::stdin().read_line(&mut String::new());
            std::process::exit(1);
        }
        return;
    }

    // 解析命令行参数
    let matches = Command::new("xml-check")
        .version(env!("CARGO_PKG_VERSION"))
        .author("XML Check Tool")
        .about("跨平台XML变速数据检测工具")
        .arg(
            Arg::new("input")
                .help("输入XML文件或目录路径")
                .required(true)
                .index(1),
        )
        .arg(
            Arg::new("config")
                .short('c')
                .long("config")
                .value_name("FILE")
                .help("配置文件路径")
                .action(ArgAction::Set),
        )
        .arg(
            Arg::new("output")
                .short('o')
                .long("output")
                .value_name("FILE")
                .help("输出报告文件路径")
                .action(ArgAction::Set),
        )
        .arg(
            Arg::new("format")
                .short('f')
                .long("format")
                .value_name("FORMAT")
                .help("输出格式 (json, xml, html, text, csv)")
                .default_value("json")
                .action(ArgAction::Set),
        )
        .arg(
            Arg::new("recursive")
                .short('r')
                .long("recursive")
                .help("递归处理目录中的所有XML文件")
                .action(ArgAction::SetTrue),
        )
        .arg(
            Arg::new("verbose")
                .short('v')
                .long("verbose")
                .help("显示详细输出")
                .action(ArgAction::SetTrue),
        )
        .arg(
            Arg::new("quiet")
                .short('q')
                .long("quiet")
                .help("静默模式，只输出错误")
                .action(ArgAction::SetTrue),
        )
        .get_matches();

    // 获取命令行参数
    let input_path = matches.get_one::<String>("input").unwrap();
    let config_path = matches.get_one::<String>("config");
    let output_path = matches.get_one::<String>("output");
    let format = matches.get_one::<String>("format").unwrap();
    let recursive = matches.get_flag("recursive");
    let verbose = matches.get_flag("verbose");
    let quiet = matches.get_flag("quiet");

    // 运行检测
    if let Err(e) = run_detection(
        input_path,
        config_path,
        output_path,
        format,
        recursive,
        verbose,
        quiet,
    ) {
        eprintln!("{} {}", "错误:".red().bold(), e);
        std::process::exit(1);
    }
}

fn run_detection(
    input_path: &str,
    config_path: Option<&String>,
    output_path: Option<&String>,
    format: &str,
    recursive: bool,
    verbose: bool,
    quiet: bool,
) -> Result<(), Box<dyn std::error::Error>> {
    if !quiet {
        println!("{}", "XML变速数据检测工具".cyan().bold());
        println!("{}", "=".repeat(50).cyan());
    }

    // 加载配置
    let mut config = if let Some(config_file) = config_path {
        if verbose && !quiet {
            println!("📁 加载配置文件: {}", config_file);
        }
        Config::from_file(config_file)?
    } else {
        Config::default()
    };

    // 设置输出格式
    config.reporter.output_format = match format {
        "json" => xml_check::config::OutputFormat::Json,
        "xml" => xml_check::config::OutputFormat::Xml,
        "html" => xml_check::config::OutputFormat::Html,
        "text" => xml_check::config::OutputFormat::Text,
        "csv" => xml_check::config::OutputFormat::Csv,
        _ => {
            return Err(format!("不支持的输出格式: {}", format).into());
        }
    };

    // 创建检测器
    let checker = XmlChecker::with_config(config);

    // 检测输入路径类型
    let input_path_buf = PathBuf::from(input_path);
    let reports = if input_path_buf.is_file() {
        if verbose && !quiet {
            println!("📄 检测文件: {}", input_path);
        }
        vec![checker.check_file(&input_path_buf)?]
    } else if input_path_buf.is_dir() {
        if verbose && !quiet {
            println!("📁 检测目录: {} (递归: {})", input_path, recursive);
        }
        checker.check_directory(&input_path_buf, recursive)?
    } else {
        return Err(format!("输入路径不存在: {}", input_path).into());
    };

    if !quiet {
        println!("✅ 检测完成，共处理 {} 个文件", reports.len());
    }

    // 显示结果摘要
    if !quiet {
        display_summary(&reports, verbose);
    }

    // 输出报告
    if let Some(output_file) = output_path {
        save_reports(&reports, output_file, &checker)?;
        if !quiet {
            println!("💾 报告已保存到: {}", output_file);
        }
    } else if !quiet {
        // 输出到控制台
        for (i, report) in reports.iter().enumerate() {
            if reports.len() > 1 {
                println!("\n{} 报告 {} {}", "=".repeat(10), i + 1, "=".repeat(10));
            }
            let report_content = xml_check::reporter::ReportGenerator::new(&checker.config().reporter)
                .serialize_report(report)?;
            println!("{}", report_content);
        }
    }

    // 检查是否有错误
    let has_errors = reports.iter().any(|r| {
        matches!(
            r.summary.overall_status,
            xml_check::reporter::OverallStatus::Fail | xml_check::reporter::OverallStatus::Error
        )
    });

    if has_errors {
        std::process::exit(1);
    }

    Ok(())
}

/// 简单检测模式，用于文件关联和拖拽
fn run_simple_detection(file_path: &str) -> Result<(), Box<dyn std::error::Error>> {
    println!("{}", "XML变速数据检测工具".cyan().bold());
    println!("{}", "=".repeat(50).cyan());
    println!("📁 检测文件: {}", file_path);
    println!();

    // 使用默认配置
    let checker = XmlChecker::new()?;
    let report = checker.check_file(file_path)?;

    // 显示结果
    println!("{}", "检测结果".yellow().bold());
    println!("{}", "-".repeat(30).yellow());

    let status_icon = match report.summary.overall_status {
        xml_check::reporter::OverallStatus::Pass => "✅",
        xml_check::reporter::OverallStatus::Warning => "⚠️",
        xml_check::reporter::OverallStatus::Fail => "❌",
        xml_check::reporter::OverallStatus::Error => "💥",
    };

    println!("{} 状态: {:?}", status_icon, report.summary.overall_status);
    println!("📊 速度数据点: {}", report.summary.speed_data_points_count);
    println!("⚡ 异常数量: {}", report.summary.anomalies_count);
    println!("❗ 验证错误: {}", report.summary.validation_errors_count);
    println!("⚠️  验证警告: {}", report.summary.validation_warnings_count);
    println!("⏱️  处理时间: {} ms", report.summary.processing_time_ms);

    // 显示建议
    if !report.recommendations.is_empty() {
        println!("\n{}", "建议".blue().bold());
        println!("{}", "-".repeat(30).blue());
        for (i, rec) in report.recommendations.iter().enumerate() {
            println!("{}. {} (优先级: {:?})", i + 1, rec.title, rec.priority);
            println!("   {}", rec.description);
        }
    }

    // 询问是否保存报告
    println!("\n是否保存详细报告? (y/N): ");
    let mut input = String::new();
    if std::io::stdin().read_line(&mut input).is_ok() {
        let input = input.trim().to_lowercase();
        if input == "y" || input == "yes" {
            let output_file = format!("{}_report.json",
                std::path::Path::new(file_path)
                    .file_stem()
                    .unwrap_or_default()
                    .to_string_lossy()
            );

            let generator = xml_check::reporter::ReportGenerator::new(&checker.config().reporter);
            let content = generator.serialize_report(&report)?;
            std::fs::write(&output_file, content)?;
            println!("💾 报告已保存到: {}", output_file);
        }
    }

    println!("\n按任意键退出...");
    let _ = std::io::stdin().read_line(&mut String::new());

    Ok(())
}

fn display_summary(reports: &[xml_check::reporter::Report], verbose: bool) {
    let mut total_data_points = 0;
    let mut total_anomalies = 0;
    let mut total_errors = 0;
    let mut total_warnings = 0;
    let mut pass_count = 0;
    let mut warning_count = 0;
    let mut fail_count = 0;
    let mut error_count = 0;

    for report in reports {
        total_data_points += report.summary.speed_data_points_count;
        total_anomalies += report.summary.anomalies_count;
        total_errors += report.summary.validation_errors_count;
        total_warnings += report.summary.validation_warnings_count;

        match report.summary.overall_status {
            xml_check::reporter::OverallStatus::Pass => pass_count += 1,
            xml_check::reporter::OverallStatus::Warning => warning_count += 1,
            xml_check::reporter::OverallStatus::Fail => fail_count += 1,
            xml_check::reporter::OverallStatus::Error => error_count += 1,
        }
    }

    println!("\n{}", "检测结果摘要".yellow().bold());
    println!("{}", "-".repeat(30).yellow());
    
    if pass_count > 0 {
        println!("✅ 通过: {} 个文件", pass_count.to_string().green());
    }
    if warning_count > 0 {
        println!("⚠️  警告: {} 个文件", warning_count.to_string().yellow());
    }
    if fail_count > 0 {
        println!("❌ 失败: {} 个文件", fail_count.to_string().red());
    }
    if error_count > 0 {
        println!("💥 错误: {} 个文件", error_count.to_string().red().bold());
    }

    println!("\n{}", "数据统计".blue().bold());
    println!("{}", "-".repeat(30).blue());
    println!("📊 总数据点: {}", total_data_points);
    println!("⚡ 异常数量: {}", total_anomalies);
    println!("❗ 验证错误: {}", total_errors);
    println!("⚠️  验证警告: {}", total_warnings);

    if verbose {
        println!("\n{}", "详细信息".magenta().bold());
        println!("{}", "-".repeat(30).magenta());
        for (i, report) in reports.iter().enumerate() {
            let status_icon = match report.summary.overall_status {
                xml_check::reporter::OverallStatus::Pass => "✅",
                xml_check::reporter::OverallStatus::Warning => "⚠️",
                xml_check::reporter::OverallStatus::Fail => "❌",
                xml_check::reporter::OverallStatus::Error => "💥",
            };
            
            let file_name = report
                .file_info
                .as_ref()
                .map(|f| f.file_path.as_str())
                .unwrap_or("未知文件");
                
            println!(
                "{} {} - 数据点: {}, 异常: {}, 错误: {}, 警告: {}",
                status_icon,
                file_name,
                report.summary.speed_data_points_count,
                report.summary.anomalies_count,
                report.summary.validation_errors_count,
                report.summary.validation_warnings_count
            );
        }
    }
}

fn save_reports(
    reports: &[xml_check::reporter::Report],
    output_path: &str,
    checker: &XmlChecker,
) -> Result<(), Box<dyn std::error::Error>> {
    let generator = xml_check::reporter::ReportGenerator::new(&checker.config().reporter);
    
    if reports.len() == 1 {
        // 单个报告
        let content = generator.serialize_report(&reports[0])?;
        std::fs::write(output_path, content)?;
    } else {
        // 多个报告，创建汇总
        let combined_report = create_combined_report(reports);
        let content = generator.serialize_report(&combined_report)?;
        std::fs::write(output_path, content)?;
    }
    
    Ok(())
}

fn create_combined_report(reports: &[xml_check::reporter::Report]) -> xml_check::reporter::Report {
    // 创建一个汇总报告
    let mut total_data_points = 0;
    let mut total_anomalies = 0;
    let mut total_errors = 0;
    let mut total_warnings = 0;
    let mut total_processing_time = 0;

    let mut overall_status = xml_check::reporter::OverallStatus::Pass;

    for report in reports {
        total_data_points += report.summary.speed_data_points_count;
        total_anomalies += report.summary.anomalies_count;
        total_errors += report.summary.validation_errors_count;
        total_warnings += report.summary.validation_warnings_count;
        total_processing_time += report.summary.processing_time_ms;

        // 更新整体状态
        match report.summary.overall_status {
            xml_check::reporter::OverallStatus::Error => {
                overall_status = xml_check::reporter::OverallStatus::Error;
            }
            xml_check::reporter::OverallStatus::Fail => {
                if !matches!(overall_status, xml_check::reporter::OverallStatus::Error) {
                    overall_status = xml_check::reporter::OverallStatus::Fail;
                }
            }
            xml_check::reporter::OverallStatus::Warning => {
                if matches!(overall_status, xml_check::reporter::OverallStatus::Pass) {
                    overall_status = xml_check::reporter::OverallStatus::Warning;
                }
            }
            _ => {}
        }
    }

    xml_check::reporter::Report {
        metadata: xml_check::reporter::ReportMetadata {
            generated_at: "2024-01-01T00:00:00Z".to_string(),
            tool_version: env!("CARGO_PKG_VERSION").to_string(),
            format: xml_check::config::OutputFormat::Json,
            language: "zh-CN".to_string(),
        },
        file_info: None,
        summary: xml_check::reporter::ReportSummary {
            overall_status,
            speed_data_points_count: total_data_points,
            anomalies_count: total_anomalies,
            validation_errors_count: total_errors,
            validation_warnings_count: total_warnings,
            processing_time_ms: total_processing_time,
        },
        detection_results: None,
        validation_results: None,
        recommendations: vec![],
    }
}

//! XML解析模块
//! 
//! 提供多种XML解析方式和数据结构

use crate::config::ParserConfig;
use crate::error::{Result, XmlCheckError};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// XML解析器
pub struct XmlParser {
    config: ParserConfig,
}

/// 解析后的XML文档结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParsedXml {
    /// 文档根节点
    pub root: XmlNode,
    /// 文档元数据
    pub metadata: XmlMetadata,
}

/// XML节点
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct XmlNode {
    /// 节点名称
    pub name: String,
    /// 节点属性
    pub attributes: HashMap<String, String>,
    /// 节点文本内容
    pub text: Option<String>,
    /// 子节点
    pub children: Vec<XmlNode>,
    /// 节点在文档中的位置
    pub position: NodePosition,
}

/// 节点位置信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NodePosition {
    /// 行号
    pub line: usize,
    /// 列号
    pub column: usize,
    /// 在父节点中的索引
    pub index: usize,
}

/// XML文档元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct XmlMetadata {
    /// XML版本
    pub version: String,
    /// 编码格式
    pub encoding: String,
    /// 是否独立文档
    pub standalone: Option<bool>,
    /// 文档大小（字节）
    pub size: usize,
    /// 节点总数
    pub node_count: usize,
    /// 最大深度
    pub max_depth: usize,
}

impl XmlParser {
    /// 创建新的XML解析器
    pub fn new(config: &ParserConfig) -> Self {
        Self {
            config: config.clone(),
        }
    }

    /// 解析XML字符串
    pub fn parse(&self, xml_content: &str) -> Result<ParsedXml> {
        // 检查文件大小
        if xml_content.len() > self.config.max_file_size {
            return Err(XmlCheckError::xml_parse(format!(
                "File size {} exceeds maximum allowed size {}",
                xml_content.len(),
                self.config.max_file_size
            )));
        }

        // 使用roxmltree进行解析
        let doc = roxmltree::Document::parse(xml_content)?;
        
        // 提取元数据
        let metadata = self.extract_metadata(&doc, xml_content.len())?;
        
        // 转换为内部结构
        let root = self.convert_node(doc.root_element(), 0)?;
        
        Ok(ParsedXml { root, metadata })
    }

    /// 提取XML文档元数据
    fn extract_metadata(&self, doc: &roxmltree::Document, size: usize) -> Result<XmlMetadata> {
        let mut node_count = 0;
        let mut max_depth = 0;

        // 遍历所有节点计算统计信息
        self.count_nodes_and_depth(doc.root_element(), 1, &mut node_count, &mut max_depth);

        Ok(XmlMetadata {
            version: "1.0".to_string(), // roxmltree不直接提供版本信息
            encoding: "UTF-8".to_string(), // 默认编码
            standalone: None,
            size,
            node_count,
            max_depth,
        })
    }

    /// 递归计算节点数量和最大深度
    fn count_nodes_and_depth(
        &self,
        node: roxmltree::Node,
        current_depth: usize,
        node_count: &mut usize,
        max_depth: &mut usize,
    ) {
        if node.is_element() {
            *node_count += 1;
            *max_depth = (*max_depth).max(current_depth);

            for child in node.children() {
                self.count_nodes_and_depth(child, current_depth + 1, node_count, max_depth);
            }
        }
    }

    /// 转换roxmltree节点为内部节点结构
    fn convert_node(&self, node: roxmltree::Node, index: usize) -> Result<XmlNode> {
        if !node.is_element() {
            return Err(XmlCheckError::xml_parse("Expected element node"));
        }

        let name = node.tag_name().name().to_string();
        
        // 提取属性
        let mut attributes = HashMap::new();
        for attr in node.attributes() {
            attributes.insert(attr.name().to_string(), attr.value().to_string());
        }

        // 提取文本内容
        let text = node.text().map(|t| t.to_string());

        // 转换子节点
        let mut children = Vec::new();
        let mut child_index = 0;
        for child in node.children() {
            if child.is_element() {
                children.push(self.convert_node(child, child_index)?);
                child_index += 1;
            }
        }

        // 创建位置信息（roxmltree不提供位置信息，使用默认值）
        let position = NodePosition {
            line: 0,
            column: 0,
            index,
        };

        Ok(XmlNode {
            name,
            attributes,
            text,
            children,
            position,
        })
    }
}

impl XmlNode {
    /// 查找具有指定名称的子节点
    pub fn find_child(&self, name: &str) -> Option<&XmlNode> {
        self.children.iter().find(|child| child.name == name)
    }

    /// 查找所有具有指定名称的子节点
    pub fn find_children(&self, name: &str) -> Vec<&XmlNode> {
        self.children.iter().filter(|child| child.name == name).collect()
    }

    /// 获取指定属性的值
    pub fn get_attribute(&self, name: &str) -> Option<&String> {
        self.attributes.get(name)
    }

    /// 检查是否具有指定属性
    pub fn has_attribute(&self, name: &str) -> bool {
        self.attributes.contains_key(name)
    }

    /// 递归查找所有匹配名称的节点
    pub fn find_all_nodes(&self, name: &str) -> Vec<&XmlNode> {
        let mut result = Vec::new();
        self.find_all_nodes_recursive(name, &mut result);
        result
    }

    /// 递归查找节点的辅助方法
    fn find_all_nodes_recursive(&self, name: &str, result: &mut Vec<&XmlNode>) {
        if self.name == name {
            result.push(self);
        }
        for child in &self.children {
            child.find_all_nodes_recursive(name, result);
        }
    }

    /// 获取节点的深度
    pub fn depth(&self) -> usize {
        if self.children.is_empty() {
            1
        } else {
            1 + self.children.iter().map(|child| child.depth()).max().unwrap_or(0)
        }
    }

    /// 获取节点的文本内容（包括子节点）
    pub fn get_text_content(&self) -> String {
        let mut content = String::new();
        if let Some(text) = &self.text {
            content.push_str(text);
        }
        for child in &self.children {
            content.push_str(&child.get_text_content());
        }
        content
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::ParserConfig;

    #[test]
    fn test_xml_parser_creation() {
        let config = ParserConfig::default();
        let parser = XmlParser::new(&config);
        assert_eq!(parser.config.max_file_size, config.max_file_size);
    }

    #[test]
    fn test_parse_simple_xml() {
        let config = ParserConfig::default();
        let parser = XmlParser::new(&config);
        
        let xml = r#"<?xml version="1.0" encoding="UTF-8"?>
<root>
    <data speed="10.5" time="1.0">Test</data>
</root>"#;

        let result = parser.parse(xml);
        assert!(result.is_ok());
        
        let parsed = result.unwrap();
        assert_eq!(parsed.root.name, "root");
        assert_eq!(parsed.root.children.len(), 1);
        assert_eq!(parsed.root.children[0].name, "data");
        assert_eq!(parsed.root.children[0].get_attribute("speed"), Some(&"10.5".to_string()));
    }

    #[test]
    fn test_parse_invalid_xml() {
        let config = ParserConfig::default();
        let parser = XmlParser::new(&config);
        
        let xml = r#"<root><unclosed>"#;
        let result = parser.parse(xml);
        assert!(result.is_err());
    }

    #[test]
    fn test_file_size_limit() {
        let mut config = ParserConfig::default();
        config.max_file_size = 10; // 很小的限制
        let parser = XmlParser::new(&config);
        
        let xml = r#"<?xml version="1.0"?><root>This is a long XML content</root>"#;
        let result = parser.parse(xml);
        assert!(result.is_err());
    }

    #[test]
    fn test_node_methods() {
        let config = ParserConfig::default();
        let parser = XmlParser::new(&config);
        
        let xml = r#"<root>
            <data speed="10.5">
                <nested>value</nested>
            </data>
            <data speed="15.2"/>
        </root>"#;

        let parsed = parser.parse(xml).unwrap();
        let root = &parsed.root;
        
        // 测试查找子节点
        let data_nodes = root.find_children("data");
        assert_eq!(data_nodes.len(), 2);
        
        // 测试属性访问
        assert!(data_nodes[0].has_attribute("speed"));
        assert_eq!(data_nodes[0].get_attribute("speed"), Some(&"10.5".to_string()));
        
        // 测试递归查找
        let all_data = root.find_all_nodes("data");
        assert_eq!(all_data.len(), 2);
    }
}

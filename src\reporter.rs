//! 报告生成模块
//! 
//! 生成XML检测结果的各种格式报告

use crate::config::{ReporterConfig, OutputFormat};
use crate::detector::DetectionResults;
use crate::error::{Result, XmlCheckError};
use crate::parser::ParsedXml;
use crate::validator::ValidationResults;
use serde::{Deserialize, Serialize};
use std::path::Path;

/// 报告生成器
pub struct ReportGenerator {
    config: ReporterConfig,
}

/// 检测报告
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Report {
    /// 报告元数据
    pub metadata: ReportMetadata,
    /// 文件信息
    pub file_info: Option<FileInfo>,
    /// 检测结果摘要
    pub summary: ReportSummary,
    /// 详细检测结果
    pub detection_results: Option<DetectionResults>,
    /// 详细验证结果
    pub validation_results: Option<ValidationResults>,
    /// 建议和推荐
    pub recommendations: Vec<Recommendation>,
}

/// 报告元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReportMetadata {
    /// 报告生成时间
    pub generated_at: String,
    /// 工具版本
    pub tool_version: String,
    /// 报告格式
    pub format: OutputFormat,
    /// 报告语言
    pub language: String,
}

/// 文件信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileInfo {
    /// 文件路径
    pub file_path: String,
    /// 文件大小（字节）
    pub file_size: usize,
    /// 文件修改时间
    pub modified_at: Option<String>,
    /// XML版本
    pub xml_version: String,
    /// XML编码
    pub xml_encoding: String,
}

/// 报告摘要
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReportSummary {
    /// 整体状态
    pub overall_status: OverallStatus,
    /// 发现的速度数据点数量
    pub speed_data_points_count: usize,
    /// 检测到的异常数量
    pub anomalies_count: usize,
    /// 验证错误数量
    pub validation_errors_count: usize,
    /// 验证警告数量
    pub validation_warnings_count: usize,
    /// 处理耗时（毫秒）
    pub processing_time_ms: u64,
}

/// 整体状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum OverallStatus {
    /// 通过 - 无错误
    Pass,
    /// 警告 - 有警告但无错误
    Warning,
    /// 失败 - 有错误
    Fail,
    /// 错误 - 处理过程中出现错误
    Error,
}

/// 建议和推荐
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Recommendation {
    /// 建议类型
    pub recommendation_type: RecommendationType,
    /// 建议标题
    pub title: String,
    /// 建议描述
    pub description: String,
    /// 优先级
    pub priority: Priority,
    /// 相关位置
    pub related_locations: Vec<String>,
}

/// 建议类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RecommendationType {
    /// 数据质量改进
    DataQuality,
    /// 性能优化
    Performance,
    /// 格式标准化
    Formatting,
    /// 安全性
    Security,
    /// 最佳实践
    BestPractice,
}

/// 优先级
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Priority {
    Low,
    Medium,
    High,
    Critical,
}

impl ReportGenerator {
    /// 创建新的报告生成器
    pub fn new(config: &ReporterConfig) -> Self {
        Self {
            config: config.clone(),
        }
    }

    /// 生成完整报告
    pub fn generate(
        &self,
        file_path: Option<&Path>,
        parsed_xml: &ParsedXml,
        detection_results: &DetectionResults,
        validation_results: &ValidationResults,
    ) -> Result<Report> {
        let metadata = self.create_metadata();
        let file_info = self.create_file_info(file_path, parsed_xml)?;
        let summary = self.create_summary(detection_results, validation_results);
        let recommendations = self.generate_recommendations(detection_results, validation_results);

        let detection_results = if self.config.include_details {
            Some(detection_results.clone())
        } else {
            None
        };

        let validation_results = if self.config.include_details {
            Some(validation_results.clone())
        } else {
            None
        };

        Ok(Report {
            metadata,
            file_info,
            summary,
            detection_results,
            validation_results,
            recommendations,
        })
    }

    /// 创建错误报告
    pub fn error(file_path: Option<&Path>, error_message: String) -> Report {
        let metadata = ReportMetadata {
            generated_at: "2024-01-01T00:00:00Z".to_string(), // 简化时间处理
            tool_version: env!("CARGO_PKG_VERSION").to_string(),
            format: OutputFormat::Json,
            language: "zh-CN".to_string(),
        };

        let file_info = file_path.map(|path| FileInfo {
            file_path: path.to_string_lossy().to_string(),
            file_size: 0,
            modified_at: None,
            xml_version: "unknown".to_string(),
            xml_encoding: "unknown".to_string(),
        });

        let summary = ReportSummary {
            overall_status: OverallStatus::Error,
            speed_data_points_count: 0,
            anomalies_count: 0,
            validation_errors_count: 1,
            validation_warnings_count: 0,
            processing_time_ms: 0,
        };

        let recommendations = vec![Recommendation {
            recommendation_type: RecommendationType::DataQuality,
            title: "修复处理错误".to_string(),
            description: error_message,
            priority: Priority::Critical,
            related_locations: vec![],
        }];

        Report {
            metadata,
            file_info,
            summary,
            detection_results: None,
            validation_results: None,
            recommendations,
        }
    }

    /// 创建报告元数据
    fn create_metadata(&self) -> ReportMetadata {
        ReportMetadata {
            generated_at: "2024-01-01T00:00:00Z".to_string(), // 简化时间处理
            tool_version: env!("CARGO_PKG_VERSION").to_string(),
            format: self.config.output_format.clone(),
            language: self.config.language.clone(),
        }
    }

    /// 创建文件信息
    fn create_file_info(&self, file_path: Option<&Path>, parsed_xml: &ParsedXml) -> Result<Option<FileInfo>> {
        if let Some(path) = file_path {
            let metadata = std::fs::metadata(path)?;
            let modified_at = metadata
                .modified()
                .ok()
                .map(|_| "2024-01-01T00:00:00Z".to_string()); // 简化时间处理

            Ok(Some(FileInfo {
                file_path: path.to_string_lossy().to_string(),
                file_size: parsed_xml.metadata.size,
                modified_at,
                xml_version: parsed_xml.metadata.version.clone(),
                xml_encoding: parsed_xml.metadata.encoding.clone(),
            }))
        } else {
            Ok(None)
        }
    }

    /// 创建报告摘要
    fn create_summary(
        &self,
        detection_results: &DetectionResults,
        validation_results: &ValidationResults,
    ) -> ReportSummary {
        let overall_status = if !validation_results.is_valid {
            OverallStatus::Fail
        } else if !validation_results.validation_warnings.is_empty() {
            OverallStatus::Warning
        } else {
            OverallStatus::Pass
        };

        let processing_time_ms = detection_results.statistics.detection_time_ms
            + validation_results.statistics.validation_time_ms;

        ReportSummary {
            overall_status,
            speed_data_points_count: detection_results.speed_data_points.len(),
            anomalies_count: detection_results.speed_analysis.anomalies.len(),
            validation_errors_count: validation_results.validation_errors.len(),
            validation_warnings_count: validation_results.validation_warnings.len(),
            processing_time_ms,
        }
    }

    /// 生成建议和推荐
    fn generate_recommendations(
        &self,
        detection_results: &DetectionResults,
        validation_results: &ValidationResults,
    ) -> Vec<Recommendation> {
        let mut recommendations = Vec::new();

        // 基于异常生成建议
        if !detection_results.speed_analysis.anomalies.is_empty() {
            recommendations.push(Recommendation {
                recommendation_type: RecommendationType::DataQuality,
                title: "检查速度数据异常".to_string(),
                description: format!(
                    "发现 {} 个速度数据异常，建议检查数据来源和采集过程",
                    detection_results.speed_analysis.anomalies.len()
                ),
                priority: Priority::High,
                related_locations: detection_results
                    .speed_analysis
                    .anomalies
                    .iter()
                    .map(|a| detection_results.speed_data_points[a.data_point_index].node_path.clone())
                    .collect(),
            });
        }

        // 基于验证错误生成建议
        if !validation_results.validation_errors.is_empty() {
            recommendations.push(Recommendation {
                recommendation_type: RecommendationType::DataQuality,
                title: "修复数据验证错误".to_string(),
                description: format!(
                    "发现 {} 个数据验证错误，需要修复以确保数据质量",
                    validation_results.validation_errors.len()
                ),
                priority: Priority::Critical,
                related_locations: validation_results
                    .validation_errors
                    .iter()
                    .map(|e| e.location.node_path.clone())
                    .collect(),
            });
        }

        // 性能建议
        if detection_results.speed_data_points.len() > 10000 {
            recommendations.push(Recommendation {
                recommendation_type: RecommendationType::Performance,
                title: "考虑数据分批处理".to_string(),
                description: "数据点数量较大，建议考虑分批处理以提高性能".to_string(),
                priority: Priority::Medium,
                related_locations: vec![],
            });
        }

        recommendations
    }

    /// 将报告序列化为指定格式
    pub fn serialize_report(&self, report: &Report) -> Result<String> {
        match self.config.output_format {
            OutputFormat::Json => {
                serde_json::to_string_pretty(report)
                    .map_err(|e| XmlCheckError::report(format!("JSON serialization failed: {}", e)))
            }
            OutputFormat::Xml => {
                serde_xml_rs::to_string(report)
                    .map_err(|e| XmlCheckError::report(format!("XML serialization failed: {}", e)))
            }
            OutputFormat::Text => Ok(self.format_as_text(report)),
            OutputFormat::Html => Ok(self.format_as_html(report)),
            OutputFormat::Csv => Ok(self.format_as_csv(report)),
        }
    }

    /// 格式化为文本
    fn format_as_text(&self, report: &Report) -> String {
        let mut output = String::new();
        
        output.push_str("=== XML变速数据检测报告 ===\n\n");
        
        // 基本信息
        if let Some(file_info) = &report.file_info {
            output.push_str(&format!("文件: {}\n", file_info.file_path));
            output.push_str(&format!("大小: {} 字节\n", file_info.file_size));
        }
        
        output.push_str(&format!("生成时间: {}\n", report.metadata.generated_at));
        output.push_str(&format!("工具版本: {}\n\n", report.metadata.tool_version));
        
        // 摘要
        output.push_str("=== 检测摘要 ===\n");
        output.push_str(&format!("状态: {:?}\n", report.summary.overall_status));
        output.push_str(&format!("速度数据点: {}\n", report.summary.speed_data_points_count));
        output.push_str(&format!("异常数量: {}\n", report.summary.anomalies_count));
        output.push_str(&format!("验证错误: {}\n", report.summary.validation_errors_count));
        output.push_str(&format!("验证警告: {}\n", report.summary.validation_warnings_count));
        output.push_str(&format!("处理时间: {} ms\n\n", report.summary.processing_time_ms));
        
        // 建议
        if !report.recommendations.is_empty() {
            output.push_str("=== 建议和推荐 ===\n");
            for (i, rec) in report.recommendations.iter().enumerate() {
                output.push_str(&format!("{}. {} (优先级: {:?})\n", i + 1, rec.title, rec.priority));
                output.push_str(&format!("   {}\n\n", rec.description));
            }
        }
        
        output
    }

    /// 格式化为HTML
    fn format_as_html(&self, report: &Report) -> String {
        format!(
            r#"<!DOCTYPE html>
<html>
<head>
    <title>XML变速数据检测报告</title>
    <meta charset="UTF-8">
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 10px; border-radius: 5px; }}
        .summary {{ margin: 20px 0; }}
        .status-pass {{ color: green; }}
        .status-warning {{ color: orange; }}
        .status-fail {{ color: red; }}
        .status-error {{ color: darkred; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>XML变速数据检测报告</h1>
        <p>生成时间: {}</p>
        <p>工具版本: {}</p>
    </div>
    
    <div class="summary">
        <h2>检测摘要</h2>
        <p>状态: <span class="status-{}">{:?}</span></p>
        <p>速度数据点: {}</p>
        <p>异常数量: {}</p>
        <p>验证错误: {}</p>
        <p>验证警告: {}</p>
        <p>处理时间: {} ms</p>
    </div>
</body>
</html>"#,
            report.metadata.generated_at,
            report.metadata.tool_version,
            match report.summary.overall_status {
                OverallStatus::Pass => "pass",
                OverallStatus::Warning => "warning", 
                OverallStatus::Fail => "fail",
                OverallStatus::Error => "error",
            },
            report.summary.overall_status,
            report.summary.speed_data_points_count,
            report.summary.anomalies_count,
            report.summary.validation_errors_count,
            report.summary.validation_warnings_count,
            report.summary.processing_time_ms
        )
    }

    /// 格式化为CSV
    fn format_as_csv(&self, report: &Report) -> String {
        let mut output = String::new();
        output.push_str("指标,数值\n");
        output.push_str(&format!("状态,{:?}\n", report.summary.overall_status));
        output.push_str(&format!("速度数据点,{}\n", report.summary.speed_data_points_count));
        output.push_str(&format!("异常数量,{}\n", report.summary.anomalies_count));
        output.push_str(&format!("验证错误,{}\n", report.summary.validation_errors_count));
        output.push_str(&format!("验证警告,{}\n", report.summary.validation_warnings_count));
        output.push_str(&format!("处理时间(ms),{}\n", report.summary.processing_time_ms));
        output
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::ReporterConfig;

    #[test]
    fn test_report_generator_creation() {
        let config = ReporterConfig::default();
        let generator = ReportGenerator::new(&config);
        assert_eq!(generator.config.language, "zh-CN");
    }

    #[test]
    fn test_error_report_creation() {
        let error_report = ReportGenerator::error(None, "Test error".to_string());
        assert!(matches!(error_report.summary.overall_status, OverallStatus::Error));
        assert_eq!(error_report.recommendations.len(), 1);
    }

    #[test]
    fn test_text_formatting() {
        let config = ReporterConfig::default();
        let generator = ReportGenerator::new(&config);
        let error_report = ReportGenerator::error(None, "Test error".to_string());
        
        let text_output = generator.format_as_text(&error_report);
        assert!(text_output.contains("XML变速数据检测报告"));
        assert!(text_output.contains("Test error"));
    }
}

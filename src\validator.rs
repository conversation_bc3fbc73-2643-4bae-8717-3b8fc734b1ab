//! 数据验证模块
//! 
//! 验证XML数据的完整性、一致性和有效性

use crate::config::{ValidatorConfig, ValidationRule, DataType};
use crate::detector::DetectionResults;
use crate::error::{Result, XmlCheckError};
use crate::parser::{ParsedXml, XmlNode};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 数据验证器
pub struct DataValidator {
    config: ValidatorConfig,
}

/// 验证结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationResults {
    /// 验证是否通过
    pub is_valid: bool,
    /// 验证错误列表
    pub validation_errors: Vec<ValidationError>,
    /// 验证警告列表
    pub validation_warnings: Vec<ValidationWarning>,
    /// 验证统计信息
    pub statistics: ValidationStatistics,
}

/// 验证错误
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ValidationError {
    /// 错误类型
    pub error_type: ValidationErrorType,
    /// 错误描述
    pub description: String,
    /// 错误位置
    pub location: ErrorLocation,
    /// 严重程度
    pub severity: ErrorSeverity,
    /// 建议修复方案
    pub suggested_fix: Option<String>,
}

/// 验证警告
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationWarning {
    /// 警告类型
    pub warning_type: ValidationWarningType,
    /// 警告描述
    pub description: String,
    /// 警告位置
    pub location: ErrorLocation,
}

/// 验证错误类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ValidationErrorType {
    /// 数据完整性错误
    IntegrityError,
    /// 数据范围错误
    RangeError,
    /// 数据一致性错误
    ConsistencyError,
    /// 自定义规则错误
    CustomRuleError,
    /// 数据类型错误
    DataTypeError,
    /// 必需字段缺失
    MissingRequiredField,
}

/// 验证警告类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ValidationWarningType {
    /// 数据质量警告
    DataQuality,
    /// 性能警告
    Performance,
    /// 格式警告
    Format,
    /// 建议优化
    Optimization,
}

/// 错误严重程度
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ErrorSeverity {
    Low,
    Medium,
    High,
    Critical,
}

/// 错误位置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorLocation {
    /// 节点路径
    pub node_path: String,
    /// 行号
    pub line: usize,
    /// 列号
    pub column: usize,
    /// 属性名（如果适用）
    pub attribute: Option<String>,
}

/// 验证统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationStatistics {
    /// 总验证规则数
    pub total_rules: usize,
    /// 通过的规则数
    pub passed_rules: usize,
    /// 失败的规则数
    pub failed_rules: usize,
    /// 总错误数
    pub total_errors: usize,
    /// 总警告数
    pub total_warnings: usize,
    /// 验证耗时（毫秒）
    pub validation_time_ms: u64,
}

impl DataValidator {
    /// 创建新的数据验证器
    pub fn new(config: &ValidatorConfig) -> Self {
        Self {
            config: config.clone(),
        }
    }

    /// 验证XML数据
    pub fn validate(
        &self,
        parsed_xml: &ParsedXml,
        detection_results: &DetectionResults,
    ) -> Result<ValidationResults> {
        let start_time = std::time::Instant::now();
        
        let mut validation_errors = Vec::new();
        let mut validation_warnings = Vec::new();
        let mut total_rules = 0;
        let mut passed_rules = 0;

        // 数据完整性检查
        if self.config.enable_integrity_check {
            total_rules += 1;
            match self.validate_data_integrity(parsed_xml, detection_results) {
                Ok(warnings) => {
                    passed_rules += 1;
                    validation_warnings.extend(warnings);
                }
                Err(errors) => {
                    validation_errors.extend(errors);
                }
            }
        }

        // 数据范围检查
        if self.config.enable_range_check {
            total_rules += 1;
            match self.validate_data_ranges(detection_results) {
                Ok(warnings) => {
                    passed_rules += 1;
                    validation_warnings.extend(warnings);
                }
                Err(errors) => {
                    validation_errors.extend(errors);
                }
            }
        }

        // 数据一致性检查
        if self.config.enable_consistency_check {
            total_rules += 1;
            match self.validate_data_consistency(detection_results) {
                Ok(warnings) => {
                    passed_rules += 1;
                    validation_warnings.extend(warnings);
                }
                Err(errors) => {
                    validation_errors.extend(errors);
                }
            }
        }

        // 自定义规则验证
        for rule in &self.config.custom_rules {
            total_rules += 1;
            match self.validate_custom_rule(parsed_xml, rule) {
                Ok(_) => passed_rules += 1,
                Err(error) => validation_errors.push(error),
            }
        }

        let validation_time_ms = start_time.elapsed().as_millis() as u64;
        let failed_rules = total_rules - passed_rules;

        let statistics = ValidationStatistics {
            total_rules,
            passed_rules,
            failed_rules,
            total_errors: validation_errors.len(),
            total_warnings: validation_warnings.len(),
            validation_time_ms,
        };

        let is_valid = validation_errors.is_empty();

        Ok(ValidationResults {
            is_valid,
            validation_errors,
            validation_warnings,
            statistics,
        })
    }

    /// 验证数据完整性
    fn validate_data_integrity(
        &self,
        parsed_xml: &ParsedXml,
        detection_results: &DetectionResults,
    ) -> std::result::Result<Vec<ValidationWarning>, Vec<ValidationError>> {
        let mut warnings = Vec::new();
        let mut errors = Vec::new();

        // 检查是否有速度数据
        if detection_results.speed_data_points.is_empty() {
            errors.push(ValidationError {
                error_type: ValidationErrorType::IntegrityError,
                description: "No speed data found in XML".to_string(),
                location: ErrorLocation {
                    node_path: "/".to_string(),
                    line: 0,
                    column: 0,
                    attribute: None,
                },
                severity: ErrorSeverity::High,
                suggested_fix: Some("Add speed data elements with speed attributes".to_string()),
            });
        }

        // 检查数据点的完整性
        for (i, data_point) in detection_results.speed_data_points.iter().enumerate() {
            if data_point.time.is_none() {
                warnings.push(ValidationWarning {
                    warning_type: ValidationWarningType::DataQuality,
                    description: format!("Speed data point {} missing time information", i),
                    location: ErrorLocation {
                        node_path: data_point.node_path.clone(),
                        line: data_point.position.line,
                        column: data_point.position.column,
                        attribute: None,
                    },
                });
            }
        }

        if errors.is_empty() {
            Ok(warnings)
        } else {
            Err(errors)
        }
    }

    /// 验证数据范围
    fn validate_data_ranges(
        &self,
        detection_results: &DetectionResults,
    ) -> std::result::Result<Vec<ValidationWarning>, Vec<ValidationError>> {
        let mut warnings = Vec::new();
        let mut errors = Vec::new();

        // 检查速度异常
        for anomaly in &detection_results.speed_analysis.anomalies {
            let data_point = &detection_results.speed_data_points[anomaly.data_point_index];
            
            let error = ValidationError {
                error_type: ValidationErrorType::RangeError,
                description: anomaly.description.clone(),
                location: ErrorLocation {
                    node_path: data_point.node_path.clone(),
                    line: data_point.position.line,
                    column: data_point.position.column,
                    attribute: Some("speed".to_string()),
                },
                severity: match anomaly.severity {
                    crate::detector::AnomalySeverity::Low => ErrorSeverity::Low,
                    crate::detector::AnomalySeverity::Medium => ErrorSeverity::Medium,
                    crate::detector::AnomalySeverity::High => ErrorSeverity::High,
                    crate::detector::AnomalySeverity::Critical => ErrorSeverity::Critical,
                },
                suggested_fix: Some("Check and correct the speed value".to_string()),
            };

            match error.severity {
                ErrorSeverity::Low | ErrorSeverity::Medium => {
                    warnings.push(ValidationWarning {
                        warning_type: ValidationWarningType::DataQuality,
                        description: error.description.clone(),
                        location: error.location.clone(),
                    });
                }
                ErrorSeverity::High | ErrorSeverity::Critical => {
                    errors.push(error);
                }
            }
        }

        if errors.is_empty() {
            Ok(warnings)
        } else {
            Err(errors)
        }
    }

    /// 验证数据一致性
    fn validate_data_consistency(
        &self,
        detection_results: &DetectionResults,
    ) -> std::result::Result<Vec<ValidationWarning>, Vec<ValidationError>> {
        let mut warnings = Vec::new();
        let mut errors = Vec::new();

        // 检查时间序列的一致性
        let mut time_points: Vec<(usize, f64)> = detection_results
            .speed_data_points
            .iter()
            .enumerate()
            .filter_map(|(i, p)| p.time.map(|t| (i, t)))
            .collect();

        time_points.sort_by(|a, b| a.1.partial_cmp(&b.1).unwrap());

        // 检查时间是否单调递增
        for window in time_points.windows(2) {
            if window[0].1 >= window[1].1 {
                let data_point = &detection_results.speed_data_points[window[1].0];
                errors.push(ValidationError {
                    error_type: ValidationErrorType::ConsistencyError,
                    description: "Time values are not in ascending order".to_string(),
                    location: ErrorLocation {
                        node_path: data_point.node_path.clone(),
                        line: data_point.position.line,
                        column: data_point.position.column,
                        attribute: Some("time".to_string()),
                    },
                    severity: ErrorSeverity::Medium,
                    suggested_fix: Some("Ensure time values are in chronological order".to_string()),
                });
            }
        }

        // 检查速度变化的合理性
        for change in &detection_results.speed_analysis.speed_changes {
            if change.change_percentage > 200.0 {
                let data_point = &detection_results.speed_data_points[change.to_index];
                warnings.push(ValidationWarning {
                    warning_type: ValidationWarningType::DataQuality,
                    description: format!(
                        "Extreme speed change: {}% from {} to {}",
                        change.change_percentage, change.from_speed, change.to_speed
                    ),
                    location: ErrorLocation {
                        node_path: data_point.node_path.clone(),
                        line: data_point.position.line,
                        column: data_point.position.column,
                        attribute: Some("speed".to_string()),
                    },
                });
            }
        }

        if errors.is_empty() {
            Ok(warnings)
        } else {
            Err(errors)
        }
    }

    /// 验证自定义规则
    fn validate_custom_rule(
        &self,
        parsed_xml: &ParsedXml,
        rule: &ValidationRule,
    ) -> std::result::Result<(), ValidationError> {
        // 简化的XPath实现 - 实际应用中可能需要更完整的XPath支持
        let nodes = self.find_nodes_by_simple_xpath(&parsed_xml.root, &rule.xpath);

        if rule.required && nodes.is_empty() {
            return Err(ValidationError {
                error_type: ValidationErrorType::MissingRequiredField,
                description: format!("Required field not found: {}", rule.description),
                location: ErrorLocation {
                    node_path: rule.xpath.clone(),
                    line: 0,
                    column: 0,
                    attribute: None,
                },
                severity: ErrorSeverity::High,
                suggested_fix: Some(format!("Add required field: {}", rule.xpath)),
            });
        }

        // 验证数据类型
        for node in nodes {
            if let Err(error) = self.validate_node_data_type(node, &rule.expected_type, rule) {
                return Err(error);
            }
        }

        Ok(())
    }

    /// 简化的XPath节点查找
    fn find_nodes_by_simple_xpath(&self, root: &XmlNode, xpath: &str) -> Vec<&XmlNode> {
        // 这是一个简化的实现，实际应用中可能需要完整的XPath解析器
        if xpath.starts_with("//") {
            let element_name = xpath.trim_start_matches("//");
            root.find_all_nodes(element_name)
        } else {
            // 简单的路径查找
            vec![root] // 简化实现
        }
    }

    /// 验证节点数据类型
    fn validate_node_data_type(
        &self,
        node: &XmlNode,
        expected_type: &DataType,
        rule: &ValidationRule,
    ) -> std::result::Result<(), ValidationError> {
        let text_content = node.get_text_content();
        
        let is_valid = match expected_type {
            DataType::String => true, // 字符串总是有效的
            DataType::Integer => text_content.trim().parse::<i64>().is_ok(),
            DataType::Float => text_content.trim().parse::<f64>().is_ok(),
            DataType::Boolean => {
                let lower = text_content.trim().to_lowercase();
                lower == "true" || lower == "false" || lower == "1" || lower == "0"
            }
            DataType::DateTime => {
                // 简化的日期时间验证
                !text_content.trim().is_empty()
            }
        };

        if !is_valid {
            return Err(ValidationError {
                error_type: ValidationErrorType::DataTypeError,
                description: format!(
                    "Invalid data type for rule '{}': expected {:?}, got '{}'",
                    rule.name, expected_type, text_content
                ),
                location: ErrorLocation {
                    node_path: format!("/{}", node.name),
                    line: node.position.line,
                    column: node.position.column,
                    attribute: None,
                },
                severity: ErrorSeverity::Medium,
                suggested_fix: Some(format!("Convert value to {:?} type", expected_type)),
            });
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::{ValidatorConfig, DetectorConfig};
    use crate::detector::VariableSpeedDetector;
    use crate::parser::XmlParser;

    #[test]
    fn test_validator_creation() {
        let config = ValidatorConfig::default();
        let validator = DataValidator::new(&config);
        assert!(validator.config.enable_integrity_check);
    }

    #[test]
    fn test_validation_with_valid_data() {
        let validator_config = ValidatorConfig::default();
        let validator = DataValidator::new(&validator_config);
        
        let detector_config = DetectorConfig::default();
        let detector = VariableSpeedDetector::new(&detector_config);
        
        let parser_config = crate::config::ParserConfig::default();
        let parser = XmlParser::new(&parser_config);
        
        let xml = r#"<root>
            <data speed="10.5" time="1.0"/>
            <data speed="15.2" time="2.0"/>
        </root>"#;

        let parsed = parser.parse(xml).unwrap();
        let detection_results = detector.detect(&parsed).unwrap();
        let validation_results = validator.validate(&parsed, &detection_results).unwrap();
        
        assert!(validation_results.is_valid);
        assert_eq!(validation_results.validation_errors.len(), 0);
    }

    #[test]
    fn test_validation_with_missing_data() {
        let validator_config = ValidatorConfig::default();
        let validator = DataValidator::new(&validator_config);
        
        let detector_config = DetectorConfig::default();
        let detector = VariableSpeedDetector::new(&detector_config);
        
        let parser_config = crate::config::ParserConfig::default();
        let parser = XmlParser::new(&parser_config);
        
        let xml = r#"<root><empty/></root>"#;

        let parsed = parser.parse(xml).unwrap();
        let detection_results = detector.detect(&parsed).unwrap();
        let validation_results = validator.validate(&parsed, &detection_results).unwrap();
        
        assert!(!validation_results.is_valid);
        assert!(validation_results.validation_errors.len() > 0);
    }
}

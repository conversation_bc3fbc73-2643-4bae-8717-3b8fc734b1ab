<?xml version="1.0" encoding="UTF-8"?>
<test_data>
    <header>
        <test_id>ANOMALY_TEST_001</test_id>
        <description>Test data with various anomalies for validation</description>
    </header>
    
    <speed_records>
        <!-- Normal data points -->
        <record speed="15.0" time="1.0"/>
        <record speed="18.5" time="2.0"/>
        <record speed="22.1" time="3.0"/>
        
        <!-- Sudden speed jump (anomaly) -->
        <record speed="85.7" time="4.0"/>
        
        <!-- Back to normal -->
        <record speed="25.3" time="5.0"/>
        <record speed="28.9" time="6.0"/>
        
        <!-- Negative speed (anomaly) -->
        <record speed="-5.2" time="7.0"/>
        
        <!-- Extremely high speed (anomaly) -->
        <record speed="1250.0" time="8.0"/>
        
        <!-- Missing time attribute (anomaly) -->
        <record speed="30.0"/>
        
        <!-- Invalid speed format (anomaly) -->
        <record speed="invalid" time="10.0"/>
        
        <!-- Normal continuation -->
        <record speed="32.1" time="11.0"/>
        <record speed="35.8" time="12.0"/>
        
        <!-- Time sequence error (anomaly) -->
        <record speed="38.2" time="10.5"/>
        
        <!-- Zero speed -->
        <record speed="0.0" time="13.0"/>
    </speed_records>
    
    <velocity_data>
        <!-- Different attribute name for speed -->
        <measurement velocity="45.2" timestamp="1.0"/>
        <measurement velocity="48.7" timestamp="2.0"/>
        <measurement velocity="52.1" timestamp="3.0"/>
        
        <!-- Missing velocity (anomaly) -->
        <measurement timestamp="4.0"/>
        
        <!-- Extreme deceleration (anomaly) -->
        <measurement velocity="5.3" timestamp="5.0"/>
    </velocity_data>
    
    <rate_measurements>
        <!-- Another speed attribute variant -->
        <point rate="25.0" t="1.0"/>
        <point rate="27.5" t="2.0"/>
        <point rate="30.1" t="3.0"/>
        
        <!-- Inconsistent units or scale (potential anomaly) -->
        <point rate="2500.0" t="4.0"/>
    </rate_measurements>
</test_data>

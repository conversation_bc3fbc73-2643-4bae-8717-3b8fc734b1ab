//! 集成测试
//! 
//! 测试整个XML检测流程

use std::fs;
use tempfile::NamedTempFile;
use xml_check::{config::Config, XmlChe<PERSON>};

#[test]
fn test_basic_xml_detection() {
    let xml_content = r#"<?xml version="1.0" encoding="UTF-8"?>
<root>
    <data speed="10.5" time="1.0"/>
    <data speed="15.2" time="2.0"/>
    <data speed="8.3" time="3.0"/>
</root>"#;

    let checker = XmlChecker::new().expect("Failed to create checker");
    let report = checker.check_xml_content(xml_content, None).expect("Failed to check XML");

    assert_eq!(report.summary.speed_data_points_count, 3);
    assert!(matches!(report.summary.overall_status, xml_check::reporter::OverallStatus::Pass));
}

#[test]
fn test_file_detection() {
    let xml_content = r#"<?xml version="1.0" encoding="UTF-8"?>
<vehicle_data>
    <speed_measurements>
        <data_point speed="25.0" time="1.0"/>
        <data_point speed="30.5" time="2.0"/>
    </speed_measurements>
</vehicle_data>"#;

    let mut temp_file = NamedTempFile::new().expect("Failed to create temp file");
    fs::write(temp_file.path(), xml_content).expect("Failed to write temp file");

    let checker = XmlChecker::new().expect("Failed to create checker");
    let report = checker.check_file(temp_file.path()).expect("Failed to check file");

    assert_eq!(report.summary.speed_data_points_count, 2);
}

#[test]
fn test_anomaly_detection() {
    let xml_content = r#"<?xml version="1.0" encoding="UTF-8"?>
<test_data>
    <record speed="15.0" time="1.0"/>
    <record speed="18.5" time="2.0"/>
    <record speed="1250.0" time="3.0"/>  <!-- Anomaly: too high -->
    <record speed="20.0" time="4.0"/>
</test_data>"#;

    let checker = XmlChecker::new().expect("Failed to create checker");
    let report = checker.check_xml_content(xml_content, None).expect("Failed to check XML");

    assert_eq!(report.summary.speed_data_points_count, 4);
    assert!(report.summary.anomalies_count > 0);
}

#[test]
fn test_validation_errors() {
    let xml_content = r#"<?xml version="1.0" encoding="UTF-8"?>
<empty_data>
    <!-- No speed data -->
</empty_data>"#;

    let checker = XmlChecker::new().expect("Failed to create checker");
    let report = checker.check_xml_content(xml_content, None).expect("Failed to check XML");

    assert_eq!(report.summary.speed_data_points_count, 0);
    assert!(report.summary.validation_errors_count > 0);
    assert!(matches!(report.summary.overall_status, xml_check::reporter::OverallStatus::Fail));
}

#[test]
fn test_custom_config() {
    let mut config = Config::default();
    config.detector.min_speed = 5.0;
    config.detector.max_speed = 100.0;

    let xml_content = r#"<?xml version="1.0" encoding="UTF-8"?>
<data>
    <measurement velocity="2.0" timestamp="1.0"/>  <!-- Below min_speed -->
    <measurement velocity="150.0" timestamp="2.0"/> <!-- Above max_speed -->
</data>"#;

    let checker = XmlChecker::with_config(config);
    let report = checker.check_xml_content(xml_content, None).expect("Failed to check XML");

    assert_eq!(report.summary.speed_data_points_count, 2);
    assert!(report.summary.anomalies_count >= 2); // Both should be anomalies
}

#[test]
fn test_different_speed_attributes() {
    let xml_content = r#"<?xml version="1.0" encoding="UTF-8"?>
<mixed_data>
    <data speed="10.0" time="1.0"/>
    <measurement velocity="15.0" timestamp="2.0"/>
    <point rate="20.0" t="3.0"/>
</mixed_data>"#;

    let checker = XmlChecker::new().expect("Failed to create checker");
    let report = checker.check_xml_content(xml_content, None).expect("Failed to check XML");

    assert_eq!(report.summary.speed_data_points_count, 3);
}

#[test]
fn test_report_serialization() {
    let xml_content = r#"<?xml version="1.0" encoding="UTF-8"?>
<simple>
    <data speed="25.0" time="1.0"/>
</simple>"#;

    let checker = XmlChecker::new().expect("Failed to create checker");
    let report = checker.check_xml_content(xml_content, None).expect("Failed to check XML");

    let generator = xml_check::reporter::ReportGenerator::new(&checker.config().reporter);
    
    // Test JSON serialization
    let json_output = generator.serialize_report(&report).expect("Failed to serialize to JSON");
    assert!(json_output.contains("speed_data_points_count"));
    
    // Test that JSON is valid
    let _: serde_json::Value = serde_json::from_str(&json_output).expect("Invalid JSON output");
}

#[test]
fn test_batch_processing() {
    let xml_files = vec![
        r#"<?xml version="1.0"?><root><data speed="10.0" time="1.0"/></root>"#,
        r#"<?xml version="1.0"?><root><data speed="20.0" time="2.0"/></root>"#,
        r#"<?xml version="1.0"?><root><data speed="30.0" time="3.0"/></root>"#,
    ];

    let mut temp_files = Vec::new();
    for (i, content) in xml_files.iter().enumerate() {
        let mut temp_file = NamedTempFile::new().expect("Failed to create temp file");
        fs::write(temp_file.path(), content).expect("Failed to write temp file");
        temp_files.push(temp_file);
    }

    let file_paths: Vec<_> = temp_files.iter().map(|f| f.path()).collect();
    
    let checker = XmlChecker::new().expect("Failed to create checker");
    let reports = checker.check_files(&file_paths).expect("Failed to check files");

    assert_eq!(reports.len(), 3);
    for report in &reports {
        assert_eq!(report.summary.speed_data_points_count, 1);
    }
}

#[test]
fn test_error_handling() {
    let invalid_xml = r#"<root><unclosed>"#;

    let checker = XmlChecker::new().expect("Failed to create checker");
    let result = checker.check_xml_content(invalid_xml, None);

    assert!(result.is_err());
}

#[test]
fn test_large_file_handling() {
    // Create a large XML with many data points
    let mut xml_content = String::from(r#"<?xml version="1.0" encoding="UTF-8"?><root>"#);
    
    for i in 0..1000 {
        xml_content.push_str(&format!(
            r#"<data speed="{}.0" time="{}"/>"#,
            10 + (i % 50),
            i
        ));
    }
    
    xml_content.push_str("</root>");

    let checker = XmlChecker::new().expect("Failed to create checker");
    let report = checker.check_xml_content(&xml_content, None).expect("Failed to check large XML");

    assert_eq!(report.summary.speed_data_points_count, 1000);
    assert!(report.summary.processing_time_ms > 0);
}

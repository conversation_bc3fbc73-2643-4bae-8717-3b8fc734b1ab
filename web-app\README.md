# 🎬 VFX XML数据分析工具 v2.0

专业的视效XML文件数据分析平台，支持Maya、Blender、Cinema 4D、After Effects等主流VFX软件的XML文件分析。

**✨ 最新功能：**
- 🎯 **专业变速检测**：整段变速、分段变速、速度变化分析
- 📏 **智能缩放检测**：均匀缩放、非均匀缩放、变形警告
- 🎨 **优化显示格式**：清晰的专业分析结果展示
- 🔧 **修复启动问题**：Windows闪退问题完全解决

## ⚡ **一键启动**

### **Windows用户**
- **推荐**：`simple-start.bat`（最稳定，已修复闪退问题）
- **诊断**：`debug-start.bat`（显示详细错误信息）
- **高级**：`ultimate-start.bat`（自动端口检测）

### **macOS用户**
**双击运行**：`启动工具.command`
- 如果提示权限问题，右键选择"打开"

### **Linux用户**
**双击运行**：`启动工具.command`
- 或在终端执行：`chmod +x 启动工具.command && ./启动工具.command`

### **PowerShell用户**
**右键运行**：`启动工具.ps1` → "使用PowerShell运行"

## 🎯 **手动启动**

如果一键启动失败：

1. **安装Node.js**：https://nodejs.org
2. **打开终端**：进入此文件夹
3. **安装依赖**：`npm install`
4. **启动服务器**：`node server.js`
5. **访问应用**：http://localhost:3001

## 🌟 **主要功能**

### **🎯 变速效果检测（Timewarp）**
- **整段变速**：显示"整段变速：2.5倍速"
- **分段变速**：显示"分段变速：共3段"，详细列出每段速度
- **无变速**：明确显示"无变速效果"
- **专业描述**：如"2.5倍速 (加速 150.0%)"、"减速80.0%"

### **📏 缩放效果检测（Resize）**
- **放大检测**：显示"放大：1.5倍（增大50%）"
- **缩小检测**：显示"缩小：0.8倍（减小20%）"
- **无变化**：明确显示"无缩放变化"
- **轴向详情**：X、Y、Z轴独立缩放分析
- **变形警告**：自动检测画面变形风险

### **🎬 专业VFX分析**
- **软件支持**：Maya、Blender、Cinema 4D、After Effects等
- **属性识别**：70+种VFX属性（动画、变换、材质、物理等）
- **智能检测**：自动识别文件类型和软件来源
- **数据分类**：按类型统计和分析VFX数据

### **📁 便捷操作**
- **拖拽上传**：直接拖拽XML文件进行即时分析
- **UNC路径读取**：读取网络共享文件夹中的XML文件
- **多页面支持**：主页、专业分析、诊断、API文档等
- **实时分析**：快速处理和展示分析结果

## 🔗 **功能页面**

启动后可访问：

- **🏠 主页 (拖拽上传)**：http://localhost:3001
- **🎬 专业分析**：http://localhost:3001/demo-stable.html
- **🔍 XML诊断**：http://localhost:3001/xml-diagnostic.html
- **📁 UNC路径读取**：http://localhost:3001/unc-reader.html
- **📚 API文档**：http://localhost:3001/api-docs.html

## 📋 **系统要求**

- **Node.js** 16.0+
- **现代浏览器**（Chrome、Safari、Firefox、Edge）
- **操作系统**：Windows 10+、macOS 10.15+、Linux

## 🔧 **故障排除**

### **常见问题**

**Q: Windows bat文件闪退？**
- 使用 `simple-start.bat`（已修复Unicode问题）
- 右键选择"以管理员身份运行"
- 使用 `debug-start.bat` 查看详细错误

**Q: 双击启动脚本没反应？**
- 确保已安装Node.js
- 尝试不同的启动脚本
- 检查杀毒软件是否阻止

**Q: 提示端口被占用？**
- `ultimate-start.bat`会自动处理端口冲突
- 或使用`force-start.bat`强制启动

**Q: 浏览器无法访问？**
- 确认启动脚本显示"Server starting"
- 使用脚本显示的具体URL地址

### **详细故障排除**
查看：`TROUBLESHOOTING.md`

## 📱 **移动设备访问**

在同一WiFi网络下：
1. 查找电脑IP地址
2. 在移动设备访问：`http://[电脑IP]:端口号`

## 🛑 **停止服务器**

- 在启动窗口按：`Ctrl+C`
- 或直接关闭启动窗口

## 📁 **文件说明**

### **Windows启动脚本**
- `simple-start.bat` - 简单启动（推荐，已修复闪退）
- `debug-start.bat` - 调试启动（显示详细信息）
- `ultimate-start.bat` - 高级启动（自动端口检测）
- `force-start.bat` - 强制启动（备用）

### **其他平台**
- `启动工具.command` - macOS/Linux启动
- `启动工具.ps1` - PowerShell启动

### **核心文件**
- `server.js` - 主服务器文件
- `public/` - 前端页面文件
- `src/` - 后端逻辑代码

## 🎉 **开始使用**

1. **双击启动脚本**
2. **等待自动安装和启动**
3. **访问显示的URL地址**
4. **体验完整的XML分析功能**

**祝您使用愉快！** 🚀

---

**版本**: 2.0.0
**更新日期**: 2025年6月
**兼容性**: Windows, macOS, Linux
**新功能**: 专业变速检测、智能缩放分析、优化显示格式

# 🚀 XML变速数据检测工具

一个功能完整的Web应用程序，用于分析XML文件中的速度数据，检测异常并生成详细报告。

## ⚡ **一键启动**

### **Windows用户**
**双击运行**：`ultimate-start.bat`

### **macOS用户**
**双击运行**：`启动工具.command`
- 如果提示权限问题，右键选择"打开"

### **Linux用户**
**双击运行**：`启动工具.command`
- 或在终端执行：`chmod +x 启动工具.command && ./启动工具.command`

### **PowerShell用户**
**右键运行**：`启动工具.ps1` → "使用PowerShell运行"

## 🎯 **手动启动**

如果一键启动失败：

1. **安装Node.js**：https://nodejs.org
2. **打开终端**：进入此文件夹
3. **安装依赖**：`npm install`
4. **启动服务器**：`node server.js`
5. **访问应用**：http://localhost:3001

## 🌟 **主要功能**

- **XML文件分析**：上传XML文件进行速度数据分析
- **文本输入分析**：直接输入XML内容进行分析
- **UNC路径读取**：读取网络共享文件夹中的XML文件
- **异常检测**：自动识别速度数据中的异常模式
- **可视化报告**：生成详细的分析报告
- **API接口**：提供完整的RESTful API

## 🔗 **功能页面**

启动后可访问：

- **主页**：http://localhost:3001
- **功能演示**：http://localhost:3001/demo-stable.html
- **UNC路径读取**：http://localhost:3001/unc-reader.html
- **API文档**：http://localhost:3001/api-docs.html

## 📋 **系统要求**

- **Node.js** 16.0+
- **现代浏览器**（Chrome、Safari、Firefox、Edge）
- **操作系统**：Windows 10+、macOS 10.15+、Linux

## 🔧 **故障排除**

### **常见问题**

**Q: 双击启动脚本没反应？**
- 确保已安装Node.js
- 右键选择"以管理员身份运行"

**Q: 提示端口被占用？**
- `ultimate-start.bat`会自动处理端口冲突
- 或使用`force-start.bat`强制启动

**Q: 浏览器无法访问？**
- 确认启动脚本显示"Server starting"
- 使用脚本显示的具体URL地址

### **详细故障排除**
查看：`TROUBLESHOOTING.md`

## 📱 **移动设备访问**

在同一WiFi网络下：
1. 查找电脑IP地址
2. 在移动设备访问：`http://[电脑IP]:端口号`

## 🛑 **停止服务器**

- 在启动窗口按：`Ctrl+C`
- 或直接关闭启动窗口

## 📁 **文件说明**

- `ultimate-start.bat` - Windows一键启动（推荐）
- `force-start.bat` - Windows强制启动
- `启动工具.command` - macOS/Linux启动
- `启动工具.ps1` - PowerShell启动
- `server.js` - 主服务器文件
- `public/` - 前端页面文件
- `src/` - 后端逻辑代码

## 🎉 **开始使用**

1. **双击启动脚本**
2. **等待自动安装和启动**
3. **访问显示的URL地址**
4. **体验完整的XML分析功能**

**祝您使用愉快！** 🚀

---

**版本**: 1.0.0  
**兼容性**: Windows, macOS, Linux

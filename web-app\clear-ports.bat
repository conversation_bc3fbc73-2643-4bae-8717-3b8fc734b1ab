@echo off
title Port Cleaner - XML Tool

echo Port Cleaner for XML Tool
echo ==========================
echo.

echo This script will stop all Node.js processes
echo and clear ports 3001-3005 for the XML tool.
echo.

echo WARNING: This will stop ALL Node.js applications!
echo Press any key to continue, or close this window to cancel.
pause >nul

echo.
echo Stopping Node.js processes...

:: Stop all Node.js processes
tasklist /FI "IMAGENAME eq node.exe" 2>nul | find /I "node.exe" >nul
if not errorlevel 1 (
    echo Found Node.js processes, stopping them...
    taskkill /F /IM node.exe >nul 2>&1
    if not errorlevel 1 (
        echo SUCCESS: Node.js processes stopped
    ) else (
        echo WARNING: Some processes might still be running
    )
) else (
    echo INFO: No Node.js processes found
)

echo.
echo Waiting for ports to be released...
timeout /t 3 /nobreak >nul

echo.
echo Checking port status...

:: Check ports 3001-3005
for %%p in (3001 3002 3003 3004 3005) do (
    netstat -an | findstr ":%%p" >nul 2>&1
    if not errorlevel 1 (
        echo Port %%p: STILL IN USE
    ) else (
        echo Port %%p: AVAILABLE
    )
)

echo.
echo Port cleanup completed!
echo.
echo You can now run the XML tool startup scripts:
echo - start-simple.bat
echo - debug-start.bat
echo.

pause

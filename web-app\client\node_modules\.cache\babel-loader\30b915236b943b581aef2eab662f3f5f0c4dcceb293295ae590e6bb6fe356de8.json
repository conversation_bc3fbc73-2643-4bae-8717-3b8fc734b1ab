{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\xmlCheck\\\\web-app\\\\client\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback } from 'react';\nimport { Layout, Typography, Card, Row, Col, message, Spin } from 'antd';\nimport { CloudUploadOutlined, FileTextOutlined, BarChartOutlined, BugOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';\nimport styled from 'styled-components';\nimport FileUploader from './components/FileUploader';\nimport TextEditor from './components/TextEditor';\nimport ResultsDisplay from './components/ResultsDisplay';\nimport ExampleSelector from './components/ExampleSelector';\nimport { analyzeXMLFile, analyzeXMLText } from './services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header,\n  Content,\n  Footer\n} = Layout;\nconst {\n  Title,\n  Paragraph\n} = Typography;\nconst StyledLayout = styled(Layout)`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n`;\n_c = StyledLayout;\nconst StyledHeader = styled(Header)`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.2);\n  \n  .ant-typography {\n    color: white !important;\n    margin: 0;\n  }\n`;\n_c2 = StyledHeader;\nconst StyledContent = styled(Content)`\n  padding: 24px;\n  max-width: 1200px;\n  margin: 0 auto;\n  width: 100%;\n`;\n_c3 = StyledContent;\nconst FeatureCard = styled(Card)`\n  height: 100%;\n  border-radius: 12px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  \n  .ant-card-head {\n    border-bottom: 1px solid rgba(0, 0, 0, 0.06);\n  }\n  \n  .ant-card-body {\n    padding: 24px;\n  }\n`;\n_c4 = FeatureCard;\nconst StatsCard = styled(Card)`\n  text-align: center;\n  border-radius: 8px;\n  border: none;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  \n  .ant-card-body {\n    padding: 16px;\n  }\n`;\n_c5 = StatsCard;\nconst IconWrapper = styled.div`\n  font-size: 24px;\n  margin-bottom: 8px;\n  color: ${props => props.color || '#1890ff'};\n`;\n_c6 = IconWrapper;\nfunction App() {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [results, setResults] = useState(null);\n  // const [activeTab, setActiveTab] = useState('upload');\n\n  const handleAnalyze = useCallback(async (data, type = 'file') => {\n    setLoading(true);\n    try {\n      let response;\n      if (type === 'file') {\n        response = await analyzeXMLFile(data);\n      } else {\n        response = await analyzeXMLText(data.content, data.fileName);\n      }\n      setResults(response.data);\n      message.success(`分析完成！检测到 ${response.data.summary.totalDataPoints} 个数据点`);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('分析失败:', error);\n      message.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || '分析失败，请检查文件格式');\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n  const handleExampleSelect = useCallback(async exampleData => {\n    await handleAnalyze({\n      content: exampleData.content,\n      fileName: exampleData.filename\n    }, 'text');\n  }, [handleAnalyze]);\n  const renderStats = () => {\n    if (!results) return null;\n    const {\n      summary\n    } = results;\n    const getStatusColor = status => {\n      switch (status) {\n        case 'pass':\n          return '#52c41a';\n        case 'warning':\n          return '#faad14';\n        case 'fail':\n          return '#ff4d4f';\n        case 'error':\n          return '#ff7875';\n        default:\n          return '#1890ff';\n      }\n    };\n    const getStatusIcon = status => {\n      switch (status) {\n        case 'pass':\n          return /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 29\n          }, this);\n        case 'warning':\n          return /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 32\n          }, this);\n        case 'fail':\n          return /*#__PURE__*/_jsxDEV(BugOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 29\n          }, this);\n        case 'error':\n          return /*#__PURE__*/_jsxDEV(BugOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 30\n          }, this);\n        default:\n          return /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 25\n          }, this);\n      }\n    };\n    return /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(StatsCard, {\n          children: [/*#__PURE__*/_jsxDEV(IconWrapper, {\n            color: \"#1890ff\",\n            children: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '24px',\n              fontWeight: 'bold',\n              color: '#1890ff'\n            },\n            children: summary.totalDataPoints\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#666'\n            },\n            children: \"\\u6570\\u636E\\u70B9\\u603B\\u6570\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(StatsCard, {\n          children: [/*#__PURE__*/_jsxDEV(IconWrapper, {\n            color: \"#52c41a\",\n            children: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '24px',\n              fontWeight: 'bold',\n              color: '#52c41a'\n            },\n            children: summary.validDataPoints\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#666'\n            },\n            children: \"\\u6709\\u6548\\u6570\\u636E\\u70B9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(StatsCard, {\n          children: [/*#__PURE__*/_jsxDEV(IconWrapper, {\n            color: \"#faad14\",\n            children: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '24px',\n              fontWeight: 'bold',\n              color: '#faad14'\n            },\n            children: summary.anomaliesCount\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#666'\n            },\n            children: \"\\u5F02\\u5E38\\u6570\\u91CF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(StatsCard, {\n          children: [/*#__PURE__*/_jsxDEV(IconWrapper, {\n            color: getStatusColor(summary.overallStatus),\n            children: getStatusIcon(summary.overallStatus)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '18px',\n              fontWeight: 'bold',\n              color: getStatusColor(summary.overallStatus),\n              textTransform: 'uppercase'\n            },\n            children: summary.overallStatus\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#666'\n            },\n            children: \"\\u6574\\u4F53\\u72B6\\u6001\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(StyledLayout, {\n    children: [/*#__PURE__*/_jsxDEV(StyledHeader, {\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        children: \"\\uD83D\\uDE80 XML\\u53D8\\u901F\\u6570\\u636E\\u68C0\\u6D4B\\u5DE5\\u5177\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n        style: {\n          color: 'rgba(255, 255, 255, 0.8)',\n          margin: 0\n        },\n        children: \"\\u667A\\u80FD\\u5206\\u6790XML\\u6587\\u4EF6\\u4E2D\\u7684\\u901F\\u5EA6\\u6570\\u636E\\uFF0C\\u68C0\\u6D4B\\u5F02\\u5E38\\u5E76\\u751F\\u6210\\u8BE6\\u7EC6\\u62A5\\u544A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(StyledContent, {\n      children: [renderStats(), /*#__PURE__*/_jsxDEV(Spin, {\n        spinning: loading,\n        tip: \"\\u6B63\\u5728\\u5206\\u6790XML\\u6570\\u636E...\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [24, 24],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            lg: 12,\n            children: /*#__PURE__*/_jsxDEV(FeatureCard, {\n              title: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(CloudUploadOutlined, {\n                  style: {\n                    marginRight: 8\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 21\n                }, this), \"\\u6587\\u4EF6\\u4E0A\\u4F20\\u5206\\u6790\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 19\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(FileUploader, {\n                onAnalyze: handleAnalyze,\n                disabled: loading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            lg: 12,\n            children: /*#__PURE__*/_jsxDEV(FeatureCard, {\n              title: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(FileTextOutlined, {\n                  style: {\n                    marginRight: 8\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 21\n                }, this), \"\\u6587\\u672C\\u7F16\\u8F91\\u5668\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 19\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(TextEditor, {\n                onAnalyze: handleAnalyze,\n                disabled: loading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            children: /*#__PURE__*/_jsxDEV(FeatureCard, {\n              title: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(BarChartOutlined, {\n                  style: {\n                    marginRight: 8\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 21\n                }, this), \"\\u793A\\u4F8B\\u6587\\u4EF6\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 19\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(ExampleSelector, {\n                onSelect: handleExampleSelect,\n                disabled: loading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), results && /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            children: /*#__PURE__*/_jsxDEV(FeatureCard, {\n              title: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(BarChartOutlined, {\n                  style: {\n                    marginRight: 8\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 23\n                }, this), \"\\u5206\\u6790\\u7ED3\\u679C\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 21\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(ResultsDisplay, {\n                results: results\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {\n      style: {\n        textAlign: 'center',\n        background: 'rgba(255, 255, 255, 0.1)',\n        color: 'rgba(255, 255, 255, 0.8)',\n        borderTop: '1px solid rgba(255, 255, 255, 0.2)'\n      },\n      children: \"XML\\u53D8\\u901F\\u6570\\u636E\\u68C0\\u6D4B\\u5DE5\\u5177 \\xA92024 - \\u57FA\\u4E8E\\u73B0\\u4EE3Web\\u6280\\u672F\\u6784\\u5EFA\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 191,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"zgTOORxyr1N+mP4KpPV7nz+z4Zk=\");\n_c7 = App;\nexport default App;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"StyledLayout\");\n$RefreshReg$(_c2, \"StyledHeader\");\n$RefreshReg$(_c3, \"StyledContent\");\n$RefreshReg$(_c4, \"FeatureCard\");\n$RefreshReg$(_c5, \"StatsCard\");\n$RefreshReg$(_c6, \"IconWrapper\");\n$RefreshReg$(_c7, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "Layout", "Typography", "Card", "Row", "Col", "message", "Spin", "CloudUploadOutlined", "FileTextOutlined", "BarChartOutlined", "BugOutlined", "CheckCircleOutlined", "ExclamationCircleOutlined", "styled", "FileUploader", "TextEditor", "ResultsDisplay", "ExampleSelector", "analyzeXMLFile", "analyzeXMLText", "jsxDEV", "_jsxDEV", "Header", "Content", "Footer", "Title", "Paragraph", "StyledLayout", "_c", "StyledHeader", "_c2", "Styled<PERSON>ontent", "_c3", "FeatureCard", "_c4", "StatsCard", "_c5", "IconWrapper", "div", "props", "color", "_c6", "App", "_s", "loading", "setLoading", "results", "setResults", "handleAnalyze", "data", "type", "response", "content", "fileName", "success", "summary", "totalDataPoints", "error", "_error$response", "_error$response$data", "console", "handleExampleSelect", "exampleData", "filename", "renderStats", "getStatusColor", "status", "getStatusIcon", "_jsxFileName", "lineNumber", "columnNumber", "gutter", "style", "marginBottom", "children", "xs", "sm", "md", "fontSize", "fontWeight", "validDataPoints", "anomaliesCount", "overallStatus", "textTransform", "level", "margin", "spinning", "tip", "lg", "title", "marginRight", "onAnalyze", "disabled", "onSelect", "textAlign", "background", "borderTop", "_c7", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/xmlCheck/web-app/client/src/App.js"], "sourcesContent": ["import React, { useState, useCallback } from 'react';\nimport { Layout, Typography, Card, Row, Col, message, Spin } from 'antd';\nimport { \n  CloudUploadOutlined, \n  FileTextOutlined, \n  BarChartOutlined,\n  BugOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined\n} from '@ant-design/icons';\nimport styled from 'styled-components';\n\nimport FileUploader from './components/FileUploader';\nimport TextEditor from './components/TextEditor';\nimport ResultsDisplay from './components/ResultsDisplay';\nimport ExampleSelector from './components/ExampleSelector';\nimport { analyzeXMLFile, analyzeXMLText } from './services/api';\n\nconst { Header, Content, Footer } = Layout;\nconst { Title, Paragraph } = Typography;\n\nconst StyledLayout = styled(Layout)`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n`;\n\nconst StyledHeader = styled(Header)`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.2);\n  \n  .ant-typography {\n    color: white !important;\n    margin: 0;\n  }\n`;\n\nconst StyledContent = styled(Content)`\n  padding: 24px;\n  max-width: 1200px;\n  margin: 0 auto;\n  width: 100%;\n`;\n\nconst FeatureCard = styled(Card)`\n  height: 100%;\n  border-radius: 12px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  \n  .ant-card-head {\n    border-bottom: 1px solid rgba(0, 0, 0, 0.06);\n  }\n  \n  .ant-card-body {\n    padding: 24px;\n  }\n`;\n\nconst StatsCard = styled(Card)`\n  text-align: center;\n  border-radius: 8px;\n  border: none;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  \n  .ant-card-body {\n    padding: 16px;\n  }\n`;\n\nconst IconWrapper = styled.div`\n  font-size: 24px;\n  margin-bottom: 8px;\n  color: ${props => props.color || '#1890ff'};\n`;\n\nfunction App() {\n  const [loading, setLoading] = useState(false);\n  const [results, setResults] = useState(null);\n  // const [activeTab, setActiveTab] = useState('upload');\n\n  const handleAnalyze = useCallback(async (data, type = 'file') => {\n    setLoading(true);\n    try {\n      let response;\n      if (type === 'file') {\n        response = await analyzeXMLFile(data);\n      } else {\n        response = await analyzeXMLText(data.content, data.fileName);\n      }\n      \n      setResults(response.data);\n      message.success(`分析完成！检测到 ${response.data.summary.totalDataPoints} 个数据点`);\n    } catch (error) {\n      console.error('分析失败:', error);\n      message.error(error.response?.data?.error || '分析失败，请检查文件格式');\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  const handleExampleSelect = useCallback(async (exampleData) => {\n    await handleAnalyze({\n      content: exampleData.content,\n      fileName: exampleData.filename\n    }, 'text');\n  }, [handleAnalyze]);\n\n  const renderStats = () => {\n    if (!results) return null;\n\n    const { summary } = results;\n    const getStatusColor = (status) => {\n      switch (status) {\n        case 'pass': return '#52c41a';\n        case 'warning': return '#faad14';\n        case 'fail': return '#ff4d4f';\n        case 'error': return '#ff7875';\n        default: return '#1890ff';\n      }\n    };\n\n    const getStatusIcon = (status) => {\n      switch (status) {\n        case 'pass': return <CheckCircleOutlined />;\n        case 'warning': return <ExclamationCircleOutlined />;\n        case 'fail': return <BugOutlined />;\n        case 'error': return <BugOutlined />;\n        default: return <BarChartOutlined />;\n      }\n    };\n\n    return (\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} md={6}>\n          <StatsCard>\n            <IconWrapper color=\"#1890ff\">\n              <FileTextOutlined />\n            </IconWrapper>\n            <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>\n              {summary.totalDataPoints}\n            </div>\n            <div style={{ color: '#666' }}>数据点总数</div>\n          </StatsCard>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <StatsCard>\n            <IconWrapper color=\"#52c41a\">\n              <CheckCircleOutlined />\n            </IconWrapper>\n            <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>\n              {summary.validDataPoints}\n            </div>\n            <div style={{ color: '#666' }}>有效数据点</div>\n          </StatsCard>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <StatsCard>\n            <IconWrapper color=\"#faad14\">\n              <ExclamationCircleOutlined />\n            </IconWrapper>\n            <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#faad14' }}>\n              {summary.anomaliesCount}\n            </div>\n            <div style={{ color: '#666' }}>异常数量</div>\n          </StatsCard>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <StatsCard>\n            <IconWrapper color={getStatusColor(summary.overallStatus)}>\n              {getStatusIcon(summary.overallStatus)}\n            </IconWrapper>\n            <div style={{ \n              fontSize: '18px', \n              fontWeight: 'bold', \n              color: getStatusColor(summary.overallStatus),\n              textTransform: 'uppercase'\n            }}>\n              {summary.overallStatus}\n            </div>\n            <div style={{ color: '#666' }}>整体状态</div>\n          </StatsCard>\n        </Col>\n      </Row>\n    );\n  };\n\n  return (\n    <StyledLayout>\n      <StyledHeader>\n        <Title level={2}>\n          🚀 XML变速数据检测工具\n        </Title>\n        <Paragraph style={{ color: 'rgba(255, 255, 255, 0.8)', margin: 0 }}>\n          智能分析XML文件中的速度数据，检测异常并生成详细报告\n        </Paragraph>\n      </StyledHeader>\n\n      <StyledContent>\n        {renderStats()}\n        \n        <Spin spinning={loading} tip=\"正在分析XML数据...\">\n          <Row gutter={[24, 24]}>\n            <Col xs={24} lg={12}>\n              <FeatureCard\n                title={\n                  <span>\n                    <CloudUploadOutlined style={{ marginRight: 8 }} />\n                    文件上传分析\n                  </span>\n                }\n              >\n                <FileUploader onAnalyze={handleAnalyze} disabled={loading} />\n              </FeatureCard>\n            </Col>\n\n            <Col xs={24} lg={12}>\n              <FeatureCard\n                title={\n                  <span>\n                    <FileTextOutlined style={{ marginRight: 8 }} />\n                    文本编辑器\n                  </span>\n                }\n              >\n                <TextEditor onAnalyze={handleAnalyze} disabled={loading} />\n              </FeatureCard>\n            </Col>\n\n            <Col xs={24}>\n              <FeatureCard\n                title={\n                  <span>\n                    <BarChartOutlined style={{ marginRight: 8 }} />\n                    示例文件\n                  </span>\n                }\n              >\n                <ExampleSelector onSelect={handleExampleSelect} disabled={loading} />\n              </FeatureCard>\n            </Col>\n\n            {results && (\n              <Col xs={24}>\n                <FeatureCard\n                  title={\n                    <span>\n                      <BarChartOutlined style={{ marginRight: 8 }} />\n                      分析结果\n                    </span>\n                  }\n                >\n                  <ResultsDisplay results={results} />\n                </FeatureCard>\n              </Col>\n            )}\n          </Row>\n        </Spin>\n      </StyledContent>\n\n      <Footer style={{ \n        textAlign: 'center', \n        background: 'rgba(255, 255, 255, 0.1)',\n        color: 'rgba(255, 255, 255, 0.8)',\n        borderTop: '1px solid rgba(255, 255, 255, 0.2)'\n      }}>\n        XML变速数据检测工具 ©2024 - 基于现代Web技术构建\n      </Footer>\n    </StyledLayout>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACpD,SAASC,MAAM,EAAEC,UAAU,EAAEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,OAAO,EAAEC,IAAI,QAAQ,MAAM;AACxE,SACEC,mBAAmB,EACnBC,gBAAgB,EAChBC,gBAAgB,EAChBC,WAAW,EACXC,mBAAmB,EACnBC,yBAAyB,QACpB,mBAAmB;AAC1B,OAAOC,MAAM,MAAM,mBAAmB;AAEtC,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,SAASC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAM;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAO,CAAC,GAAGxB,MAAM;AAC1C,MAAM;EAAEyB,KAAK;EAAEC;AAAU,CAAC,GAAGzB,UAAU;AAEvC,MAAM0B,YAAY,GAAGd,MAAM,CAACb,MAAM,CAAC;AACnC;AACA;AACA,CAAC;AAAC4B,EAAA,GAHID,YAAY;AAKlB,MAAME,YAAY,GAAGhB,MAAM,CAACS,MAAM,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GATID,YAAY;AAWlB,MAAME,aAAa,GAAGlB,MAAM,CAACU,OAAO,CAAC;AACrC;AACA;AACA;AACA;AACA,CAAC;AAACS,GAAA,GALID,aAAa;AAOnB,MAAME,WAAW,GAAGpB,MAAM,CAACX,IAAI,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgC,GAAA,GAfID,WAAW;AAiBjB,MAAME,SAAS,GAAGtB,MAAM,CAACX,IAAI,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkC,GAAA,GATID,SAAS;AAWf,MAAME,WAAW,GAAGxB,MAAM,CAACyB,GAAG;AAC9B;AACA;AACA,WAAWC,KAAK,IAAIA,KAAK,CAACC,KAAK,IAAI,SAAS;AAC5C,CAAC;AAACC,GAAA,GAJIJ,WAAW;AAMjB,SAASK,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgD,OAAO,EAAEC,UAAU,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAC5C;;EAEA,MAAMkD,aAAa,GAAGjD,WAAW,CAAC,OAAOkD,IAAI,EAAEC,IAAI,GAAG,MAAM,KAAK;IAC/DL,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,IAAIM,QAAQ;MACZ,IAAID,IAAI,KAAK,MAAM,EAAE;QACnBC,QAAQ,GAAG,MAAMjC,cAAc,CAAC+B,IAAI,CAAC;MACvC,CAAC,MAAM;QACLE,QAAQ,GAAG,MAAMhC,cAAc,CAAC8B,IAAI,CAACG,OAAO,EAAEH,IAAI,CAACI,QAAQ,CAAC;MAC9D;MAEAN,UAAU,CAACI,QAAQ,CAACF,IAAI,CAAC;MACzB5C,OAAO,CAACiD,OAAO,CAAC,YAAYH,QAAQ,CAACF,IAAI,CAACM,OAAO,CAACC,eAAe,OAAO,CAAC;IAC3E,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACdC,OAAO,CAACH,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7BpD,OAAO,CAACoD,KAAK,CAAC,EAAAC,eAAA,GAAAD,KAAK,CAACN,QAAQ,cAAAO,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBT,IAAI,cAAAU,oBAAA,uBAApBA,oBAAA,CAAsBF,KAAK,KAAI,cAAc,CAAC;IAC9D,CAAC,SAAS;MACRZ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMgB,mBAAmB,GAAG9D,WAAW,CAAC,MAAO+D,WAAW,IAAK;IAC7D,MAAMd,aAAa,CAAC;MAClBI,OAAO,EAAEU,WAAW,CAACV,OAAO;MAC5BC,QAAQ,EAAES,WAAW,CAACC;IACxB,CAAC,EAAE,MAAM,CAAC;EACZ,CAAC,EAAE,CAACf,aAAa,CAAC,CAAC;EAEnB,MAAMgB,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAAClB,OAAO,EAAE,OAAO,IAAI;IAEzB,MAAM;MAAES;IAAQ,CAAC,GAAGT,OAAO;IAC3B,MAAMmB,cAAc,GAAIC,MAAM,IAAK;MACjC,QAAQA,MAAM;QACZ,KAAK,MAAM;UAAE,OAAO,SAAS;QAC7B,KAAK,SAAS;UAAE,OAAO,SAAS;QAChC,KAAK,MAAM;UAAE,OAAO,SAAS;QAC7B,KAAK,OAAO;UAAE,OAAO,SAAS;QAC9B;UAAS,OAAO,SAAS;MAC3B;IACF,CAAC;IAED,MAAMC,aAAa,GAAID,MAAM,IAAK;MAChC,QAAQA,MAAM;QACZ,KAAK,MAAM;UAAE,oBAAO7C,OAAA,CAACV,mBAAmB;YAAA0C,QAAA,EAAAe,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAC3C,KAAK,SAAS;UAAE,oBAAOjD,OAAA,CAACT,yBAAyB;YAAAyC,QAAA,EAAAe,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QACpD,KAAK,MAAM;UAAE,oBAAOjD,OAAA,CAACX,WAAW;YAAA2C,QAAA,EAAAe,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QACnC,KAAK,OAAO;UAAE,oBAAOjD,OAAA,CAACX,WAAW;YAAA2C,QAAA,EAAAe,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QACpC;UAAS,oBAAOjD,OAAA,CAACZ,gBAAgB;YAAA4C,QAAA,EAAAe,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;MACtC;IACF,CAAC;IAED,oBACEjD,OAAA,CAAClB,GAAG;MAACoE,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACC,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAAC,QAAA,gBACjDrD,OAAA,CAACjB,GAAG;QAACuE,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAH,QAAA,eACzBrD,OAAA,CAACc,SAAS;UAAAuC,QAAA,gBACRrD,OAAA,CAACgB,WAAW;YAACG,KAAK,EAAC,SAAS;YAAAkC,QAAA,eAC1BrD,OAAA,CAACb,gBAAgB;cAAA6C,QAAA,EAAAe,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAjB,QAAA,EAAAe,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACdjD,OAAA;YAAKmD,KAAK,EAAE;cAAEM,QAAQ,EAAE,MAAM;cAAEC,UAAU,EAAE,MAAM;cAAEvC,KAAK,EAAE;YAAU,CAAE;YAAAkC,QAAA,EACpEnB,OAAO,CAACC;UAAe;YAAAH,QAAA,EAAAe,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACNjD,OAAA;YAAKmD,KAAK,EAAE;cAAEhC,KAAK,EAAE;YAAO,CAAE;YAAAkC,QAAA,EAAC;UAAK;YAAArB,QAAA,EAAAe,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAjB,QAAA,EAAAe,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC;MAAC;QAAAjB,QAAA,EAAAe,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACNjD,OAAA,CAACjB,GAAG;QAACuE,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAH,QAAA,eACzBrD,OAAA,CAACc,SAAS;UAAAuC,QAAA,gBACRrD,OAAA,CAACgB,WAAW;YAACG,KAAK,EAAC,SAAS;YAAAkC,QAAA,eAC1BrD,OAAA,CAACV,mBAAmB;cAAA0C,QAAA,EAAAe,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAjB,QAAA,EAAAe,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACdjD,OAAA;YAAKmD,KAAK,EAAE;cAAEM,QAAQ,EAAE,MAAM;cAAEC,UAAU,EAAE,MAAM;cAAEvC,KAAK,EAAE;YAAU,CAAE;YAAAkC,QAAA,EACpEnB,OAAO,CAACyB;UAAe;YAAA3B,QAAA,EAAAe,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACNjD,OAAA;YAAKmD,KAAK,EAAE;cAAEhC,KAAK,EAAE;YAAO,CAAE;YAAAkC,QAAA,EAAC;UAAK;YAAArB,QAAA,EAAAe,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAjB,QAAA,EAAAe,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC;MAAC;QAAAjB,QAAA,EAAAe,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACNjD,OAAA,CAACjB,GAAG;QAACuE,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAH,QAAA,eACzBrD,OAAA,CAACc,SAAS;UAAAuC,QAAA,gBACRrD,OAAA,CAACgB,WAAW;YAACG,KAAK,EAAC,SAAS;YAAAkC,QAAA,eAC1BrD,OAAA,CAACT,yBAAyB;cAAAyC,QAAA,EAAAe,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAjB,QAAA,EAAAe,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACdjD,OAAA;YAAKmD,KAAK,EAAE;cAAEM,QAAQ,EAAE,MAAM;cAAEC,UAAU,EAAE,MAAM;cAAEvC,KAAK,EAAE;YAAU,CAAE;YAAAkC,QAAA,EACpEnB,OAAO,CAAC0B;UAAc;YAAA5B,QAAA,EAAAe,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACNjD,OAAA;YAAKmD,KAAK,EAAE;cAAEhC,KAAK,EAAE;YAAO,CAAE;YAAAkC,QAAA,EAAC;UAAI;YAAArB,QAAA,EAAAe,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAjB,QAAA,EAAAe,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC;MAAC;QAAAjB,QAAA,EAAAe,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACNjD,OAAA,CAACjB,GAAG;QAACuE,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAH,QAAA,eACzBrD,OAAA,CAACc,SAAS;UAAAuC,QAAA,gBACRrD,OAAA,CAACgB,WAAW;YAACG,KAAK,EAAEyB,cAAc,CAACV,OAAO,CAAC2B,aAAa,CAAE;YAAAR,QAAA,EACvDP,aAAa,CAACZ,OAAO,CAAC2B,aAAa;UAAC;YAAA7B,QAAA,EAAAe,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACdjD,OAAA;YAAKmD,KAAK,EAAE;cACVM,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE,MAAM;cAClBvC,KAAK,EAAEyB,cAAc,CAACV,OAAO,CAAC2B,aAAa,CAAC;cAC5CC,aAAa,EAAE;YACjB,CAAE;YAAAT,QAAA,EACCnB,OAAO,CAAC2B;UAAa;YAAA7B,QAAA,EAAAe,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACNjD,OAAA;YAAKmD,KAAK,EAAE;cAAEhC,KAAK,EAAE;YAAO,CAAE;YAAAkC,QAAA,EAAC;UAAI;YAAArB,QAAA,EAAAe,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAjB,QAAA,EAAAe,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC;MAAC;QAAAjB,QAAA,EAAAe,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAjB,QAAA,EAAAe,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,oBACEjD,OAAA,CAACM,YAAY;IAAA+C,QAAA,gBACXrD,OAAA,CAACQ,YAAY;MAAA6C,QAAA,gBACXrD,OAAA,CAACI,KAAK;QAAC2D,KAAK,EAAE,CAAE;QAAAV,QAAA,EAAC;MAEjB;QAAArB,QAAA,EAAAe,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRjD,OAAA,CAACK,SAAS;QAAC8C,KAAK,EAAE;UAAEhC,KAAK,EAAE,0BAA0B;UAAE6C,MAAM,EAAE;QAAE,CAAE;QAAAX,QAAA,EAAC;MAEpE;QAAArB,QAAA,EAAAe,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC;IAAA;MAAAjB,QAAA,EAAAe,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAEfjD,OAAA,CAACU,aAAa;MAAA2C,QAAA,GACXV,WAAW,CAAC,CAAC,eAEd3C,OAAA,CAACf,IAAI;QAACgF,QAAQ,EAAE1C,OAAQ;QAAC2C,GAAG,EAAC,4CAAc;QAAAb,QAAA,eACzCrD,OAAA,CAAClB,GAAG;UAACoE,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAG,QAAA,gBACpBrD,OAAA,CAACjB,GAAG;YAACuE,EAAE,EAAE,EAAG;YAACa,EAAE,EAAE,EAAG;YAAAd,QAAA,eAClBrD,OAAA,CAACY,WAAW;cACVwD,KAAK,eACHpE,OAAA;gBAAAqD,QAAA,gBACErD,OAAA,CAACd,mBAAmB;kBAACiE,KAAK,EAAE;oBAAEkB,WAAW,EAAE;kBAAE;gBAAE;kBAAArC,QAAA,EAAAe,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,wCAEpD;cAAA;gBAAAjB,QAAA,EAAAe,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP;cAAAI,QAAA,eAEDrD,OAAA,CAACP,YAAY;gBAAC6E,SAAS,EAAE3C,aAAc;gBAAC4C,QAAQ,EAAEhD;cAAQ;gBAAAS,QAAA,EAAAe,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAjB,QAAA,EAAAe,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAjB,QAAA,EAAAe,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eAENjD,OAAA,CAACjB,GAAG;YAACuE,EAAE,EAAE,EAAG;YAACa,EAAE,EAAE,EAAG;YAAAd,QAAA,eAClBrD,OAAA,CAACY,WAAW;cACVwD,KAAK,eACHpE,OAAA;gBAAAqD,QAAA,gBACErD,OAAA,CAACb,gBAAgB;kBAACgE,KAAK,EAAE;oBAAEkB,WAAW,EAAE;kBAAE;gBAAE;kBAAArC,QAAA,EAAAe,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,kCAEjD;cAAA;gBAAAjB,QAAA,EAAAe,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP;cAAAI,QAAA,eAEDrD,OAAA,CAACN,UAAU;gBAAC4E,SAAS,EAAE3C,aAAc;gBAAC4C,QAAQ,EAAEhD;cAAQ;gBAAAS,QAAA,EAAAe,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAjB,QAAA,EAAAe,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAjB,QAAA,EAAAe,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eAENjD,OAAA,CAACjB,GAAG;YAACuE,EAAE,EAAE,EAAG;YAAAD,QAAA,eACVrD,OAAA,CAACY,WAAW;cACVwD,KAAK,eACHpE,OAAA;gBAAAqD,QAAA,gBACErD,OAAA,CAACZ,gBAAgB;kBAAC+D,KAAK,EAAE;oBAAEkB,WAAW,EAAE;kBAAE;gBAAE;kBAAArC,QAAA,EAAAe,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,4BAEjD;cAAA;gBAAAjB,QAAA,EAAAe,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP;cAAAI,QAAA,eAEDrD,OAAA,CAACJ,eAAe;gBAAC4E,QAAQ,EAAEhC,mBAAoB;gBAAC+B,QAAQ,EAAEhD;cAAQ;gBAAAS,QAAA,EAAAe,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAjB,QAAA,EAAAe,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D;UAAC;YAAAjB,QAAA,EAAAe,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,EAELxB,OAAO,iBACNzB,OAAA,CAACjB,GAAG;YAACuE,EAAE,EAAE,EAAG;YAAAD,QAAA,eACVrD,OAAA,CAACY,WAAW;cACVwD,KAAK,eACHpE,OAAA;gBAAAqD,QAAA,gBACErD,OAAA,CAACZ,gBAAgB;kBAAC+D,KAAK,EAAE;oBAAEkB,WAAW,EAAE;kBAAE;gBAAE;kBAAArC,QAAA,EAAAe,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,4BAEjD;cAAA;gBAAAjB,QAAA,EAAAe,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP;cAAAI,QAAA,eAEDrD,OAAA,CAACL,cAAc;gBAAC8B,OAAO,EAAEA;cAAQ;gBAAAO,QAAA,EAAAe,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAjB,QAAA,EAAAe,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAjB,QAAA,EAAAe,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CACN;QAAA;UAAAjB,QAAA,EAAAe,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAjB,QAAA,EAAAe,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAjB,QAAA,EAAAe,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAEhBjD,OAAA,CAACG,MAAM;MAACgD,KAAK,EAAE;QACbsB,SAAS,EAAE,QAAQ;QACnBC,UAAU,EAAE,0BAA0B;QACtCvD,KAAK,EAAE,0BAA0B;QACjCwD,SAAS,EAAE;MACb,CAAE;MAAAtB,QAAA,EAAC;IAEH;MAAArB,QAAA,EAAAe,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAjB,QAAA,EAAAe,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB;AAAC3B,EAAA,CAlMQD,GAAG;AAAAuD,GAAA,GAAHvD,GAAG;AAoMZ,eAAeA,GAAG;AAAC,IAAAd,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAwD,GAAA;AAAAC,YAAA,CAAAtE,EAAA;AAAAsE,YAAA,CAAApE,GAAA;AAAAoE,YAAA,CAAAlE,GAAA;AAAAkE,YAAA,CAAAhE,GAAA;AAAAgE,YAAA,CAAA9D,GAAA;AAAA8D,YAAA,CAAAzD,GAAA;AAAAyD,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
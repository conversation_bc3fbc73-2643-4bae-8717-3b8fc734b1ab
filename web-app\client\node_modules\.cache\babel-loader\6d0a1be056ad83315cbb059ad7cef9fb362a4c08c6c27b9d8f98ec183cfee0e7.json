{"ast": null, "code": "import { sqrt } from \"../math.js\";\nconst tan30 = sqrt(1 / 3);\nconst tan30_2 = tan30 * 2;\nexport default {\n  draw(context, size) {\n    const y = sqrt(size / tan30_2);\n    const x = y * tan30;\n    context.moveTo(0, -y);\n    context.lineTo(x, 0);\n    context.lineTo(0, y);\n    context.lineTo(-x, 0);\n    context.closePath();\n  }\n};", "map": {"version": 3, "names": ["sqrt", "tan30", "tan30_2", "draw", "context", "size", "y", "x", "moveTo", "lineTo", "closePath"], "sources": ["C:/Users/<USER>/Documents/augment-projects/xmlCheck/web-app/client/node_modules/d3-shape/src/symbol/diamond.js"], "sourcesContent": ["import {sqrt} from \"../math.js\";\n\nconst tan30 = sqrt(1 / 3);\nconst tan30_2 = tan30 * 2;\n\nexport default {\n  draw(context, size) {\n    const y = sqrt(size / tan30_2);\n    const x = y * tan30;\n    context.moveTo(0, -y);\n    context.lineTo(x, 0);\n    context.lineTo(0, y);\n    context.lineTo(-x, 0);\n    context.closePath();\n  }\n};\n"], "mappings": "AAAA,SAAQA,IAAI,QAAO,YAAY;AAE/B,MAAMC,KAAK,GAAGD,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACzB,MAAME,OAAO,GAAGD,KAAK,GAAG,CAAC;AAEzB,eAAe;EACbE,IAAIA,CAACC,OAAO,EAAEC,IAAI,EAAE;IAClB,MAAMC,CAAC,GAAGN,IAAI,CAACK,IAAI,GAAGH,OAAO,CAAC;IAC9B,MAAMK,CAAC,GAAGD,CAAC,GAAGL,KAAK;IACnBG,OAAO,CAACI,MAAM,CAAC,CAAC,EAAE,CAACF,CAAC,CAAC;IACrBF,OAAO,CAACK,MAAM,CAACF,CAAC,EAAE,CAAC,CAAC;IACpBH,OAAO,CAACK,MAAM,CAAC,CAAC,EAAEH,CAAC,CAAC;IACpBF,OAAO,CAACK,MAAM,CAAC,CAACF,CAAC,EAAE,CAAC,CAAC;IACrBH,OAAO,CAACM,SAAS,CAAC,CAAC;EACrB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
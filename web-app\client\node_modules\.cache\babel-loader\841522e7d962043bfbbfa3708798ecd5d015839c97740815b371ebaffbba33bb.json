{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\xmlCheck\\\\web-app\\\\client\\\\src\\\\components\\\\ResultsDisplay.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Tabs, Card, Table, Tag, Typography, Row, Col, Statistic, Alert, Space, Button, Descriptions } from 'antd';\nimport { BarChartOutlined, TableOutlined, BugOutlined, CheckCircleOutlined, DownloadOutlined, InfoCircleOutlined, LineChartOutlined } from '@ant-design/icons';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  TabPane\n} = Tabs;\nconst {\n  Text\n} = Typography;\nconst StyledCard = styled(Card)`\n  margin-bottom: 16px;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n`;\n_c = StyledCard;\nconst StatCard = styled(Card)`\n  text-align: center;\n  border-radius: 8px;\n  \n  .ant-statistic-content {\n    color: ${props => props.color || '#1890ff'};\n  }\n`;\n_c2 = StatCard;\nconst ResultsDisplay = ({\n  results\n}) => {\n  _s();\n  const [activeTab, setActiveTab] = useState('overview');\n  if (!results) {\n    return null;\n  }\n  const {\n    metadata,\n    summary,\n    speedDataPoints,\n    speedAnalysis,\n    anomalies,\n    validation,\n    recommendations\n  } = results;\n\n  // 准备图表数据\n  const chartData = speedDataPoints.filter(point => point.time !== null).sort((a, b) => a.time - b.time).map((point, index) => ({\n    index: index + 1,\n    time: point.time,\n    speed: point.speed,\n    name: `点${index + 1}`\n  }));\n\n  // 异常分布数据\n  const anomalyDistribution = anomalies.reduce((acc, anomaly) => {\n    acc[anomaly.type] = (acc[anomaly.type] || 0) + 1;\n    return acc;\n  }, {});\n  const anomalyChartData = Object.entries(anomalyDistribution).map(([type, count]) => ({\n    type: type,\n    count: count,\n    name: getAnomalyTypeName(type)\n  }));\n\n  // 获取状态颜色\n  const getStatusColor = status => {\n    switch (status) {\n      case 'pass':\n        return '#52c41a';\n      case 'warning':\n        return '#faad14';\n      case 'fail':\n        return '#ff4d4f';\n      case 'error':\n        return '#ff7875';\n      default:\n        return '#1890ff';\n    }\n  };\n\n  // 获取异常类型名称\n  function getAnomalyTypeName(type) {\n    const typeNames = {\n      'out_of_range': '超出范围',\n      'statistical_outlier': '统计异常',\n      'negative_speed': '负速度',\n      'sudden_change': '突变'\n    };\n    return typeNames[type] || type;\n  }\n\n  // 获取严重程度颜色\n  const getSeverityColor = severity => {\n    switch (severity) {\n      case 'low':\n        return 'blue';\n      case 'medium':\n        return 'orange';\n      case 'high':\n        return 'red';\n      case 'critical':\n        return 'red';\n      default:\n        return 'default';\n    }\n  };\n\n  // 数据表格列定义\n  const dataColumns = [{\n    title: '序号',\n    dataIndex: 'index',\n    key: 'index',\n    width: 80,\n    render: (_, __, index) => index + 1\n  }, {\n    title: '节点路径',\n    dataIndex: 'nodePath',\n    key: 'nodePath',\n    ellipsis: true\n  }, {\n    title: '速度',\n    dataIndex: 'speed',\n    key: 'speed',\n    width: 100,\n    render: speed => /*#__PURE__*/_jsxDEV(Text, {\n      strong: true,\n      style: {\n        color: speed < 0 ? '#ff4d4f' : '#1890ff'\n      },\n      children: speed.toFixed(2)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => a.speed - b.speed\n  }, {\n    title: '时间',\n    dataIndex: 'time',\n    key: 'time',\n    width: 100,\n    render: time => time !== null ? time.toFixed(2) : '-',\n    sorter: (a, b) => (a.time || 0) - (b.time || 0)\n  }];\n\n  // 异常表格列定义\n  const anomalyColumns = [{\n    title: '类型',\n    dataIndex: 'type',\n    key: 'type',\n    width: 120,\n    render: type => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getSeverityColor('medium'),\n      children: getAnomalyTypeName(type)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '描述',\n    dataIndex: 'description',\n    key: 'description',\n    ellipsis: true\n  }, {\n    title: '严重程度',\n    dataIndex: 'severity',\n    key: 'severity',\n    width: 100,\n    render: severity => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getSeverityColor(severity),\n      children: severity.toUpperCase()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '位置',\n    dataIndex: 'nodePath',\n    key: 'nodePath',\n    ellipsis: true\n  }];\n\n  // 导出报告\n  const handleExport = () => {\n    const reportData = {\n      metadata,\n      summary,\n      speedAnalysis,\n      anomalies: anomalies.length,\n      recommendations: recommendations.length,\n      exportTime: new Date().toISOString()\n    };\n    const blob = new Blob([JSON.stringify(reportData, null, 2)], {\n      type: 'application/json'\n    });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `xml-analysis-report-${Date.now()}.json`;\n    a.click();\n    URL.revokeObjectURL(url);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: 16,\n        textAlign: 'right'\n      },\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 17\n        }, this),\n        onClick: handleExport,\n        children: \"\\u5BFC\\u51FA\\u62A5\\u544A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n      activeKey: activeTab,\n      onChange: setActiveTab,\n      children: [/*#__PURE__*/_jsxDEV(TabPane, {\n        tab: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [/*#__PURE__*/_jsxDEV(InfoCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 15\n          }, this), \"\\u6982\\u89C8\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 13\n        }, this),\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(StatCard, {\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u6570\\u636E\\u70B9\\u603B\\u6570\",\n                value: summary.totalDataPoints,\n                prefix: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 27\n                }, this),\n                valueStyle: {\n                  color: '#1890ff'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(StatCard, {\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u6709\\u6548\\u6570\\u636E\",\n                value: summary.validDataPoints,\n                prefix: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 27\n                }, this),\n                valueStyle: {\n                  color: '#52c41a'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(StatCard, {\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u5F02\\u5E38\\u6570\\u91CF\",\n                value: summary.anomaliesCount,\n                prefix: /*#__PURE__*/_jsxDEV(BugOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 27\n                }, this),\n                valueStyle: {\n                  color: '#faad14'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(StatCard, {\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u5904\\u7406\\u65F6\\u95F4\",\n                value: metadata.processingTime,\n                suffix: \"ms\",\n                valueStyle: {\n                  color: '#722ed1'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StyledCard, {\n          title: \"\\u6587\\u4EF6\\u4FE1\\u606F\",\n          style: {\n            marginTop: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Descriptions, {\n            column: 2,\n            bordered: true,\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u6587\\u4EF6\\u540D\",\n              children: metadata.fileName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u6587\\u4EF6\\u5927\\u5C0F\",\n              children: [(metadata.fileSize / 1024).toFixed(2), \" KB\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u5206\\u6790\\u65F6\\u95F4\",\n              children: new Date(metadata.timestamp).toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u6574\\u4F53\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(Tag, {\n                color: getStatusColor(summary.overallStatus),\n                children: summary.overallStatus.toUpperCase()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StyledCard, {\n          title: \"\\u901F\\u5EA6\\u7EDF\\u8BA1\",\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: [16, 16],\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u6700\\u5C0F\\u901F\\u5EA6\",\n                value: speedAnalysis.minSpeed,\n                precision: 2,\n                valueStyle: {\n                  color: '#52c41a'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u6700\\u5927\\u901F\\u5EA6\",\n                value: speedAnalysis.maxSpeed,\n                precision: 2,\n                valueStyle: {\n                  color: '#ff4d4f'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u5E73\\u5747\\u901F\\u5EA6\",\n                value: speedAnalysis.averageSpeed,\n                precision: 2,\n                valueStyle: {\n                  color: '#1890ff'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u6807\\u51C6\\u5DEE\",\n                value: speedAnalysis.standardDeviation,\n                precision: 2,\n                valueStyle: {\n                  color: '#722ed1'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this), recommendations.length > 0 && /*#__PURE__*/_jsxDEV(StyledCard, {\n          title: \"\\u5EFA\\u8BAE\\u548C\\u63A8\\u8350\",\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            direction: \"vertical\",\n            size: \"small\",\n            style: {\n              width: '100%'\n            },\n            children: recommendations.map((rec, index) => /*#__PURE__*/_jsxDEV(Alert, {\n              message: rec.title,\n              description: rec.description,\n              type: rec.priority === 'critical' ? 'error' : rec.priority === 'high' ? 'warning' : 'info',\n              showIcon: true\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 13\n        }, this)]\n      }, \"overview\", true, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n        tab: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [/*#__PURE__*/_jsxDEV(LineChartOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 15\n          }, this), \"\\u56FE\\u8868\\u5206\\u6790\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 13\n        }, this),\n        children: chartData.length > 0 ? /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            children: /*#__PURE__*/_jsxDEV(StyledCard, {\n              title: \"\\u901F\\u5EA6\\u65F6\\u95F4\\u66F2\\u7EBF\",\n              children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: 300,\n                children: /*#__PURE__*/_jsxDEV(LineChart, {\n                  data: chartData,\n                  children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                    strokeDasharray: \"3 3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                    dataKey: \"time\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 357,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Line, {\n                    type: \"monotone\",\n                    dataKey: \"speed\",\n                    stroke: \"#1890ff\",\n                    strokeWidth: 2,\n                    dot: {\n                      fill: '#1890ff',\n                      strokeWidth: 2,\n                      r: 4\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 360,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 15\n          }, this), anomalyChartData.length > 0 && /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            children: /*#__PURE__*/_jsxDEV(StyledCard, {\n              title: \"\\u5F02\\u5E38\\u7C7B\\u578B\\u5206\\u5E03\",\n              children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: 250,\n                children: /*#__PURE__*/_jsxDEV(BarChart, {\n                  data: anomalyChartData,\n                  children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                    strokeDasharray: \"3 3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                    dataKey: \"name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 379,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 380,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                    dataKey: \"count\",\n                    fill: \"#faad14\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u65E0\\u56FE\\u8868\\u6570\\u636E\",\n          description: \"\\u5F53\\u524D\\u6570\\u636E\\u4E2D\\u6CA1\\u6709\\u65F6\\u95F4\\u4FE1\\u606F\\uFF0C\\u65E0\\u6CD5\\u751F\\u6210\\u65F6\\u95F4\\u5E8F\\u5217\\u56FE\\u8868\",\n          type: \"info\",\n          showIcon: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 13\n        }, this)\n      }, \"charts\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n        tab: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [/*#__PURE__*/_jsxDEV(TableOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 15\n          }, this), \"\\u6570\\u636E\\u8BE6\\u60C5\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 13\n        }, this),\n        children: /*#__PURE__*/_jsxDEV(StyledCard, {\n          title: `速度数据点 (${speedDataPoints.length} 条)`,\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            columns: dataColumns,\n            dataSource: speedDataPoints,\n            rowKey: (record, index) => index,\n            pagination: {\n              pageSize: 10,\n              showSizeChanger: true,\n              showQuickJumper: true,\n              showTotal: total => `共 ${total} 条数据`\n            },\n            scroll: {\n              x: 800\n            },\n            size: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 11\n        }, this)\n      }, \"data\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 399,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n        tab: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [/*#__PURE__*/_jsxDEV(BugOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 15\n          }, this), \"\\u5F02\\u5E38\\u68C0\\u6D4B \", anomalies.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#ff4d4f'\n            },\n            children: [\"(\", anomalies.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 45\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 13\n        }, this),\n        children: [anomalies.length > 0 ? /*#__PURE__*/_jsxDEV(StyledCard, {\n          title: `检测到的异常 (${anomalies.length} 个)`,\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            columns: anomalyColumns,\n            dataSource: anomalies,\n            rowKey: (record, index) => index,\n            pagination: {\n              pageSize: 10,\n              showSizeChanger: true,\n              showTotal: total => `共 ${total} 个异常`\n            },\n            scroll: {\n              x: 800\n            },\n            size: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u672A\\u53D1\\u73B0\\u5F02\\u5E38\",\n          description: \"\\u606D\\u559C\\uFF01\\u6240\\u6709\\u6570\\u636E\\u70B9\\u90FD\\u901A\\u8FC7\\u4E86\\u5F02\\u5E38\\u68C0\\u6D4B\",\n          type: \"success\",\n          showIcon: true,\n          style: {\n            marginBottom: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 13\n        }, this), validation.errors.length > 0 && /*#__PURE__*/_jsxDEV(StyledCard, {\n          title: \"\\u9A8C\\u8BC1\\u9519\\u8BEF\",\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            direction: \"vertical\",\n            size: \"small\",\n            style: {\n              width: '100%'\n            },\n            children: validation.errors.map((error, index) => /*#__PURE__*/_jsxDEV(Alert, {\n              message: error.description,\n              description: error.suggestion,\n              type: \"error\",\n              showIcon: true\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 13\n        }, this), validation.warnings.length > 0 && /*#__PURE__*/_jsxDEV(StyledCard, {\n          title: \"\\u9A8C\\u8BC1\\u8B66\\u544A\",\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            direction: \"vertical\",\n            size: \"small\",\n            style: {\n              width: '100%'\n            },\n            children: validation.warnings.map((warning, index) => /*#__PURE__*/_jsxDEV(Alert, {\n              message: warning.description,\n              description: warning.suggestion,\n              type: \"warning\",\n              showIcon: true\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 13\n        }, this)]\n      }, \"anomalies\", true, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 207,\n    columnNumber: 5\n  }, this);\n};\n_s(ResultsDisplay, \"chU/96YDf6tpLdqvgqS2ZL+A4Bo=\");\n_c3 = ResultsDisplay;\nexport default ResultsDisplay;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"StyledCard\");\n$RefreshReg$(_c2, \"StatCard\");\n$RefreshReg$(_c3, \"ResultsDisplay\");", "map": {"version": 3, "names": ["React", "useState", "Tabs", "Card", "Table", "Tag", "Typography", "Row", "Col", "Statistic", "<PERSON><PERSON>", "Space", "<PERSON><PERSON>", "Descriptions", "BarChartOutlined", "TableOutlined", "BugOutlined", "CheckCircleOutlined", "DownloadOutlined", "InfoCircleOutlined", "LineChartOutlined", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Bar", "styled", "jsxDEV", "_jsxDEV", "TabPane", "Text", "StyledCard", "_c", "StatCard", "props", "color", "_c2", "ResultsDisplay", "results", "_s", "activeTab", "setActiveTab", "metadata", "summary", "speedDataPoints", "speedAnalysis", "anomalies", "validation", "recommendations", "chartData", "filter", "point", "time", "sort", "a", "b", "map", "index", "speed", "name", "anomalyDistribution", "reduce", "acc", "anomaly", "type", "anomalyChartData", "Object", "entries", "count", "getAnomalyTypeName", "getStatusColor", "status", "typeNames", "getSeverityColor", "severity", "dataColumns", "title", "dataIndex", "key", "width", "render", "_", "__", "ellipsis", "strong", "style", "children", "toFixed", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sorter", "anomalyColumns", "toUpperCase", "handleExport", "reportData", "length", "exportTime", "Date", "toISOString", "blob", "Blob", "JSON", "stringify", "url", "URL", "createObjectURL", "document", "createElement", "href", "download", "now", "click", "revokeObjectURL", "marginBottom", "textAlign", "icon", "onClick", "active<PERSON><PERSON>", "onChange", "tab", "gutter", "xs", "sm", "md", "value", "totalDataPoints", "prefix", "valueStyle", "validDataPoints", "anomaliesCount", "processingTime", "suffix", "marginTop", "column", "bordered", "size", "<PERSON><PERSON>", "label", "fileSize", "timestamp", "toLocaleString", "overallStatus", "minSpeed", "precision", "maxSpeed", "averageSpeed", "standardDeviation", "direction", "rec", "message", "description", "priority", "showIcon", "height", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "stroke", "strokeWidth", "dot", "fill", "r", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "record", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "total", "scroll", "x", "errors", "error", "suggestion", "warnings", "warning", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/xmlCheck/web-app/client/src/components/ResultsDisplay.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { \n  Tabs, \n  Card, \n  Table, \n  Tag, \n  Typography, \n  Row, \n  Col, \n  Statistic, \n  Alert,\n  Space,\n  Button,\n  Descriptions\n} from 'antd';\nimport { \n  BarChartOutlined, \n  TableOutlined, \n  BugOutlined, \n  CheckCircleOutlined,\n  DownloadOutlined,\n  InfoCircleOutlined,\n  LineChartOutlined\n} from '@ant-design/icons';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';\nimport styled from 'styled-components';\n\nconst { TabPane } = Tabs;\nconst { Text } = Typography;\n\nconst StyledCard = styled(Card)`\n  margin-bottom: 16px;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n`;\n\nconst StatCard = styled(Card)`\n  text-align: center;\n  border-radius: 8px;\n  \n  .ant-statistic-content {\n    color: ${props => props.color || '#1890ff'};\n  }\n`;\n\nconst ResultsDisplay = ({ results }) => {\n  const [activeTab, setActiveTab] = useState('overview');\n\n  if (!results) {\n    return null;\n  }\n\n  const { metadata, summary, speedDataPoints, speedAnalysis, anomalies, validation, recommendations } = results;\n\n  // 准备图表数据\n  const chartData = speedDataPoints\n    .filter(point => point.time !== null)\n    .sort((a, b) => a.time - b.time)\n    .map((point, index) => ({\n      index: index + 1,\n      time: point.time,\n      speed: point.speed,\n      name: `点${index + 1}`\n    }));\n\n  // 异常分布数据\n  const anomalyDistribution = anomalies.reduce((acc, anomaly) => {\n    acc[anomaly.type] = (acc[anomaly.type] || 0) + 1;\n    return acc;\n  }, {});\n\n  const anomalyChartData = Object.entries(anomalyDistribution).map(([type, count]) => ({\n    type: type,\n    count: count,\n    name: getAnomalyTypeName(type)\n  }));\n\n  // 获取状态颜色\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'pass': return '#52c41a';\n      case 'warning': return '#faad14';\n      case 'fail': return '#ff4d4f';\n      case 'error': return '#ff7875';\n      default: return '#1890ff';\n    }\n  };\n\n  // 获取异常类型名称\n  function getAnomalyTypeName(type) {\n    const typeNames = {\n      'out_of_range': '超出范围',\n      'statistical_outlier': '统计异常',\n      'negative_speed': '负速度',\n      'sudden_change': '突变'\n    };\n    return typeNames[type] || type;\n  }\n\n  // 获取严重程度颜色\n  const getSeverityColor = (severity) => {\n    switch (severity) {\n      case 'low': return 'blue';\n      case 'medium': return 'orange';\n      case 'high': return 'red';\n      case 'critical': return 'red';\n      default: return 'default';\n    }\n  };\n\n  // 数据表格列定义\n  const dataColumns = [\n    {\n      title: '序号',\n      dataIndex: 'index',\n      key: 'index',\n      width: 80,\n      render: (_, __, index) => index + 1,\n    },\n    {\n      title: '节点路径',\n      dataIndex: 'nodePath',\n      key: 'nodePath',\n      ellipsis: true,\n    },\n    {\n      title: '速度',\n      dataIndex: 'speed',\n      key: 'speed',\n      width: 100,\n      render: (speed) => (\n        <Text strong style={{ color: speed < 0 ? '#ff4d4f' : '#1890ff' }}>\n          {speed.toFixed(2)}\n        </Text>\n      ),\n      sorter: (a, b) => a.speed - b.speed,\n    },\n    {\n      title: '时间',\n      dataIndex: 'time',\n      key: 'time',\n      width: 100,\n      render: (time) => time !== null ? time.toFixed(2) : '-',\n      sorter: (a, b) => (a.time || 0) - (b.time || 0),\n    },\n  ];\n\n  // 异常表格列定义\n  const anomalyColumns = [\n    {\n      title: '类型',\n      dataIndex: 'type',\n      key: 'type',\n      width: 120,\n      render: (type) => (\n        <Tag color={getSeverityColor('medium')}>\n          {getAnomalyTypeName(type)}\n        </Tag>\n      ),\n    },\n    {\n      title: '描述',\n      dataIndex: 'description',\n      key: 'description',\n      ellipsis: true,\n    },\n    {\n      title: '严重程度',\n      dataIndex: 'severity',\n      key: 'severity',\n      width: 100,\n      render: (severity) => (\n        <Tag color={getSeverityColor(severity)}>\n          {severity.toUpperCase()}\n        </Tag>\n      ),\n    },\n    {\n      title: '位置',\n      dataIndex: 'nodePath',\n      key: 'nodePath',\n      ellipsis: true,\n    },\n  ];\n\n  // 导出报告\n  const handleExport = () => {\n    const reportData = {\n      metadata,\n      summary,\n      speedAnalysis,\n      anomalies: anomalies.length,\n      recommendations: recommendations.length,\n      exportTime: new Date().toISOString()\n    };\n    \n    const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `xml-analysis-report-${Date.now()}.json`;\n    a.click();\n    URL.revokeObjectURL(url);\n  };\n\n  return (\n    <div>\n      {/* 操作按钮 */}\n      <div style={{ marginBottom: 16, textAlign: 'right' }}>\n        <Button \n          type=\"primary\" \n          icon={<DownloadOutlined />} \n          onClick={handleExport}\n        >\n          导出报告\n        </Button>\n      </div>\n\n      <Tabs activeKey={activeTab} onChange={setActiveTab}>\n        {/* 概览标签页 */}\n        <TabPane \n          tab={\n            <span>\n              <InfoCircleOutlined />\n              概览\n            </span>\n          } \n          key=\"overview\"\n        >\n          <Row gutter={[16, 16]}>\n            <Col xs={24} sm={12} md={6}>\n              <StatCard>\n                <Statistic\n                  title=\"数据点总数\"\n                  value={summary.totalDataPoints}\n                  prefix={<BarChartOutlined />}\n                  valueStyle={{ color: '#1890ff' }}\n                />\n              </StatCard>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <StatCard>\n                <Statistic\n                  title=\"有效数据\"\n                  value={summary.validDataPoints}\n                  prefix={<CheckCircleOutlined />}\n                  valueStyle={{ color: '#52c41a' }}\n                />\n              </StatCard>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <StatCard>\n                <Statistic\n                  title=\"异常数量\"\n                  value={summary.anomaliesCount}\n                  prefix={<BugOutlined />}\n                  valueStyle={{ color: '#faad14' }}\n                />\n              </StatCard>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <StatCard>\n                <Statistic\n                  title=\"处理时间\"\n                  value={metadata.processingTime}\n                  suffix=\"ms\"\n                  valueStyle={{ color: '#722ed1' }}\n                />\n              </StatCard>\n            </Col>\n          </Row>\n\n          <StyledCard title=\"文件信息\" style={{ marginTop: 16 }}>\n            <Descriptions column={2} bordered size=\"small\">\n              <Descriptions.Item label=\"文件名\">{metadata.fileName}</Descriptions.Item>\n              <Descriptions.Item label=\"文件大小\">{(metadata.fileSize / 1024).toFixed(2)} KB</Descriptions.Item>\n              <Descriptions.Item label=\"分析时间\">{new Date(metadata.timestamp).toLocaleString()}</Descriptions.Item>\n              <Descriptions.Item label=\"整体状态\">\n                <Tag color={getStatusColor(summary.overallStatus)}>\n                  {summary.overallStatus.toUpperCase()}\n                </Tag>\n              </Descriptions.Item>\n            </Descriptions>\n          </StyledCard>\n\n          <StyledCard title=\"速度统计\">\n            <Row gutter={[16, 16]}>\n              <Col xs={24} sm={12} md={6}>\n                <Statistic\n                  title=\"最小速度\"\n                  value={speedAnalysis.minSpeed}\n                  precision={2}\n                  valueStyle={{ color: '#52c41a' }}\n                />\n              </Col>\n              <Col xs={24} sm={12} md={6}>\n                <Statistic\n                  title=\"最大速度\"\n                  value={speedAnalysis.maxSpeed}\n                  precision={2}\n                  valueStyle={{ color: '#ff4d4f' }}\n                />\n              </Col>\n              <Col xs={24} sm={12} md={6}>\n                <Statistic\n                  title=\"平均速度\"\n                  value={speedAnalysis.averageSpeed}\n                  precision={2}\n                  valueStyle={{ color: '#1890ff' }}\n                />\n              </Col>\n              <Col xs={24} sm={12} md={6}>\n                <Statistic\n                  title=\"标准差\"\n                  value={speedAnalysis.standardDeviation}\n                  precision={2}\n                  valueStyle={{ color: '#722ed1' }}\n                />\n              </Col>\n            </Row>\n          </StyledCard>\n\n          {recommendations.length > 0 && (\n            <StyledCard title=\"建议和推荐\">\n              <Space direction=\"vertical\" size=\"small\" style={{ width: '100%' }}>\n                {recommendations.map((rec, index) => (\n                  <Alert\n                    key={index}\n                    message={rec.title}\n                    description={rec.description}\n                    type={rec.priority === 'critical' ? 'error' : rec.priority === 'high' ? 'warning' : 'info'}\n                    showIcon\n                  />\n                ))}\n              </Space>\n            </StyledCard>\n          )}\n        </TabPane>\n\n        {/* 图表标签页 */}\n        <TabPane \n          tab={\n            <span>\n              <LineChartOutlined />\n              图表分析\n            </span>\n          } \n          key=\"charts\"\n        >\n          {chartData.length > 0 ? (\n            <Row gutter={[16, 16]}>\n              <Col xs={24}>\n                <StyledCard title=\"速度时间曲线\">\n                  <ResponsiveContainer width=\"100%\" height={300}>\n                    <LineChart data={chartData}>\n                      <CartesianGrid strokeDasharray=\"3 3\" />\n                      <XAxis dataKey=\"time\" />\n                      <YAxis />\n                      <Tooltip />\n                      <Line \n                        type=\"monotone\" \n                        dataKey=\"speed\" \n                        stroke=\"#1890ff\" \n                        strokeWidth={2}\n                        dot={{ fill: '#1890ff', strokeWidth: 2, r: 4 }}\n                      />\n                    </LineChart>\n                  </ResponsiveContainer>\n                </StyledCard>\n              </Col>\n              \n              {anomalyChartData.length > 0 && (\n                <Col xs={24}>\n                  <StyledCard title=\"异常类型分布\">\n                    <ResponsiveContainer width=\"100%\" height={250}>\n                      <BarChart data={anomalyChartData}>\n                        <CartesianGrid strokeDasharray=\"3 3\" />\n                        <XAxis dataKey=\"name\" />\n                        <YAxis />\n                        <Tooltip />\n                        <Bar dataKey=\"count\" fill=\"#faad14\" />\n                      </BarChart>\n                    </ResponsiveContainer>\n                  </StyledCard>\n                </Col>\n              )}\n            </Row>\n          ) : (\n            <Alert\n              message=\"无图表数据\"\n              description=\"当前数据中没有时间信息，无法生成时间序列图表\"\n              type=\"info\"\n              showIcon\n            />\n          )}\n        </TabPane>\n\n        {/* 数据表格标签页 */}\n        <TabPane \n          tab={\n            <span>\n              <TableOutlined />\n              数据详情\n            </span>\n          } \n          key=\"data\"\n        >\n          <StyledCard title={`速度数据点 (${speedDataPoints.length} 条)`}>\n            <Table\n              columns={dataColumns}\n              dataSource={speedDataPoints}\n              rowKey={(record, index) => index}\n              pagination={{\n                pageSize: 10,\n                showSizeChanger: true,\n                showQuickJumper: true,\n                showTotal: (total) => `共 ${total} 条数据`,\n              }}\n              scroll={{ x: 800 }}\n              size=\"small\"\n            />\n          </StyledCard>\n        </TabPane>\n\n        {/* 异常检测标签页 */}\n        <TabPane \n          tab={\n            <span>\n              <BugOutlined />\n              异常检测 {anomalies.length > 0 && <span style={{ color: '#ff4d4f' }}>({anomalies.length})</span>}\n            </span>\n          } \n          key=\"anomalies\"\n        >\n          {anomalies.length > 0 ? (\n            <StyledCard title={`检测到的异常 (${anomalies.length} 个)`}>\n              <Table\n                columns={anomalyColumns}\n                dataSource={anomalies}\n                rowKey={(record, index) => index}\n                pagination={{\n                  pageSize: 10,\n                  showSizeChanger: true,\n                  showTotal: (total) => `共 ${total} 个异常`,\n                }}\n                scroll={{ x: 800 }}\n                size=\"small\"\n              />\n            </StyledCard>\n          ) : (\n            <Alert\n              message=\"未发现异常\"\n              description=\"恭喜！所有数据点都通过了异常检测\"\n              type=\"success\"\n              showIcon\n              style={{ marginBottom: 16 }}\n            />\n          )}\n\n          {validation.errors.length > 0 && (\n            <StyledCard title=\"验证错误\">\n              <Space direction=\"vertical\" size=\"small\" style={{ width: '100%' }}>\n                {validation.errors.map((error, index) => (\n                  <Alert\n                    key={index}\n                    message={error.description}\n                    description={error.suggestion}\n                    type=\"error\"\n                    showIcon\n                  />\n                ))}\n              </Space>\n            </StyledCard>\n          )}\n\n          {validation.warnings.length > 0 && (\n            <StyledCard title=\"验证警告\">\n              <Space direction=\"vertical\" size=\"small\" style={{ width: '100%' }}>\n                {validation.warnings.map((warning, index) => (\n                  <Alert\n                    key={index}\n                    message={warning.description}\n                    description={warning.suggestion}\n                    type=\"warning\"\n                    showIcon\n                  />\n                ))}\n              </Space>\n            </StyledCard>\n          )}\n        </TabPane>\n      </Tabs>\n    </div>\n  );\n};\n\nexport default ResultsDisplay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,IAAI,EACJC,IAAI,EACJC,KAAK,EACLC,GAAG,EACHC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,YAAY,QACP,MAAM;AACb,SACEC,gBAAgB,EAChBC,aAAa,EACbC,WAAW,EACXC,mBAAmB,EACnBC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,QACZ,mBAAmB;AAC1B,SAASC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,mBAAmB,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,UAAU;AACpH,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAM;EAAEC;AAAQ,CAAC,GAAG/B,IAAI;AACxB,MAAM;EAAEgC;AAAK,CAAC,GAAG5B,UAAU;AAE3B,MAAM6B,UAAU,GAAGL,MAAM,CAAC3B,IAAI,CAAC;AAC/B;AACA;AACA;AACA,CAAC;AAACiC,EAAA,GAJID,UAAU;AAMhB,MAAME,QAAQ,GAAGP,MAAM,CAAC3B,IAAI,CAAC;AAC7B;AACA;AACA;AACA;AACA,aAAamC,KAAK,IAAIA,KAAK,CAACC,KAAK,IAAI,SAAS;AAC9C;AACA,CAAC;AAACC,GAAA,GAPIH,QAAQ;AASd,MAAMI,cAAc,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACtC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAC,UAAU,CAAC;EAEtD,IAAI,CAACyC,OAAO,EAAE;IACZ,OAAO,IAAI;EACb;EAEA,MAAM;IAAEI,QAAQ;IAAEC,OAAO;IAAEC,eAAe;IAAEC,aAAa;IAAEC,SAAS;IAAEC,UAAU;IAAEC;EAAgB,CAAC,GAAGV,OAAO;;EAE7G;EACA,MAAMW,SAAS,GAAGL,eAAe,CAC9BM,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,IAAI,KAAK,IAAI,CAAC,CACpCC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACF,IAAI,GAAGG,CAAC,CAACH,IAAI,CAAC,CAC/BI,GAAG,CAAC,CAACL,KAAK,EAAEM,KAAK,MAAM;IACtBA,KAAK,EAAEA,KAAK,GAAG,CAAC;IAChBL,IAAI,EAAED,KAAK,CAACC,IAAI;IAChBM,KAAK,EAAEP,KAAK,CAACO,KAAK;IAClBC,IAAI,EAAE,IAAIF,KAAK,GAAG,CAAC;EACrB,CAAC,CAAC,CAAC;;EAEL;EACA,MAAMG,mBAAmB,GAAGd,SAAS,CAACe,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAK;IAC7DD,GAAG,CAACC,OAAO,CAACC,IAAI,CAAC,GAAG,CAACF,GAAG,CAACC,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;IAChD,OAAOF,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,MAAMG,gBAAgB,GAAGC,MAAM,CAACC,OAAO,CAACP,mBAAmB,CAAC,CAACJ,GAAG,CAAC,CAAC,CAACQ,IAAI,EAAEI,KAAK,CAAC,MAAM;IACnFJ,IAAI,EAAEA,IAAI;IACVI,KAAK,EAAEA,KAAK;IACZT,IAAI,EAAEU,kBAAkB,CAACL,IAAI;EAC/B,CAAC,CAAC,CAAC;;EAEH;EACA,MAAMM,cAAc,GAAIC,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,OAAO;QAAE,OAAO,SAAS;MAC9B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;;EAED;EACA,SAASF,kBAAkBA,CAACL,IAAI,EAAE;IAChC,MAAMQ,SAAS,GAAG;MAChB,cAAc,EAAE,MAAM;MACtB,qBAAqB,EAAE,MAAM;MAC7B,gBAAgB,EAAE,KAAK;MACvB,eAAe,EAAE;IACnB,CAAC;IACD,OAAOA,SAAS,CAACR,IAAI,CAAC,IAAIA,IAAI;EAChC;;EAEA;EACA,MAAMS,gBAAgB,GAAIC,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,KAAK;QAAE,OAAO,MAAM;MACzB,KAAK,QAAQ;QAAE,OAAO,QAAQ;MAC9B,KAAK,MAAM;QAAE,OAAO,KAAK;MACzB,KAAK,UAAU;QAAE,OAAO,KAAK;MAC7B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;;EAED;EACA,MAAMC,WAAW,GAAG,CAClB;IACEC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAEA,CAACC,CAAC,EAAEC,EAAE,EAAEzB,KAAK,KAAKA,KAAK,GAAG;EACpC,CAAC,EACD;IACEmB,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfK,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGtB,KAAK,iBACZ9B,OAAA,CAACE,IAAI;MAACsD,MAAM;MAACC,KAAK,EAAE;QAAElD,KAAK,EAAEuB,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG;MAAU,CAAE;MAAA4B,QAAA,EAC9D5B,KAAK,CAAC6B,OAAO,CAAC,CAAC;IAAC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CACP;IACDC,MAAM,EAAEA,CAACtC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACI,KAAK,GAAGH,CAAC,CAACG;EAChC,CAAC,EACD;IACEkB,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAG5B,IAAI,IAAKA,IAAI,KAAK,IAAI,GAAGA,IAAI,CAACmC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IACvDK,MAAM,EAAEA,CAACtC,CAAC,EAAEC,CAAC,KAAK,CAACD,CAAC,CAACF,IAAI,IAAI,CAAC,KAAKG,CAAC,CAACH,IAAI,IAAI,CAAC;EAChD,CAAC,CACF;;EAED;EACA,MAAMyC,cAAc,GAAG,CACrB;IACEjB,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGhB,IAAI,iBACXpC,OAAA,CAAC3B,GAAG;MAACkC,KAAK,EAAEsC,gBAAgB,CAAC,QAAQ,CAAE;MAAAa,QAAA,EACpCjB,kBAAkB,CAACL,IAAI;IAAC;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB;EAET,CAAC,EACD;IACEf,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBK,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGN,QAAQ,iBACf9C,OAAA,CAAC3B,GAAG;MAACkC,KAAK,EAAEsC,gBAAgB,CAACC,QAAQ,CAAE;MAAAY,QAAA,EACpCZ,QAAQ,CAACoB,WAAW,CAAC;IAAC;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB;EAET,CAAC,EACD;IACEf,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfK,QAAQ,EAAE;EACZ,CAAC,CACF;;EAED;EACA,MAAMY,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,UAAU,GAAG;MACjBtD,QAAQ;MACRC,OAAO;MACPE,aAAa;MACbC,SAAS,EAAEA,SAAS,CAACmD,MAAM;MAC3BjD,eAAe,EAAEA,eAAe,CAACiD,MAAM;MACvCC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACrC,CAAC;IAED,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACC,IAAI,CAACC,SAAS,CAACR,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;MAAEhC,IAAI,EAAE;IAAmB,CAAC,CAAC;IAC1F,MAAMyC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;IACrC,MAAM/C,CAAC,GAAGsD,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCvD,CAAC,CAACwD,IAAI,GAAGL,GAAG;IACZnD,CAAC,CAACyD,QAAQ,GAAG,uBAAuBZ,IAAI,CAACa,GAAG,CAAC,CAAC,OAAO;IACrD1D,CAAC,CAAC2D,KAAK,CAAC,CAAC;IACTP,GAAG,CAACQ,eAAe,CAACT,GAAG,CAAC;EAC1B,CAAC;EAED,oBACE7E,OAAA;IAAA0D,QAAA,gBAEE1D,OAAA;MAAKyD,KAAK,EAAE;QAAE8B,YAAY,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAQ,CAAE;MAAA9B,QAAA,eACnD1D,OAAA,CAACpB,MAAM;QACLwD,IAAI,EAAC,SAAS;QACdqD,IAAI,eAAEzF,OAAA,CAACd,gBAAgB;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3B2B,OAAO,EAAEvB,YAAa;QAAAT,QAAA,EACvB;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEN/D,OAAA,CAAC9B,IAAI;MAACyH,SAAS,EAAE/E,SAAU;MAACgF,QAAQ,EAAE/E,YAAa;MAAA6C,QAAA,gBAEjD1D,OAAA,CAACC,OAAO;QACN4F,GAAG,eACD7F,OAAA;UAAA0D,QAAA,gBACE1D,OAAA,CAACb,kBAAkB;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAExB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QAAAL,QAAA,gBAGD1D,OAAA,CAACzB,GAAG;UAACuH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAApC,QAAA,gBACpB1D,OAAA,CAACxB,GAAG;YAACuH,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvC,QAAA,eACzB1D,OAAA,CAACK,QAAQ;cAAAqD,QAAA,eACP1D,OAAA,CAACvB,SAAS;gBACRuE,KAAK,EAAC,gCAAO;gBACbkD,KAAK,EAAEnF,OAAO,CAACoF,eAAgB;gBAC/BC,MAAM,eAAEpG,OAAA,CAAClB,gBAAgB;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC7BsC,UAAU,EAAE;kBAAE9F,KAAK,EAAE;gBAAU;cAAE;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACN/D,OAAA,CAACxB,GAAG;YAACuH,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvC,QAAA,eACzB1D,OAAA,CAACK,QAAQ;cAAAqD,QAAA,eACP1D,OAAA,CAACvB,SAAS;gBACRuE,KAAK,EAAC,0BAAM;gBACZkD,KAAK,EAAEnF,OAAO,CAACuF,eAAgB;gBAC/BF,MAAM,eAAEpG,OAAA,CAACf,mBAAmB;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAChCsC,UAAU,EAAE;kBAAE9F,KAAK,EAAE;gBAAU;cAAE;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACN/D,OAAA,CAACxB,GAAG;YAACuH,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvC,QAAA,eACzB1D,OAAA,CAACK,QAAQ;cAAAqD,QAAA,eACP1D,OAAA,CAACvB,SAAS;gBACRuE,KAAK,EAAC,0BAAM;gBACZkD,KAAK,EAAEnF,OAAO,CAACwF,cAAe;gBAC9BH,MAAM,eAAEpG,OAAA,CAAChB,WAAW;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACxBsC,UAAU,EAAE;kBAAE9F,KAAK,EAAE;gBAAU;cAAE;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACN/D,OAAA,CAACxB,GAAG;YAACuH,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvC,QAAA,eACzB1D,OAAA,CAACK,QAAQ;cAAAqD,QAAA,eACP1D,OAAA,CAACvB,SAAS;gBACRuE,KAAK,EAAC,0BAAM;gBACZkD,KAAK,EAAEpF,QAAQ,CAAC0F,cAAe;gBAC/BC,MAAM,EAAC,IAAI;gBACXJ,UAAU,EAAE;kBAAE9F,KAAK,EAAE;gBAAU;cAAE;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/D,OAAA,CAACG,UAAU;UAAC6C,KAAK,EAAC,0BAAM;UAACS,KAAK,EAAE;YAAEiD,SAAS,EAAE;UAAG,CAAE;UAAAhD,QAAA,eAChD1D,OAAA,CAACnB,YAAY;YAAC8H,MAAM,EAAE,CAAE;YAACC,QAAQ;YAACC,IAAI,EAAC,OAAO;YAAAnD,QAAA,gBAC5C1D,OAAA,CAACnB,YAAY,CAACiI,IAAI;cAACC,KAAK,EAAC,oBAAK;cAAArD,QAAA,EAAE5C,QAAQ,CAAC8C;YAAQ;cAAAA,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eACtE/D,OAAA,CAACnB,YAAY,CAACiI,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAArD,QAAA,GAAE,CAAC5C,QAAQ,CAACkG,QAAQ,GAAG,IAAI,EAAErD,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC,eAC9F/D,OAAA,CAACnB,YAAY,CAACiI,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAArD,QAAA,EAAE,IAAIa,IAAI,CAACzD,QAAQ,CAACmG,SAAS,CAAC,CAACC,cAAc,CAAC;YAAC;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eACnG/D,OAAA,CAACnB,YAAY,CAACiI,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAArD,QAAA,eAC7B1D,OAAA,CAAC3B,GAAG;gBAACkC,KAAK,EAAEmC,cAAc,CAAC3B,OAAO,CAACoG,aAAa,CAAE;gBAAAzD,QAAA,EAC/C3C,OAAO,CAACoG,aAAa,CAACjD,WAAW,CAAC;cAAC;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEb/D,OAAA,CAACG,UAAU;UAAC6C,KAAK,EAAC,0BAAM;UAAAU,QAAA,eACtB1D,OAAA,CAACzB,GAAG;YAACuH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;YAAApC,QAAA,gBACpB1D,OAAA,CAACxB,GAAG;cAACuH,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvC,QAAA,eACzB1D,OAAA,CAACvB,SAAS;gBACRuE,KAAK,EAAC,0BAAM;gBACZkD,KAAK,EAAEjF,aAAa,CAACmG,QAAS;gBAC9BC,SAAS,EAAE,CAAE;gBACbhB,UAAU,EAAE;kBAAE9F,KAAK,EAAE;gBAAU;cAAE;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN/D,OAAA,CAACxB,GAAG;cAACuH,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvC,QAAA,eACzB1D,OAAA,CAACvB,SAAS;gBACRuE,KAAK,EAAC,0BAAM;gBACZkD,KAAK,EAAEjF,aAAa,CAACqG,QAAS;gBAC9BD,SAAS,EAAE,CAAE;gBACbhB,UAAU,EAAE;kBAAE9F,KAAK,EAAE;gBAAU;cAAE;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN/D,OAAA,CAACxB,GAAG;cAACuH,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvC,QAAA,eACzB1D,OAAA,CAACvB,SAAS;gBACRuE,KAAK,EAAC,0BAAM;gBACZkD,KAAK,EAAEjF,aAAa,CAACsG,YAAa;gBAClCF,SAAS,EAAE,CAAE;gBACbhB,UAAU,EAAE;kBAAE9F,KAAK,EAAE;gBAAU;cAAE;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN/D,OAAA,CAACxB,GAAG;cAACuH,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvC,QAAA,eACzB1D,OAAA,CAACvB,SAAS;gBACRuE,KAAK,EAAC,oBAAK;gBACXkD,KAAK,EAAEjF,aAAa,CAACuG,iBAAkB;gBACvCH,SAAS,EAAE,CAAE;gBACbhB,UAAU,EAAE;kBAAE9F,KAAK,EAAE;gBAAU;cAAE;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EAEZ3C,eAAe,CAACiD,MAAM,GAAG,CAAC,iBACzBrE,OAAA,CAACG,UAAU;UAAC6C,KAAK,EAAC,gCAAO;UAAAU,QAAA,eACvB1D,OAAA,CAACrB,KAAK;YAAC8I,SAAS,EAAC,UAAU;YAACZ,IAAI,EAAC,OAAO;YAACpD,KAAK,EAAE;cAAEN,KAAK,EAAE;YAAO,CAAE;YAAAO,QAAA,EAC/DtC,eAAe,CAACQ,GAAG,CAAC,CAAC8F,GAAG,EAAE7F,KAAK,kBAC9B7B,OAAA,CAACtB,KAAK;cAEJiJ,OAAO,EAAED,GAAG,CAAC1E,KAAM;cACnB4E,WAAW,EAAEF,GAAG,CAACE,WAAY;cAC7BxF,IAAI,EAAEsF,GAAG,CAACG,QAAQ,KAAK,UAAU,GAAG,OAAO,GAAGH,GAAG,CAACG,QAAQ,KAAK,MAAM,GAAG,SAAS,GAAG,MAAO;cAC3FC,QAAQ;YAAA,GAJHjG,KAAK;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKX,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACb;MAAA,GA7GG,UAAU;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA8GP,CAAC,eAGV/D,OAAA,CAACC,OAAO;QACN4F,GAAG,eACD7F,OAAA;UAAA0D,QAAA,gBACE1D,OAAA,CAACZ,iBAAiB;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,4BAEvB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QAAAL,QAAA,EAGArC,SAAS,CAACgD,MAAM,GAAG,CAAC,gBACnBrE,OAAA,CAACzB,GAAG;UAACuH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAApC,QAAA,gBACpB1D,OAAA,CAACxB,GAAG;YAACuH,EAAE,EAAE,EAAG;YAAArC,QAAA,eACV1D,OAAA,CAACG,UAAU;cAAC6C,KAAK,EAAC,sCAAQ;cAAAU,QAAA,eACxB1D,OAAA,CAACL,mBAAmB;gBAACwD,KAAK,EAAC,MAAM;gBAAC4E,MAAM,EAAE,GAAI;gBAAArE,QAAA,eAC5C1D,OAAA,CAACX,SAAS;kBAAC2I,IAAI,EAAE3G,SAAU;kBAAAqC,QAAA,gBACzB1D,OAAA,CAACP,aAAa;oBAACwI,eAAe,EAAC;kBAAK;oBAAArE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvC/D,OAAA,CAACT,KAAK;oBAAC2I,OAAO,EAAC;kBAAM;oBAAAtE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACxB/D,OAAA,CAACR,KAAK;oBAAAoE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACT/D,OAAA,CAACN,OAAO;oBAAAkE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACX/D,OAAA,CAACV,IAAI;oBACH8C,IAAI,EAAC,UAAU;oBACf8F,OAAO,EAAC,OAAO;oBACfC,MAAM,EAAC,SAAS;oBAChBC,WAAW,EAAE,CAAE;oBACfC,GAAG,EAAE;sBAAEC,IAAI,EAAE,SAAS;sBAAEF,WAAW,EAAE,CAAC;sBAAEG,CAAC,EAAE;oBAAE;kBAAE;oBAAA3E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,EAEL1B,gBAAgB,CAACgC,MAAM,GAAG,CAAC,iBAC1BrE,OAAA,CAACxB,GAAG;YAACuH,EAAE,EAAE,EAAG;YAAArC,QAAA,eACV1D,OAAA,CAACG,UAAU;cAAC6C,KAAK,EAAC,sCAAQ;cAAAU,QAAA,eACxB1D,OAAA,CAACL,mBAAmB;gBAACwD,KAAK,EAAC,MAAM;gBAAC4E,MAAM,EAAE,GAAI;gBAAArE,QAAA,eAC5C1D,OAAA,CAACJ,QAAQ;kBAACoI,IAAI,EAAE3F,gBAAiB;kBAAAqB,QAAA,gBAC/B1D,OAAA,CAACP,aAAa;oBAACwI,eAAe,EAAC;kBAAK;oBAAArE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvC/D,OAAA,CAACT,KAAK;oBAAC2I,OAAO,EAAC;kBAAM;oBAAAtE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACxB/D,OAAA,CAACR,KAAK;oBAAAoE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACT/D,OAAA,CAACN,OAAO;oBAAAkE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACX/D,OAAA,CAACH,GAAG;oBAACqI,OAAO,EAAC,OAAO;oBAACI,IAAI,EAAC;kBAAS;oBAAA1E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,gBAEN/D,OAAA,CAACtB,KAAK;UACJiJ,OAAO,EAAC,gCAAO;UACfC,WAAW,EAAC,sIAAwB;UACpCxF,IAAI,EAAC,MAAM;UACX0F,QAAQ;QAAA;UAAAlE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MACF,GA/CG,QAAQ;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgDL,CAAC,eAGV/D,OAAA,CAACC,OAAO;QACN4F,GAAG,eACD7F,OAAA;UAAA0D,QAAA,gBACE1D,OAAA,CAACjB,aAAa;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,4BAEnB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;QAAAL,QAAA,eAGD1D,OAAA,CAACG,UAAU;UAAC6C,KAAK,EAAE,UAAUhC,eAAe,CAACqD,MAAM,KAAM;UAAAX,QAAA,eACvD1D,OAAA,CAAC5B,KAAK;YACJoK,OAAO,EAAEzF,WAAY;YACrB0F,UAAU,EAAEzH,eAAgB;YAC5B0H,MAAM,EAAEA,CAACC,MAAM,EAAE9G,KAAK,KAAKA,KAAM;YACjC+G,UAAU,EAAE;cACVC,QAAQ,EAAE,EAAE;cACZC,eAAe,EAAE,IAAI;cACrBC,eAAe,EAAE,IAAI;cACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;YAClC,CAAE;YACFC,MAAM,EAAE;cAAEC,CAAC,EAAE;YAAI,CAAE;YACnBtC,IAAI,EAAC;UAAO;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ;MAAC,GAhBT,MAAM;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAiBH,CAAC,eAGV/D,OAAA,CAACC,OAAO;QACN4F,GAAG,eACD7F,OAAA;UAAA0D,QAAA,gBACE1D,OAAA,CAAChB,WAAW;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,6BACV,EAAC7C,SAAS,CAACmD,MAAM,GAAG,CAAC,iBAAIrE,OAAA;YAAMyD,KAAK,EAAE;cAAElD,KAAK,EAAE;YAAU,CAAE;YAAAmD,QAAA,GAAC,GAAC,EAACxC,SAAS,CAACmD,MAAM,EAAC,GAAC;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxF,CACP;QAAAL,QAAA,GAGAxC,SAAS,CAACmD,MAAM,GAAG,CAAC,gBACnBrE,OAAA,CAACG,UAAU;UAAC6C,KAAK,EAAE,WAAW9B,SAAS,CAACmD,MAAM,KAAM;UAAAX,QAAA,eAClD1D,OAAA,CAAC5B,KAAK;YACJoK,OAAO,EAAEvE,cAAe;YACxBwE,UAAU,EAAEvH,SAAU;YACtBwH,MAAM,EAAEA,CAACC,MAAM,EAAE9G,KAAK,KAAKA,KAAM;YACjC+G,UAAU,EAAE;cACVC,QAAQ,EAAE,EAAE;cACZC,eAAe,EAAE,IAAI;cACrBE,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;YAClC,CAAE;YACFC,MAAM,EAAE;cAAEC,CAAC,EAAE;YAAI,CAAE;YACnBtC,IAAI,EAAC;UAAO;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,gBAEb/D,OAAA,CAACtB,KAAK;UACJiJ,OAAO,EAAC,gCAAO;UACfC,WAAW,EAAC,kGAAkB;UAC9BxF,IAAI,EAAC,SAAS;UACd0F,QAAQ;UACRrE,KAAK,EAAE;YAAE8B,YAAY,EAAE;UAAG;QAAE;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CACF,EAEA5C,UAAU,CAACiI,MAAM,CAAC/E,MAAM,GAAG,CAAC,iBAC3BrE,OAAA,CAACG,UAAU;UAAC6C,KAAK,EAAC,0BAAM;UAAAU,QAAA,eACtB1D,OAAA,CAACrB,KAAK;YAAC8I,SAAS,EAAC,UAAU;YAACZ,IAAI,EAAC,OAAO;YAACpD,KAAK,EAAE;cAAEN,KAAK,EAAE;YAAO,CAAE;YAAAO,QAAA,EAC/DvC,UAAU,CAACiI,MAAM,CAACxH,GAAG,CAAC,CAACyH,KAAK,EAAExH,KAAK,kBAClC7B,OAAA,CAACtB,KAAK;cAEJiJ,OAAO,EAAE0B,KAAK,CAACzB,WAAY;cAC3BA,WAAW,EAAEyB,KAAK,CAACC,UAAW;cAC9BlH,IAAI,EAAC,OAAO;cACZ0F,QAAQ;YAAA,GAJHjG,KAAK;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKX,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACb,EAEA5C,UAAU,CAACoI,QAAQ,CAAClF,MAAM,GAAG,CAAC,iBAC7BrE,OAAA,CAACG,UAAU;UAAC6C,KAAK,EAAC,0BAAM;UAAAU,QAAA,eACtB1D,OAAA,CAACrB,KAAK;YAAC8I,SAAS,EAAC,UAAU;YAACZ,IAAI,EAAC,OAAO;YAACpD,KAAK,EAAE;cAAEN,KAAK,EAAE;YAAO,CAAE;YAAAO,QAAA,EAC/DvC,UAAU,CAACoI,QAAQ,CAAC3H,GAAG,CAAC,CAAC4H,OAAO,EAAE3H,KAAK,kBACtC7B,OAAA,CAACtB,KAAK;cAEJiJ,OAAO,EAAE6B,OAAO,CAAC5B,WAAY;cAC7BA,WAAW,EAAE4B,OAAO,CAACF,UAAW;cAChClH,IAAI,EAAC,SAAS;cACd0F,QAAQ;YAAA,GAJHjG,KAAK;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKX,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACb;MAAA,GAzDG,WAAW;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA0DR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACpD,EAAA,CAjcIF,cAAc;AAAAgJ,GAAA,GAAdhJ,cAAc;AAmcpB,eAAeA,cAAc;AAAC,IAAAL,EAAA,EAAAI,GAAA,EAAAiJ,GAAA;AAAAC,YAAA,CAAAtJ,EAAA;AAAAsJ,YAAA,CAAAlJ,GAAA;AAAAkJ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n/* eslint react/prop-types:0 */\nimport React, { Component } from 'react';\nimport AjaxUpload from \"./AjaxUploader\";\nfunction empty() {}\nvar Upload = /*#__PURE__*/function (_Component) {\n  _inherits(Upload, _Component);\n  var _super = _createSuper(Upload);\n  function Upload() {\n    var _this;\n    _classCallCheck(this, Upload);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"uploader\", void 0);\n    _defineProperty(_assertThisInitialized(_this), \"saveUploader\", function (node) {\n      _this.uploader = node;\n    });\n    return _this;\n  }\n  _createClass(Upload, [{\n    key: \"abort\",\n    value: function abort(file) {\n      this.uploader.abort(file);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      return /*#__PURE__*/React.createElement(AjaxUpload, _extends({}, this.props, {\n        ref: this.saveUploader\n      }));\n    }\n  }]);\n  return Upload;\n}(Component);\n_defineProperty(Upload, \"defaultProps\", {\n  component: 'span',\n  prefixCls: 'rc-upload',\n  data: {},\n  headers: {},\n  name: 'file',\n  multipart: false,\n  onStart: empty,\n  onError: empty,\n  onSuccess: empty,\n  multiple: false,\n  beforeUpload: null,\n  customRequest: null,\n  withCredentials: false,\n  openFileDialogOnClick: true,\n  hasControlInside: false\n});\nexport default Upload;", "map": {"version": 3, "names": ["_extends", "_classCallCheck", "_createClass", "_assertThisInitialized", "_inherits", "_createSuper", "_defineProperty", "React", "Component", "AjaxUpload", "empty", "Upload", "_Component", "_super", "_this", "_len", "arguments", "length", "args", "Array", "_key", "call", "apply", "concat", "node", "uploader", "key", "value", "abort", "file", "render", "createElement", "props", "ref", "saveUploader", "component", "prefixCls", "data", "headers", "name", "multipart", "onStart", "onError", "onSuccess", "multiple", "beforeUpload", "customRequest", "withCredentials", "openFileDialogOnClick", "hasControlInside"], "sources": ["C:/Users/<USER>/Documents/augment-projects/xmlCheck/web-app/client/node_modules/rc-upload/es/Upload.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n/* eslint react/prop-types:0 */\nimport React, { Component } from 'react';\nimport AjaxUpload from \"./AjaxUploader\";\nfunction empty() {}\nvar Upload = /*#__PURE__*/function (_Component) {\n  _inherits(Upload, _Component);\n  var _super = _createSuper(Upload);\n  function Upload() {\n    var _this;\n    _classCallCheck(this, Upload);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"uploader\", void 0);\n    _defineProperty(_assertThisInitialized(_this), \"saveUploader\", function (node) {\n      _this.uploader = node;\n    });\n    return _this;\n  }\n  _createClass(Upload, [{\n    key: \"abort\",\n    value: function abort(file) {\n      this.uploader.abort(file);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      return /*#__PURE__*/React.createElement(AjaxUpload, _extends({}, this.props, {\n        ref: this.saveUploader\n      }));\n    }\n  }]);\n  return Upload;\n}(Component);\n_defineProperty(Upload, \"defaultProps\", {\n  component: 'span',\n  prefixCls: 'rc-upload',\n  data: {},\n  headers: {},\n  name: 'file',\n  multipart: false,\n  onStart: empty,\n  onError: empty,\n  onSuccess: empty,\n  multiple: false,\n  beforeUpload: null,\n  customRequest: null,\n  withCredentials: false,\n  openFileDialogOnClick: true,\n  hasControlInside: false\n});\nexport default Upload;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE;AACA,OAAOC,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,UAAU,MAAM,gBAAgB;AACvC,SAASC,KAAKA,CAAA,EAAG,CAAC;AAClB,IAAIC,MAAM,GAAG,aAAa,UAAUC,UAAU,EAAE;EAC9CR,SAAS,CAACO,MAAM,EAAEC,UAAU,CAAC;EAC7B,IAAIC,MAAM,GAAGR,YAAY,CAACM,MAAM,CAAC;EACjC,SAASA,MAAMA,CAAA,EAAG;IAChB,IAAIG,KAAK;IACTb,eAAe,CAAC,IAAI,EAAEU,MAAM,CAAC;IAC7B,KAAK,IAAII,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IACAN,KAAK,GAAGD,MAAM,CAACQ,IAAI,CAACC,KAAK,CAACT,MAAM,EAAE,CAAC,IAAI,CAAC,CAACU,MAAM,CAACL,IAAI,CAAC,CAAC;IACtDZ,eAAe,CAACH,sBAAsB,CAACW,KAAK,CAAC,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;IAClER,eAAe,CAACH,sBAAsB,CAACW,KAAK,CAAC,EAAE,cAAc,EAAE,UAAUU,IAAI,EAAE;MAC7EV,KAAK,CAACW,QAAQ,GAAGD,IAAI;IACvB,CAAC,CAAC;IACF,OAAOV,KAAK;EACd;EACAZ,YAAY,CAACS,MAAM,EAAE,CAAC;IACpBe,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,SAASC,KAAKA,CAACC,IAAI,EAAE;MAC1B,IAAI,CAACJ,QAAQ,CAACG,KAAK,CAACC,IAAI,CAAC;IAC3B;EACF,CAAC,EAAE;IACDH,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASG,MAAMA,CAAA,EAAG;MACvB,OAAO,aAAavB,KAAK,CAACwB,aAAa,CAACtB,UAAU,EAAET,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACgC,KAAK,EAAE;QAC3EC,GAAG,EAAE,IAAI,CAACC;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC,CAAC;EACH,OAAOvB,MAAM;AACf,CAAC,CAACH,SAAS,CAAC;AACZF,eAAe,CAACK,MAAM,EAAE,cAAc,EAAE;EACtCwB,SAAS,EAAE,MAAM;EACjBC,SAAS,EAAE,WAAW;EACtBC,IAAI,EAAE,CAAC,CAAC;EACRC,OAAO,EAAE,CAAC,CAAC;EACXC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,KAAK;EAChBC,OAAO,EAAE/B,KAAK;EACdgC,OAAO,EAAEhC,KAAK;EACdiC,SAAS,EAAEjC,KAAK;EAChBkC,QAAQ,EAAE,KAAK;EACfC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,IAAI;EACnBC,eAAe,EAAE,KAAK;EACtBC,qBAAqB,EAAE,IAAI;EAC3BC,gBAAgB,EAAE;AACpB,CAAC,CAAC;AACF,eAAetC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport TransBtn from \"../TransBtn\";\nimport React from 'react';\nexport var useAllowClear = function useAllowClear(prefixCls, onClearMouseDown, displayValues, allowClear, clearIcon) {\n  var disabled = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : false;\n  var mergedSearchValue = arguments.length > 6 ? arguments[6] : undefined;\n  var mode = arguments.length > 7 ? arguments[7] : undefined;\n  var mergedClearIcon = React.useMemo(function () {\n    if (_typeof(allowClear) === 'object') {\n      return allowClear.clearIcon;\n    }\n    if (clearIcon) {\n      return clearIcon;\n    }\n  }, [allowClear, clearIcon]);\n  var mergedAllowClear = React.useMemo(function () {\n    if (!disabled && !!allowClear && (displayValues.length || mergedSearchValue) && !(mode === 'combobox' && mergedSearchValue === '')) {\n      return true;\n    }\n    return false;\n  }, [allowClear, disabled, displayValues.length, mergedSearchValue, mode]);\n  return {\n    allowClear: mergedAllowClear,\n    clearIcon: /*#__PURE__*/React.createElement(TransBtn, {\n      className: \"\".concat(prefixCls, \"-clear\"),\n      onMouseDown: onClearMouseDown,\n      customizeIcon: mergedClearIcon\n    }, \"\\xD7\")\n  };\n};", "map": {"version": 3, "names": ["_typeof", "TransBtn", "React", "useAllowClear", "prefixCls", "onClearMouseDown", "displayValues", "allowClear", "clearIcon", "disabled", "arguments", "length", "undefined", "mergedSearchValue", "mode", "mergedClearIcon", "useMemo", "mergedAllowClear", "createElement", "className", "concat", "onMouseDown", "customizeIcon"], "sources": ["C:/Users/<USER>/Documents/augment-projects/xmlCheck/web-app/client/node_modules/rc-select/es/hooks/useAllowClear.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport TransBtn from \"../TransBtn\";\nimport React from 'react';\nexport var useAllowClear = function useAllowClear(prefixCls, onClearMouseDown, displayValues, allowClear, clearIcon) {\n  var disabled = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : false;\n  var mergedSearchValue = arguments.length > 6 ? arguments[6] : undefined;\n  var mode = arguments.length > 7 ? arguments[7] : undefined;\n  var mergedClearIcon = React.useMemo(function () {\n    if (_typeof(allowClear) === 'object') {\n      return allowClear.clearIcon;\n    }\n    if (clearIcon) {\n      return clearIcon;\n    }\n  }, [allowClear, clearIcon]);\n  var mergedAllowClear = React.useMemo(function () {\n    if (!disabled && !!allowClear && (displayValues.length || mergedSearchValue) && !(mode === 'combobox' && mergedSearchValue === '')) {\n      return true;\n    }\n    return false;\n  }, [allowClear, disabled, displayValues.length, mergedSearchValue, mode]);\n  return {\n    allowClear: mergedAllowClear,\n    clearIcon: /*#__PURE__*/React.createElement(TransBtn, {\n      className: \"\".concat(prefixCls, \"-clear\"),\n      onMouseDown: onClearMouseDown,\n      customizeIcon: mergedClearIcon\n    }, \"\\xD7\")\n  };\n};"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,SAAS,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAE;EACnH,IAAIC,QAAQ,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EACxF,IAAIG,iBAAiB,GAAGH,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGE,SAAS;EACvE,IAAIE,IAAI,GAAGJ,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGE,SAAS;EAC1D,IAAIG,eAAe,GAAGb,KAAK,CAACc,OAAO,CAAC,YAAY;IAC9C,IAAIhB,OAAO,CAACO,UAAU,CAAC,KAAK,QAAQ,EAAE;MACpC,OAAOA,UAAU,CAACC,SAAS;IAC7B;IACA,IAAIA,SAAS,EAAE;MACb,OAAOA,SAAS;IAClB;EACF,CAAC,EAAE,CAACD,UAAU,EAAEC,SAAS,CAAC,CAAC;EAC3B,IAAIS,gBAAgB,GAAGf,KAAK,CAACc,OAAO,CAAC,YAAY;IAC/C,IAAI,CAACP,QAAQ,IAAI,CAAC,CAACF,UAAU,KAAKD,aAAa,CAACK,MAAM,IAAIE,iBAAiB,CAAC,IAAI,EAAEC,IAAI,KAAK,UAAU,IAAID,iBAAiB,KAAK,EAAE,CAAC,EAAE;MAClI,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,EAAE,CAACN,UAAU,EAAEE,QAAQ,EAAEH,aAAa,CAACK,MAAM,EAAEE,iBAAiB,EAAEC,IAAI,CAAC,CAAC;EACzE,OAAO;IACLP,UAAU,EAAEU,gBAAgB;IAC5BT,SAAS,EAAE,aAAaN,KAAK,CAACgB,aAAa,CAACjB,QAAQ,EAAE;MACpDkB,SAAS,EAAE,EAAE,CAACC,MAAM,CAAChB,SAAS,EAAE,QAAQ,CAAC;MACzCiB,WAAW,EAAEhB,gBAAgB;MAC7BiB,aAAa,EAAEP;IACjB,CAAC,EAAE,MAAM;EACX,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
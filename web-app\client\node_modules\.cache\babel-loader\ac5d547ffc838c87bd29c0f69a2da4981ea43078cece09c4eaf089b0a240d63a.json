{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport cls from 'classnames';\nimport * as React from 'react';\nimport SliderContext from \"../context\";\nimport { getIndex } from \"../util\";\nimport Track from \"./Track\";\nvar Tracks = function Tracks(props) {\n  var prefixCls = props.prefixCls,\n    style = props.style,\n    values = props.values,\n    startPoint = props.startPoint,\n    onStartMove = props.onStartMove;\n  var _React$useContext = React.useContext(SliderContext),\n    included = _React$useContext.included,\n    range = _React$useContext.range,\n    min = _React$useContext.min,\n    styles = _React$useContext.styles,\n    classNames = _React$useContext.classNames;\n\n  // =========================== List ===========================\n  var trackList = React.useMemo(function () {\n    if (!range) {\n      // null value do not have track\n      if (values.length === 0) {\n        return [];\n      }\n      var startValue = startPoint !== null && startPoint !== void 0 ? startPoint : min;\n      var endValue = values[0];\n      return [{\n        start: Math.min(startValue, endValue),\n        end: Math.max(startValue, endValue)\n      }];\n    }\n\n    // Multiple\n    var list = [];\n    for (var i = 0; i < values.length - 1; i += 1) {\n      list.push({\n        start: values[i],\n        end: values[i + 1]\n      });\n    }\n    return list;\n  }, [values, range, startPoint, min]);\n  if (!included) {\n    return null;\n  }\n\n  // ========================== Render ==========================\n  var tracksNode = trackList !== null && trackList !== void 0 && trackList.length && (classNames.tracks || styles.tracks) ? /*#__PURE__*/React.createElement(Track, {\n    index: null,\n    prefixCls: prefixCls,\n    start: trackList[0].start,\n    end: trackList[trackList.length - 1].end,\n    replaceCls: cls(classNames.tracks, \"\".concat(prefixCls, \"-tracks\")),\n    style: styles.tracks\n  }) : null;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, tracksNode, trackList.map(function (_ref, index) {\n    var start = _ref.start,\n      end = _ref.end;\n    return /*#__PURE__*/React.createElement(Track, {\n      index: index,\n      prefixCls: prefixCls,\n      style: _objectSpread(_objectSpread({}, getIndex(style, index)), styles.track),\n      start: start,\n      end: end,\n      key: index,\n      onStartMove: onStartMove\n    });\n  }));\n};\nexport default Tracks;", "map": {"version": 3, "names": ["_objectSpread", "cls", "React", "SliderContext", "getIndex", "Track", "Tracks", "props", "prefixCls", "style", "values", "startPoint", "onStartMove", "_React$useContext", "useContext", "included", "range", "min", "styles", "classNames", "trackList", "useMemo", "length", "startValue", "endValue", "start", "Math", "end", "max", "list", "i", "push", "tracksNode", "tracks", "createElement", "index", "replaceCls", "concat", "Fragment", "map", "_ref", "track", "key"], "sources": ["C:/Users/<USER>/Documents/augment-projects/xmlCheck/web-app/client/node_modules/rc-slider/es/Tracks/index.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport cls from 'classnames';\nimport * as React from 'react';\nimport SliderContext from \"../context\";\nimport { getIndex } from \"../util\";\nimport Track from \"./Track\";\nvar Tracks = function Tracks(props) {\n  var prefixCls = props.prefixCls,\n    style = props.style,\n    values = props.values,\n    startPoint = props.startPoint,\n    onStartMove = props.onStartMove;\n  var _React$useContext = React.useContext(SliderContext),\n    included = _React$useContext.included,\n    range = _React$useContext.range,\n    min = _React$useContext.min,\n    styles = _React$useContext.styles,\n    classNames = _React$useContext.classNames;\n\n  // =========================== List ===========================\n  var trackList = React.useMemo(function () {\n    if (!range) {\n      // null value do not have track\n      if (values.length === 0) {\n        return [];\n      }\n      var startValue = startPoint !== null && startPoint !== void 0 ? startPoint : min;\n      var endValue = values[0];\n      return [{\n        start: Math.min(startValue, endValue),\n        end: Math.max(startValue, endValue)\n      }];\n    }\n\n    // Multiple\n    var list = [];\n    for (var i = 0; i < values.length - 1; i += 1) {\n      list.push({\n        start: values[i],\n        end: values[i + 1]\n      });\n    }\n    return list;\n  }, [values, range, startPoint, min]);\n  if (!included) {\n    return null;\n  }\n\n  // ========================== Render ==========================\n  var tracksNode = trackList !== null && trackList !== void 0 && trackList.length && (classNames.tracks || styles.tracks) ? /*#__PURE__*/React.createElement(Track, {\n    index: null,\n    prefixCls: prefixCls,\n    start: trackList[0].start,\n    end: trackList[trackList.length - 1].end,\n    replaceCls: cls(classNames.tracks, \"\".concat(prefixCls, \"-tracks\")),\n    style: styles.tracks\n  }) : null;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, tracksNode, trackList.map(function (_ref, index) {\n    var start = _ref.start,\n      end = _ref.end;\n    return /*#__PURE__*/React.createElement(Track, {\n      index: index,\n      prefixCls: prefixCls,\n      style: _objectSpread(_objectSpread({}, getIndex(style, index)), styles.track),\n      start: start,\n      end: end,\n      key: index,\n      onStartMove: onStartMove\n    });\n  }));\n};\nexport default Tracks;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,GAAG,MAAM,YAAY;AAC5B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,YAAY;AACtC,SAASC,QAAQ,QAAQ,SAAS;AAClC,OAAOC,KAAK,MAAM,SAAS;AAC3B,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,KAAK,EAAE;EAClC,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACnBC,MAAM,GAAGH,KAAK,CAACG,MAAM;IACrBC,UAAU,GAAGJ,KAAK,CAACI,UAAU;IAC7BC,WAAW,GAAGL,KAAK,CAACK,WAAW;EACjC,IAAIC,iBAAiB,GAAGX,KAAK,CAACY,UAAU,CAACX,aAAa,CAAC;IACrDY,QAAQ,GAAGF,iBAAiB,CAACE,QAAQ;IACrCC,KAAK,GAAGH,iBAAiB,CAACG,KAAK;IAC/BC,GAAG,GAAGJ,iBAAiB,CAACI,GAAG;IAC3BC,MAAM,GAAGL,iBAAiB,CAACK,MAAM;IACjCC,UAAU,GAAGN,iBAAiB,CAACM,UAAU;;EAE3C;EACA,IAAIC,SAAS,GAAGlB,KAAK,CAACmB,OAAO,CAAC,YAAY;IACxC,IAAI,CAACL,KAAK,EAAE;MACV;MACA,IAAIN,MAAM,CAACY,MAAM,KAAK,CAAC,EAAE;QACvB,OAAO,EAAE;MACX;MACA,IAAIC,UAAU,GAAGZ,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAGM,GAAG;MAChF,IAAIO,QAAQ,GAAGd,MAAM,CAAC,CAAC,CAAC;MACxB,OAAO,CAAC;QACNe,KAAK,EAAEC,IAAI,CAACT,GAAG,CAACM,UAAU,EAAEC,QAAQ,CAAC;QACrCG,GAAG,EAAED,IAAI,CAACE,GAAG,CAACL,UAAU,EAAEC,QAAQ;MACpC,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIK,IAAI,GAAG,EAAE;IACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,MAAM,CAACY,MAAM,GAAG,CAAC,EAAEQ,CAAC,IAAI,CAAC,EAAE;MAC7CD,IAAI,CAACE,IAAI,CAAC;QACRN,KAAK,EAAEf,MAAM,CAACoB,CAAC,CAAC;QAChBH,GAAG,EAAEjB,MAAM,CAACoB,CAAC,GAAG,CAAC;MACnB,CAAC,CAAC;IACJ;IACA,OAAOD,IAAI;EACb,CAAC,EAAE,CAACnB,MAAM,EAAEM,KAAK,EAAEL,UAAU,EAAEM,GAAG,CAAC,CAAC;EACpC,IAAI,CAACF,QAAQ,EAAE;IACb,OAAO,IAAI;EACb;;EAEA;EACA,IAAIiB,UAAU,GAAGZ,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,IAAIA,SAAS,CAACE,MAAM,KAAKH,UAAU,CAACc,MAAM,IAAIf,MAAM,CAACe,MAAM,CAAC,GAAG,aAAa/B,KAAK,CAACgC,aAAa,CAAC7B,KAAK,EAAE;IAChK8B,KAAK,EAAE,IAAI;IACX3B,SAAS,EAAEA,SAAS;IACpBiB,KAAK,EAAEL,SAAS,CAAC,CAAC,CAAC,CAACK,KAAK;IACzBE,GAAG,EAAEP,SAAS,CAACA,SAAS,CAACE,MAAM,GAAG,CAAC,CAAC,CAACK,GAAG;IACxCS,UAAU,EAAEnC,GAAG,CAACkB,UAAU,CAACc,MAAM,EAAE,EAAE,CAACI,MAAM,CAAC7B,SAAS,EAAE,SAAS,CAAC,CAAC;IACnEC,KAAK,EAAES,MAAM,CAACe;EAChB,CAAC,CAAC,GAAG,IAAI;EACT,OAAO,aAAa/B,KAAK,CAACgC,aAAa,CAAChC,KAAK,CAACoC,QAAQ,EAAE,IAAI,EAAEN,UAAU,EAAEZ,SAAS,CAACmB,GAAG,CAAC,UAAUC,IAAI,EAAEL,KAAK,EAAE;IAC7G,IAAIV,KAAK,GAAGe,IAAI,CAACf,KAAK;MACpBE,GAAG,GAAGa,IAAI,CAACb,GAAG;IAChB,OAAO,aAAazB,KAAK,CAACgC,aAAa,CAAC7B,KAAK,EAAE;MAC7C8B,KAAK,EAAEA,KAAK;MACZ3B,SAAS,EAAEA,SAAS;MACpBC,KAAK,EAAET,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEI,QAAQ,CAACK,KAAK,EAAE0B,KAAK,CAAC,CAAC,EAAEjB,MAAM,CAACuB,KAAK,CAAC;MAC7EhB,KAAK,EAAEA,KAAK;MACZE,GAAG,EAAEA,GAAG;MACRe,GAAG,EAAEP,KAAK;MACVvB,WAAW,EAAEA;IACf,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC;AACD,eAAeN,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
declare global {
    interface Performance {
        interactionCount: number;
    }
}
/**
 * Returns the `interactionCount` value using the native API (if available)
 * or the polyfill estimate in this module.
 */
export declare const getInteractionCount: () => number;
/**
 * Feature detects native support or initializes the polyfill if needed.
 */
export declare const initInteractionCountPolyfill: () => void;

{"name": "workbox-core", "version": "6.6.0", "license": "MIT", "author": "Google's Web DevRel Team", "description": "This module is used by a number of the other Workbox modules to share common code.", "repository": "googlechrome/workbox", "bugs": "https://github.com/googlechrome/workbox/issues", "homepage": "https://github.com/GoogleChrome/workbox", "keywords": ["workbox", "workboxjs", "service worker", "sw"], "workbox": {"browserNamespace": "workbox.core", "packageType": "sw"}, "main": "index.js", "module": "index.mjs", "types": "index.d.ts", "gitHead": "252644491d9bb5a67518935ede6df530107c9475"}
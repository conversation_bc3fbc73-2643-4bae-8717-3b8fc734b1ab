@echo off
title VFX Tool Debug Starter

echo ========================================
echo   VFX Tool Debug Starter
echo ========================================
echo.
echo This script will show detailed information
echo to help diagnose any startup issues.
echo.

:: Change to script directory
echo Current directory: %CD%
cd /d "%~dp0"
echo Script directory: %CD%
echo.

:: Check Node.js installation
echo Checking Node.js installation...
node --version
if errorlevel 1 (
    echo.
    echo ERROR: Node.js is not installed or not in PATH
    echo.
    echo Please download and install Node.js from:
    echo https://nodejs.org
    echo.
    echo Make sure to restart your computer after installation.
    echo.
    pause
    exit /b 1
) else (
    echo Node.js is installed and working.
)

:: Check npm
echo.
echo Checking npm...
npm --version
if errorlevel 1 (
    echo ERROR: npm is not available
    pause
    exit /b 1
) else (
    echo npm is working.
)

:: Check required files
echo.
echo Checking required files...
if exist "server.js" (
    echo [OK] server.js found
) else (
    echo [ERROR] server.js not found
    echo Please make sure you are in the correct directory.
    pause
    exit /b 1
)

if exist "package.json" (
    echo [OK] package.json found
) else (
    echo [ERROR] package.json not found
    pause
    exit /b 1
)

if exist "src\xmlDetector.js" (
    echo [OK] xmlDetector.js found
) else (
    echo [ERROR] xmlDetector.js not found
    pause
    exit /b 1
)

:: Check dependencies
echo.
echo Checking dependencies...
if exist "node_modules" (
    echo [OK] node_modules directory exists
) else (
    echo [INFO] node_modules not found, will install dependencies
    echo.
    echo Installing dependencies...
    npm install
    if errorlevel 1 (
        echo.
        echo ERROR: Failed to install dependencies
        echo.
        echo This could be due to:
        echo 1. No internet connection
        echo 2. Firewall blocking npm
        echo 3. Proxy settings
        echo 4. Disk space issues
        echo.
        pause
        exit /b 1
    )
    echo Dependencies installed successfully.
)

:: Check for running processes
echo.
echo Checking for running Node.js processes...
tasklist /fi "imagename eq node.exe" 2>nul | find /i "node.exe" >nul
if not errorlevel 1 (
    echo Found running Node.js processes. Stopping them...
    taskkill /f /im node.exe >nul 2>&1
    timeout /t 3 /nobreak >nul
) else (
    echo No running Node.js processes found.
)

:: Check port availability
echo.
echo Checking port 3001...
netstat -an | findstr ":3001 " >nul
if not errorlevel 1 (
    echo WARNING: Port 3001 is in use
    echo The server will try to use an alternative port
) else (
    echo Port 3001 is available
)

:: Start server with detailed output
echo.
echo ========================================
echo   Starting VFX Analysis Server
echo ========================================
echo.
echo If the server starts successfully, you will see:
echo "XML Tool Web Server started successfully"
echo.
echo If you see any errors, please copy them and
echo contact support.
echo.
echo Starting server...
echo.

set PORT=3001
set NODE_ENV=development
node server.js

:: If we get here, the server has stopped
echo.
echo ========================================
echo   Server has stopped
echo ========================================
echo.
echo If the server stopped unexpectedly, please:
echo 1. Check the error messages above
echo 2. Try running as administrator
echo 3. Check Windows Defender/Antivirus settings
echo.
pause

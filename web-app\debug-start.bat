@echo off
title XML Tool Debug Launcher

echo ========================================
echo   XML Tool Debug Launcher
echo ========================================
echo.
echo This script will show detailed debug info
echo Window will not close automatically
echo.

echo [DEBUG] Current directory: %CD%
echo [DEBUG] Script location: %~dp0
echo.

echo [DEBUG] Changing to script directory...
cd /d "%~dp0"
echo [DEBUG] After change directory: %CD%
echo.

echo [DEBUG] Checking key files...
if exist "package.json" (
    echo [SUCCESS] Found package.json
) else (
    echo [ERROR] package.json not found
    echo [ERROR] Please run in correct project directory
    goto :error_exit
)

if exist "server.js" (
    echo [SUCCESS] Found server.js
) else (
    echo [ERROR] server.js not found
    echo [ERROR] Project files incomplete
    goto :error_exit
)
echo.

echo [DEBUG] Checking Node.js installation...
echo [DEBUG] Executing: node --version

node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js command failed
    echo [DEBUG] Trying to show detailed error...
    node --version
    echo.
    echo [SOLUTION] Please install Node.js:
    echo 1. Visit https://nodejs.org
    echo 2. Download Windows LTS version
    echo 3. Run installer, check "Add to PATH" option
    echo 4. Restart computer after installation
    echo 5. Run this script again
    echo.
    goto :error_exit
) else (
    for /f "tokens=*" %%i in ('node --version 2^>nul') do set NODE_VERSION=%%i
    echo [SUCCESS] Node.js version: %NODE_VERSION%
)
echo.

echo [DEBUG] Checking npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm not available
    echo [DEBUG] Trying to show npm error...
    npm --version
    goto :error_exit
) else (
    for /f "tokens=*" %%i in ('npm --version 2^>nul') do set NPM_VERSION=%%i
    echo [SUCCESS] npm version: %NPM_VERSION%
)
echo.

echo [DEBUG] Checking project dependencies...
if exist "node_modules" (
    echo [INFO] Dependencies already installed
) else (
    echo [INFO] Need to install dependencies
    echo [EXECUTE] npm install
    echo [TIP] This may take several minutes, please wait...
    echo.
    
    call npm install
    if errorlevel 1 (
        echo.
        echo [ERROR] Dependencies installation failed
        echo [DEBUG] Trying to show detailed error...
        echo [RETRY] Using verbose mode...
        call npm install --verbose
        if errorlevel 1 (
            echo [ERROR] Dependencies installation still failed
            goto :error_exit
        )
    )
    echo [SUCCESS] Dependencies installation completed
)
echo.

echo [DEBUG] Preparing to start server...
echo [INFO] If startup successful, visit: http://localhost:3001
echo [INFO] Press Ctrl+C to stop server
echo.
echo [EXECUTE] node server.js
echo ========================================
echo.

node server.js

echo.
echo ========================================
echo [INFO] Server stopped
goto :normal_exit

:error_exit
echo.
echo ========================================
echo [ERROR] Error occurred during startup
echo ========================================
echo.
echo Common solutions:
echo 1. Make sure Node.js is properly installed
echo 2. Restart computer and try again
echo 3. Run this script as administrator
echo 4. Check network connection
echo 5. Check detailed error messages above
echo.
goto :pause_exit

:normal_exit
echo [INFO] Script ended normally
echo.

:pause_exit
echo Press any key to close window...
pause >nul
exit /b

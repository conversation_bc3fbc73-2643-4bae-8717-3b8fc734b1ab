@echo off
title XML Tool Debug Launcher

echo ========================================
echo   XML Tool Debug Launcher
echo ========================================
echo.
echo This script will show detailed debug info
echo Window will not close automatically
echo.

echo [DEBUG] Current directory: %CD%
echo [DEBUG] Script location: %~dp0
echo.

echo [DEBUG] Changing to script directory...
cd /d "%~dp0"
echo [DEBUG] After change directory: %CD%
echo.

echo [DEBUG] Checking key files...
if exist "package.json" (
    echo [SUCCESS] Found package.json
) else (
    echo [ERROR] package.json not found
    echo [ERROR] Please run in correct project directory
    goto :error_exit
)

if exist "server.js" (
    echo [SUCCESS] Found server.js
) else (
    echo [ERROR] server.js not found
    echo [ERROR] Project files incomplete
    goto :error_exit
)
echo.

echo [DEBUG] Checking Node.js installation...
echo [DEBUG] Executing: node --version

node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js command failed
    echo [DEBUG] Trying to show detailed error...
    node --version
    echo.
    echo [SOLUTION] Please install Node.js:
    echo 1. Visit https://nodejs.org
    echo 2. Download Windows LTS version
    echo 3. Run installer, check "Add to PATH" option
    echo 4. Restart computer after installation
    echo 5. Run this script again
    echo.
    goto :error_exit
) else (
    for /f "tokens=*" %%i in ('node --version 2^>nul') do set NODE_VERSION=%%i
    echo [SUCCESS] Node.js version: %NODE_VERSION%
)
echo.

echo [DEBUG] Checking npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm not available
    echo [DEBUG] Trying to show npm error...
    npm --version
    goto :error_exit
) else (
    for /f "tokens=*" %%i in ('npm --version 2^>nul') do set NPM_VERSION=%%i
    echo [SUCCESS] npm version: %NPM_VERSION%
)
echo.

echo [DEBUG] Checking project dependencies...
if exist "node_modules" (
    echo [INFO] Dependencies already installed
) else (
    echo [INFO] Need to install dependencies
    echo [EXECUTE] npm install
    echo [TIP] This may take several minutes, please wait...
    echo.
    
    call npm install
    if errorlevel 1 (
        echo.
        echo [ERROR] Dependencies installation failed
        echo [DEBUG] Trying to show detailed error...
        echo [RETRY] Using verbose mode...
        call npm install --verbose
        if errorlevel 1 (
            echo [ERROR] Dependencies installation still failed
            goto :error_exit
        )
    )
    echo [SUCCESS] Dependencies installation completed
)
echo.

:: 检查并停止现有的Node.js进程
echo [DEBUG] Checking for existing Node.js processes...
tasklist /FI "IMAGENAME eq node.exe" 2>nul | find /I "node.exe" >nul
if not errorlevel 1 (
    echo [WARNING] Node.js processes are already running
    echo [ACTION] Attempting to stop existing servers...
    taskkill /F /IM node.exe >nul 2>&1
    timeout /t 3 /nobreak >nul
    echo [INFO] Existing processes stopped
)

:: 智能端口选择
echo [DEBUG] Checking port availability...
set "PORT=3001"
set "SERVER_URL=http://localhost:3001"

netstat -an | findstr ":3001" >nul 2>&1
if not errorlevel 1 (
    echo [WARNING] Port 3001 is in use, trying port 3002...
    set "PORT=3002"
    set "SERVER_URL=http://localhost:3002"

    netstat -an | findstr ":3002" >nul 2>&1
    if not errorlevel 1 (
        echo [WARNING] Port 3002 is also in use, trying port 3003...
        set "PORT=3003"
        set "SERVER_URL=http://localhost:3003"

        netstat -an | findstr ":3003" >nul 2>&1
        if not errorlevel 1 (
            echo [WARNING] Port 3003 is also in use, using emergency port 3004...
            set "PORT=3004"
            set "SERVER_URL=http://localhost:3004"
        )
    )
)

echo [SUCCESS] Using port: %PORT%
echo [INFO] Server URL: %SERVER_URL%
echo.

echo [DEBUG] Preparing to start server...
echo [INFO] If startup successful, visit: %SERVER_URL%
echo [INFO] Press Ctrl+C to stop server
echo.
echo [EXECUTE] node server.js with PORT=%PORT%
echo ========================================
echo.

set PORT=%PORT%
node server.js

:: 检查启动结果
if errorlevel 1 (
    echo.
    echo [ERROR] Server failed to start with port %PORT%
    echo [DEBUG] Trying emergency recovery...
    echo.

    :: 尝试使用随机端口
    set "PORT=3005"
    echo [RECOVERY] Trying emergency port 3005...
    set PORT=%PORT%
    node server.js

    if errorlevel 1 (
        echo [ERROR] Emergency recovery also failed
        goto :error_exit
    )
)

echo.
echo ========================================
echo [INFO] Server stopped
goto :normal_exit

:error_exit
echo.
echo ========================================
echo [ERROR] Error occurred during startup
echo ========================================
echo.
echo Common solutions:
echo 1. Make sure Node.js is properly installed
echo 2. Restart computer and try again
echo 3. Run this script as administrator
echo 4. Check network connection
echo 5. Check detailed error messages above
echo.
goto :pause_exit

:normal_exit
echo [INFO] Script ended normally
echo.

:pause_exit
echo Press any key to close window...
pause >nul
exit /b

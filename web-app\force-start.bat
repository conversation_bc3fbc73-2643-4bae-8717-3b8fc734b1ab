@echo off
title VFX Tool Force Starter

echo VFX XML Data Analysis Tool - Force Starter
echo ===========================================
echo.
echo This script will forcefully start the VFX analysis tool
echo by clearing all conflicts and using available ports.
echo.

cd /d "%~dp0"

echo Step 1: Stopping all Node.js processes...
taskkill /F /IM node.exe >nul 2>&1
timeout /t 2 /nobreak >nul

echo Step 2: Finding available port...
set "FOUND_PORT="

:: Try ports from 3001 to 3010
for %%p in (3001 3002 3003 3004 3005 3006 3007 3008 3009 3010) do (
    if not defined FOUND_PORT (
        netstat -an | findstr ":%%p" >nul 2>&1
        if errorlevel 1 (
            set "FOUND_PORT=%%p"
            echo Found available port: %%p
        )
    )
)

if not defined FOUND_PORT (
    echo ERROR: No available ports found between 3001-3010
    echo Please restart your computer and try again.
    pause
    exit
)

echo.
echo Step 3: Starting server on port %FOUND_PORT%...
echo.
echo Server will be available at: http://localhost:%FOUND_PORT%
echo.
echo Press Ctrl+C to stop the server
echo ========================================
echo.

set PORT=%FOUND_PORT%
node server.js

if errorlevel 1 (
    echo.
    echo ERROR: Server failed to start
    echo.
    echo Possible solutions:
    echo 1. Run as administrator
    echo 2. Check if Node.js is properly installed
    echo 3. Restart computer
    echo.
) else (
    echo.
    echo Server stopped normally
)

echo.
pause

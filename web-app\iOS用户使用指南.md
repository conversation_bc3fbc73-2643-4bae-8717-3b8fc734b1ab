# 📱 iOS用户使用指南 - XML变速数据检测工具

## 🎯 **重要说明**

**⚠️ 这不是一个可以直接点击的静态网页！**

这是一个完整的Web应用程序，需要启动后端服务器才能正常工作。

## 📋 **系统要求**

### **必需软件**
- **Node.js** 16.0 或更高版本
- **npm** (随Node.js自动安装)
- **终端应用** (macOS自带)

### **推荐配置**
- **macOS** 10.15 或更高版本
- **内存** 4GB 或更多
- **存储空间** 至少500MB可用空间

## 🚀 **安装和启动步骤**

### **步骤1: 安装Node.js**

1. **访问Node.js官网**：https://nodejs.org
2. **下载macOS版本**：选择LTS版本（推荐）
3. **安装Node.js**：双击下载的.pkg文件，按提示安装
4. **验证安装**：
   - 打开"终端"应用
   - 输入：`node --version`
   - 应该显示版本号，如：`v18.17.0`

### **步骤2: 解压和准备**

1. **解压文件**：双击压缩包解压到桌面
2. **打开终端**：在"应用程序" → "实用工具" → "终端"
3. **进入项目目录**：
   ```bash
   cd ~/Desktop/web-app
   ```

### **步骤3: 安装依赖**

在终端中执行：
```bash
npm install
```

等待安装完成（可能需要几分钟）。

### **步骤4: 启动服务器**

在终端中执行：
```bash
node server.js
```

看到以下信息表示启动成功：
```
🚀 XML变速数据检测工具Web服务器启动成功
📡 服务器地址: http://localhost:3001
🌍 环境: development
⏰ 启动时间: 2024-xx-xx xx:xx:xx
```

### **步骤5: 使用应用**

1. **打开浏览器**：Safari、Chrome或Firefox
2. **访问地址**：http://localhost:3001
3. **开始使用**：现在可以正常使用所有功能了！

## 🎯 **功能说明**

### **主要功能**
- **XML文件分析**：上传XML文件进行速度数据分析
- **文本输入分析**：直接输入XML内容进行分析
- **UNC路径读取**：读取网络共享文件夹中的XML文件
- **示例演示**：内置示例数据用于功能演示

### **页面导航**
- **主页**：http://localhost:3001
- **功能演示**：http://localhost:3001/demo-stable.html
- **UNC路径读取**：http://localhost:3001/unc-reader.html
- **API文档**：http://localhost:3001/api-docs.html

## 🔧 **常见问题解决**

### **Q1: 终端显示"command not found: node"**
**解决**：Node.js未正确安装
- 重新下载并安装Node.js
- 重启终端应用
- 确认安装的是macOS版本

### **Q2: npm install失败**
**解决**：网络或权限问题
```bash
# 尝试清除缓存
npm cache clean --force

# 重新安装
npm install
```

### **Q3: 端口3001被占用**
**解决**：更改端口
```bash
# 使用其他端口启动
PORT=3002 node server.js
```
然后访问：http://localhost:3002

### **Q4: 浏览器无法访问localhost**
**解决**：
- 确认服务器正在运行（终端显示启动信息）
- 尝试使用127.0.0.1:3001
- 检查防火墙设置

### **Q5: 功能按钮无响应**
**解决**：
- 刷新浏览器页面（Command+R）
- 清除浏览器缓存
- 尝试使用其他浏览器

## 🛑 **停止服务器**

使用完毕后，在终端中按：
```
Ctrl + C
```
或者直接关闭终端窗口。

## 📱 **移动设备访问**

如果需要在iPhone/iPad上访问：

1. **确保设备在同一WiFi网络**
2. **查找Mac的IP地址**：
   ```bash
   ifconfig | grep "inet " | grep -v 127.0.0.1
   ```
3. **在移动设备浏览器访问**：
   ```
   http://[Mac的IP地址]:3001
   ```
   例如：http://*************:3001

## 🔒 **安全注意事项**

- **仅在受信任的网络环境中使用**
- **不要在公共WiFi上启动服务器**
- **使用完毕后及时停止服务器**
- **不要将服务器暴露到互联网**

## 📞 **技术支持**

如果遇到问题：

1. **检查错误信息**：查看终端中的错误提示
2. **重启服务器**：停止后重新启动
3. **重新安装**：删除node_modules文件夹，重新npm install
4. **系统重启**：重启Mac后重试

## 🎉 **开始使用**

按照以上步骤操作后，您就可以：

- ✅ 分析XML速度数据文件
- ✅ 检测数据异常
- ✅ 生成详细报告
- ✅ 使用网络文件读取功能
- ✅ 体验完整的Web应用功能

**祝您使用愉快！** 🚀

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XML变速数据检测工具 - API文档</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 20px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .nav {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .nav ul {
            list-style: none;
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .nav a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 4px;
            transition: background 0.3s;
        }
        
        .nav a:hover {
            background: #f0f8ff;
        }
        
        .section {
            background: white;
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .section h2 {
            color: #667eea;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f8ff;
        }
        
        .endpoint {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 0.9rem;
            margin-right: 10px;
        }
        
        .method.get {
            background: #d4edda;
            color: #155724;
        }
        
        .method.post {
            background: #cce5ff;
            color: #004085;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        
        .response-example {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .parameter-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        
        .parameter-table th,
        .parameter-table td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }
        
        .parameter-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .required {
            color: #dc3545;
            font-weight: bold;
        }
        
        .optional {
            color: #6c757d;
        }
        
        .back-link {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            text-decoration: none;
            margin-bottom: 20px;
            transition: background 0.3s;
        }
        
        .back-link:hover {
            background: #5a6fd8;
        }
        
        @media (max-width: 768px) {
            .nav ul {
                flex-direction: column;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="/" class="back-link">← 返回主页</a>
        
        <div class="header">
            <h1>📚 API文档</h1>
            <p>XML变速数据检测工具 RESTful API 接口文档</p>
        </div>
        
        <div class="nav">
            <ul>
                <li><a href="#overview">概览</a></li>
                <li><a href="#authentication">认证</a></li>
                <li><a href="#endpoints">接口列表</a></li>
                <li><a href="#examples">使用示例</a></li>
                <li><a href="#errors">错误处理</a></li>
            </ul>
        </div>
        
        <div class="section" id="overview">
            <h2>🌐 API概览</h2>
            <p><strong>基础URL:</strong> <code>http://localhost:3001/api</code></p>
            <p><strong>协议:</strong> HTTP/HTTPS</p>
            <p><strong>数据格式:</strong> JSON</p>
            <p><strong>字符编码:</strong> UTF-8</p>
            
            <h3>功能特性</h3>
            <ul>
                <li>✅ XML文件上传和分析</li>
                <li>✅ XML文本内容直接分析</li>
                <li>✅ 速度数据自动检测</li>
                <li>✅ 异常数据识别</li>
                <li>✅ 统计分析和数据验证</li>
                <li>✅ 示例文件管理</li>
            </ul>
        </div>
        
        <div class="section" id="authentication">
            <h2>🔐 认证方式</h2>
            <p>当前版本无需认证，但有以下限制：</p>
            <ul>
                <li><strong>请求频率限制:</strong> 每15分钟最多100次请求</li>
                <li><strong>文件大小限制:</strong> 最大10MB</li>
                <li><strong>支持的文件类型:</strong> .xml, text/xml, application/xml</li>
            </ul>
        </div>
        
        <div class="section" id="endpoints">
            <h2>🔌 API接口列表</h2>
            
            <div class="endpoint">
                <h3>
                    <span class="method get">GET</span>
                    /api/health
                </h3>
                <p><strong>功能:</strong> 检查API服务健康状态</p>
                <p><strong>参数:</strong> 无</p>
                
                <h4>响应示例:</h4>
                <div class="code-block">
{
  "status": "ok",
  "timestamp": "2025-06-03T09:05:43.908Z",
  "version": "1.0.0"
}
                </div>
            </div>
            
            <div class="endpoint">
                <h3>
                    <span class="method post">POST</span>
                    /api/analyze
                </h3>
                <p><strong>功能:</strong> 上传XML文件进行分析</p>
                <p><strong>Content-Type:</strong> multipart/form-data</p>
                
                <h4>请求参数:</h4>
                <table class="parameter-table">
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必需</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>xmlFile</td>
                        <td>File</td>
                        <td class="required">是</td>
                        <td>XML文件，最大10MB</td>
                    </tr>
                </table>
                
                <h4>使用示例:</h4>
                <div class="code-block">
curl -X POST http://localhost:3001/api/analyze \
  -F "xmlFile=@example.xml"
                </div>
            </div>
            
            <div class="endpoint">
                <h3>
                    <span class="method post">POST</span>
                    /api/analyze-text
                </h3>
                <p><strong>功能:</strong> 分析XML文本内容</p>
                <p><strong>Content-Type:</strong> application/json</p>
                
                <h4>请求参数:</h4>
                <table class="parameter-table">
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必需</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>xmlContent</td>
                        <td>String</td>
                        <td class="required">是</td>
                        <td>XML文本内容</td>
                    </tr>
                    <tr>
                        <td>fileName</td>
                        <td>String</td>
                        <td class="optional">否</td>
                        <td>文件名，默认为"untitled.xml"</td>
                    </tr>
                </table>
                
                <h4>使用示例:</h4>
                <div class="code-block">
curl -X POST http://localhost:3001/api/analyze-text \
  -H "Content-Type: application/json" \
  -d '{
    "xmlContent": "&lt;test&gt;&lt;measurement speed=\"50.0\" time=\"1.0\"/&gt;&lt;/test&gt;",
    "fileName": "test.xml"
  }'
                </div>

                <h4>成功响应示例:</h4>
                <div class="code-block">
{
  "success": true,
  "data": {
    "metadata": {
      "fileName": "test.xml",
      "fileSize": 65,
      "processingTime": 15,
      "timestamp": "2025-06-03T09:05:43.908Z"
    },
    "summary": {
      "totalDataPoints": 1,
      "validDataPoints": 1,
      "anomaliesCount": 0,
      "validationErrors": 0,
      "validationWarnings": 1,
      "overallStatus": "pass"
    },
    "speedAnalysis": {
      "minSpeed": 50.0,
      "maxSpeed": 50.0,
      "averageSpeed": 50.0,
      "standardDeviation": 0.0,
      "speedChanges": []
    },
    "anomalies": [],
    "validation": {
      "errors": [],
      "warnings": [
        {
          "type": "missing_time_data",
          "description": "0 个数据点缺少时间信息",
          "suggestion": "建议为所有数据点添加时间戳"
        }
      ]
    },
    "recommendations": []
  },
  "fileName": "test.xml",
  "fileSize": 65,
  "timestamp": "2025-06-03T09:05:43.908Z"
}
                </div>
            </div>
            
            <div class="endpoint">
                <h3>
                    <span class="method get">GET</span>
                    /api/examples
                </h3>
                <p><strong>功能:</strong> 获取示例文件列表</p>
                <p><strong>参数:</strong> 无</p>
                
                <h4>响应示例:</h4>
                <div class="code-block">
{
  "success": true,
  "data": [
    {
      "name": "sample_speed_data.xml",
      "description": "正常的车辆速度测试数据",
      "category": "normal"
    },
    {
      "name": "anomaly_data.xml",
      "description": "包含各种异常的测试数据",
      "category": "anomaly"
    }
  ]
}
                </div>
            </div>
            
            <div class="endpoint">
                <h3>
                    <span class="method get">GET</span>
                    /api/examples/:filename
                </h3>
                <p><strong>功能:</strong> 获取指定示例文件内容</p>
                
                <h4>路径参数:</h4>
                <table class="parameter-table">
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>filename</td>
                        <td>String</td>
                        <td>示例文件名</td>
                    </tr>
                </table>
                
                <h4>使用示例:</h4>
                <div class="code-block">
curl http://localhost:3001/api/examples/sample_speed_data.xml
                </div>
            </div>
        </div>
        
        <div class="section" id="examples">
            <h2>💡 完整使用示例</h2>
            
            <h3>JavaScript (浏览器)</h3>
            <div class="code-block">
// 分析XML文本
async function analyzeXML(xmlContent) {
  try {
    const response = await fetch('/api/analyze-text', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        xmlContent: xmlContent,
        fileName: 'test.xml'
      })
    });
    
    const result = await response.json();
    console.log('分析结果:', result);
  } catch (error) {
    console.error('分析失败:', error);
  }
}
            </div>
            
            <h3>Python</h3>
            <div class="code-block">
import requests

# 分析XML文件
def analyze_xml_file(file_path):
    url = 'http://localhost:3001/api/analyze'
    
    with open(file_path, 'rb') as f:
        files = {'xmlFile': f}
        response = requests.post(url, files=files)
    
    return response.json()

# 分析XML文本
def analyze_xml_text(xml_content, file_name='test.xml'):
    url = 'http://localhost:3001/api/analyze-text'
    data = {
        'xmlContent': xml_content,
        'fileName': file_name
    }
    
    response = requests.post(url, json=data)
    return response.json()
            </div>
        </div>
        
        <div class="section" id="errors">
            <h2>⚠️ 错误处理</h2>
            
            <h3>HTTP状态码</h3>
            <table class="parameter-table">
                <tr>
                    <th>状态码</th>
                    <th>说明</th>
                </tr>
                <tr>
                    <td>200</td>
                    <td>请求成功</td>
                </tr>
                <tr>
                    <td>400</td>
                    <td>请求参数错误</td>
                </tr>
                <tr>
                    <td>404</td>
                    <td>接口不存在</td>
                </tr>
                <tr>
                    <td>429</td>
                    <td>请求过于频繁</td>
                </tr>
                <tr>
                    <td>500</td>
                    <td>服务器内部错误</td>
                </tr>
            </table>
            
            <h3>错误响应格式</h3>
            <div class="code-block">
{
  "error": "错误描述信息",
  "timestamp": "2025-06-03T09:05:43.908Z"
}
            </div>
            
            <h3>常见错误</h3>
            <ul>
                <li><strong>文件过大:</strong> "文件大小超过限制（最大10MB）"</li>
                <li><strong>文件格式错误:</strong> "只支持XML文件"</li>
                <li><strong>XML格式错误:</strong> "XML解析失败: [具体错误信息]"</li>
                <li><strong>请求过频:</strong> "请求过于频繁，请稍后再试"</li>
            </ul>
        </div>
        
        <div class="section">
            <h2>🔗 相关链接</h2>
            <ul>
                <li><a href="/">返回主页</a></li>
                <li><a href="/demo.html">功能演示</a></li>
                <li><a href="/test.html">完整界面</a></li>
                <li><a href="/api/health" target="_blank">API健康检查</a></li>
            </ul>
        </div>
    </div>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
            border: 1px solid #ddd;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .debug {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 调试页面</h1>
        <p>用于测试JavaScript功能和API连接</p>
        
        <h3>基础测试</h3>
        <button onclick="testBasicJS()">测试JavaScript</button>
        <button onclick="testConsole()">测试控制台</button>
        <div id="basicResult" class="result" style="display:none;"></div>
        
        <h3>API测试</h3>
        <button onclick="testAPIHealth()">测试API健康检查</button>
        <button onclick="testAPIAnalyze()">测试XML分析</button>
        <div id="apiResult" class="result" style="display:none;"></div>
        
        <h3>网络测试</h3>
        <button onclick="testFetch()">测试Fetch API</button>
        <button onclick="testCORS()">测试CORS</button>
        <div id="networkResult" class="result" style="display:none;"></div>
        
        <h3>调试信息</h3>
        <div id="debugInfo" class="result debug">
            <strong>浏览器信息:</strong><br>
            User Agent: <span id="userAgent"></span><br>
            当前URL: <span id="currentURL"></span><br>
            协议: <span id="protocol"></span><br>
            主机: <span id="host"></span><br>
        </div>
    </div>

    <script>
        // 显示调试信息
        document.getElementById('userAgent').textContent = navigator.userAgent;
        document.getElementById('currentURL').textContent = window.location.href;
        document.getElementById('protocol').textContent = window.location.protocol;
        document.getElementById('host').textContent = window.location.host;
        
        // 显示结果的通用函数
        function showResult(elementId, content, type = 'debug') {
            console.log(`显示结果: ${elementId}, 类型: ${type}, 内容: ${content}`);
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = content;
                element.className = `result ${type}`;
                element.style.display = 'block';
            } else {
                console.error(`找不到元素: ${elementId}`);
            }
        }
        
        // 测试基础JavaScript
        function testBasicJS() {
            console.log('测试基础JavaScript功能');
            try {
                const testData = {
                    time: new Date().toLocaleString(),
                    random: Math.random(),
                    test: 'JavaScript正常工作'
                };
                showResult('basicResult', `✅ JavaScript正常工作!\n时间: ${testData.time}\n随机数: ${testData.random.toFixed(4)}`, 'success');
            } catch (error) {
                console.error('JavaScript测试失败:', error);
                showResult('basicResult', `❌ JavaScript错误: ${error.message}`, 'error');
            }
        }
        
        // 测试控制台
        function testConsole() {
            console.log('这是一条测试日志');
            console.warn('这是一条警告');
            console.error('这是一条错误（测试用）');
            showResult('basicResult', '✅ 控制台测试完成，请查看浏览器开发者工具的控制台', 'success');
        }
        
        // 测试API健康检查
        async function testAPIHealth() {
            console.log('开始测试API健康检查');
            try {
                showResult('apiResult', '🔄 正在检查API状态...', 'debug');
                
                const response = await fetch('/api/health');
                console.log('API响应状态:', response.status);
                console.log('API响应头:', response.headers);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('API响应数据:', data);
                
                showResult('apiResult', `✅ API健康检查成功!\n状态: ${data.status}\n版本: ${data.version}\n时间: ${data.timestamp}`, 'success');
            } catch (error) {
                console.error('API健康检查失败:', error);
                showResult('apiResult', `❌ API健康检查失败: ${error.message}`, 'error');
            }
        }
        
        // 测试XML分析API
        async function testAPIAnalyze() {
            console.log('开始测试XML分析API');
            try {
                showResult('apiResult', '🔄 正在测试XML分析...', 'debug');
                
                const testXML = '<?xml version="1.0"?><test><measurement speed="50.0" time="1.0"/></test>';
                
                const response = await fetch('/api/analyze-text', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        xmlContent: testXML,
                        fileName: 'debug-test.xml'
                    })
                });
                
                console.log('分析API响应状态:', response.status);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }
                
                const result = await response.json();
                console.log('分析API响应数据:', result);
                
                if (result.success) {
                    const summary = result.data.summary;
                    showResult('apiResult', `✅ XML分析成功!\n数据点: ${summary.totalDataPoints}\n状态: ${summary.overallStatus}\n处理时间: ${result.data.metadata.processingTime}ms`, 'success');
                } else {
                    showResult('apiResult', `❌ XML分析失败: ${result.error}`, 'error');
                }
            } catch (error) {
                console.error('XML分析测试失败:', error);
                showResult('apiResult', `❌ XML分析测试失败: ${error.message}`, 'error');
            }
        }
        
        // 测试Fetch API
        async function testFetch() {
            console.log('测试Fetch API功能');
            try {
                showResult('networkResult', '🔄 测试Fetch API...', 'debug');
                
                // 测试基本的fetch功能
                const response = await fetch(window.location.href);
                console.log('Fetch测试响应:', response.status);
                
                if (response.ok) {
                    showResult('networkResult', `✅ Fetch API正常工作!\n状态码: ${response.status}\nContent-Type: ${response.headers.get('content-type')}`, 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                console.error('Fetch测试失败:', error);
                showResult('networkResult', `❌ Fetch API测试失败: ${error.message}`, 'error');
            }
        }
        
        // 测试CORS
        async function testCORS() {
            console.log('测试CORS设置');
            try {
                showResult('networkResult', '🔄 测试CORS设置...', 'debug');
                
                const response = await fetch('/api/health', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                console.log('CORS测试响应:', response.status);
                console.log('CORS响应头:', [...response.headers.entries()]);
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('networkResult', `✅ CORS设置正常!\n可以正常访问API\n响应: ${JSON.stringify(data)}`, 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                console.error('CORS测试失败:', error);
                showResult('networkResult', `❌ CORS测试失败: ${error.message}\n可能的原因: 跨域限制或服务器配置问题`, 'error');
            }
        }
        
        // 页面加载完成后的初始化
        window.onload = function() {
            console.log('调试页面加载完成');
            showResult('basicResult', '页面加载完成，可以开始测试', 'debug');
        };
        
        // 全局错误处理
        window.onerror = function(message, source, lineno, colno, error) {
            console.error('全局JavaScript错误:', message, 'at', source, lineno, colno);
            showResult('basicResult', `❌ 全局JavaScript错误: ${message}\n位置: ${source}:${lineno}:${colno}`, 'error');
        };
        
        // Promise错误处理
        window.addEventListener('unhandledrejection', function(event) {
            console.error('未处理的Promise错误:', event.reason);
            showResult('networkResult', `❌ 未处理的Promise错误: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>

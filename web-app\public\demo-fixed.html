<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XML变速数据检测工具 - 演示（修复版）</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        textarea {
            width: 100%;
            height: 150px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            font-size: 13px;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
            border: 1px solid #ddd;
            min-height: 50px;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.online {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.offline {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 XML变速数据检测工具演示（修复版）</h1>
        
        <div class="status" id="connectionStatus">
            🔄 正在检查连接状态...
        </div>
        
        <div class="section">
            <h3>📊 API状态检查</h3>
            <button onclick="checkHealth()" id="healthBtn">检查API健康状态</button>
            <button onclick="testConnection()" id="connectionBtn">测试网络连接</button>
            <div id="healthResult" class="result" style="display:none;"></div>
        </div>

        <div class="section">
            <h3>📝 XML文本分析</h3>
            <textarea id="xmlInput" placeholder="输入XML内容..."></textarea>
            <br>
            <button onclick="loadExample()" id="exampleBtn">加载示例XML</button>
            <button onclick="analyzeXML()" id="analyzeBtn">分析XML</button>
            <button onclick="clearInput()" id="clearBtn">清空</button>
            <div id="analysisResult" class="result" style="display:none;"></div>
        </div>

        <div class="section">
            <h3>📁 示例文件</h3>
            <button onclick="getExamples()" id="examplesBtn">获取示例文件列表</button>
            <div id="examplesResult" class="result" style="display:none;"></div>
        </div>
        
        <div class="section">
            <h3>🔧 调试信息</h3>
            <div id="debugInfo" class="result">
                页面加载时间: <span id="loadTime"></span><br>
                当前URL: <span id="currentURL"></span><br>
                API基础URL: <span id="apiBase"></span><br>
                浏览器: <span id="browser"></span>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        const API_BASE = '/api';
        let isAPIOnline = false;
        
        // 显示调试信息
        document.getElementById('loadTime').textContent = new Date().toLocaleString();
        document.getElementById('currentURL').textContent = window.location.href;
        document.getElementById('apiBase').textContent = API_BASE;
        document.getElementById('browser').textContent = navigator.userAgent.split(' ').pop();
        
        // 通用的结果显示函数
        function showResult(elementId, content, type = 'result') {
            console.log(`[${new Date().toLocaleTimeString()}] 显示结果: ${elementId}, 类型: ${type}`);
            console.log('内容:', content);
            
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = content;
                element.className = `result ${type}`;
                element.style.display = 'block';
            } else {
                console.error(`找不到元素: ${elementId}`);
                alert(`错误: 找不到页面元素 ${elementId}`);
            }
        }
        
        // 设置按钮状态
        function setButtonState(buttonId, disabled, text) {
            const button = document.getElementById(buttonId);
            if (button) {
                button.disabled = disabled;
                if (text) button.textContent = text;
            }
        }
        
        // 更新连接状态
        function updateConnectionStatus(online, message) {
            const statusElement = document.getElementById('connectionStatus');
            isAPIOnline = online;
            
            if (online) {
                statusElement.className = 'status online';
                statusElement.textContent = `✅ ${message || 'API连接正常'}`;
            } else {
                statusElement.className = 'status offline';
                statusElement.textContent = `❌ ${message || 'API连接失败'}`;
            }
        }
        
        // 测试网络连接
        async function testConnection() {
            console.log('[测试] 开始网络连接测试');
            setButtonState('connectionBtn', true, '测试中...');
            
            try {
                // 测试基本的页面访问
                const response = await fetch(window.location.href);
                if (response.ok) {
                    showResult('healthResult', '✅ 网络连接正常\n可以访问当前页面', 'success');
                    updateConnectionStatus(true, '网络连接正常');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                console.error('[测试] 网络连接失败:', error);
                showResult('healthResult', `❌ 网络连接失败: ${error.message}`, 'error');
                updateConnectionStatus(false, '网络连接失败');
            } finally {
                setButtonState('connectionBtn', false, '测试网络连接');
            }
        }

        // 检查API健康状态
        async function checkHealth() {
            console.log('[API] 开始健康检查');
            setButtonState('healthBtn', true, '检查中...');
            showResult('healthResult', '🔄 正在检查API状态...', 'loading');
            
            try {
                const url = `${API_BASE}/health`;
                console.log('[API] 请求URL:', url);
                
                const response = await fetch(url);
                console.log('[API] 响应状态:', response.status);
                console.log('[API] 响应头:', [...response.headers.entries()]);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('[API] 响应数据:', data);
                
                const resultText = `✅ API健康检查成功!\n状态: ${data.status}\n版本: ${data.version}\n时间: ${data.timestamp}`;
                showResult('healthResult', resultText, 'success');
                updateConnectionStatus(true, 'API服务正常');
                
            } catch (error) {
                console.error('[API] 健康检查失败:', error);
                const errorText = `❌ API健康检查失败\n错误: ${error.message}\n\n可能的原因:\n1. 服务器未启动\n2. 端口被占用\n3. 网络连接问题`;
                showResult('healthResult', errorText, 'error');
                updateConnectionStatus(false, 'API服务异常');
            } finally {
                setButtonState('healthBtn', false, '检查API健康状态');
            }
        }

        // 加载示例XML
        function loadExample() {
            console.log('[示例] 加载示例XML');
            const exampleXML = `<?xml version="1.0" encoding="UTF-8"?>
<vehicle_test>
    <metadata>
        <test_id>DEMO_001</test_id>
        <date>2024-01-15</date>
    </metadata>
    
    <speed_data>
        <measurement speed="0.0" time="0.0"/>
        <measurement speed="15.5" time="1.0"/>
        <measurement speed="32.1" time="2.0"/>
        <measurement speed="48.7" time="3.0"/>
        <measurement speed="52.3" time="4.0"/>
        <measurement speed="45.8" time="5.0"/>
        <measurement speed="28.2" time="6.0"/>
        <measurement speed="12.1" time="7.0"/>
        <measurement speed="0.0" time="8.0"/>
    </speed_data>
</vehicle_test>`;
            
            document.getElementById('xmlInput').value = exampleXML;
            showResult('analysisResult', '✅ 示例XML已加载，点击"分析XML"开始分析', 'success');
        }

        // 分析XML
        async function analyzeXML() {
            console.log('[分析] 开始XML分析');
            const xmlContent = document.getElementById('xmlInput').value.trim();
            
            if (!xmlContent) {
                showResult('analysisResult', '❌ 请先输入XML内容或加载示例', 'error');
                return;
            }
            
            if (!isAPIOnline) {
                showResult('analysisResult', '❌ API服务不可用，请先检查API状态', 'error');
                return;
            }
            
            setButtonState('analyzeBtn', true, '分析中...');
            showResult('analysisResult', '🔄 正在分析XML数据...', 'loading');

            try {
                const url = `${API_BASE}/analyze-text`;
                console.log('[分析] 请求URL:', url);
                console.log('[分析] XML内容长度:', xmlContent.length);
                
                const requestBody = {
                    xmlContent: xmlContent,
                    fileName: 'demo.xml'
                };
                
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
                
                console.log('[分析] 响应状态:', response.status);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const result = await response.json();
                console.log('[分析] 响应数据:', result);

                if (result.success) {
                    const summary = result.data.summary;
                    const speedAnalysis = result.data.speedAnalysis;
                    const anomalies = result.data.anomalies;
                    
                    let output = `✅ 分析成功！\n\n`;
                    output += `📊 基本统计:\n`;
                    output += `- 数据点总数: ${summary.totalDataPoints}\n`;
                    output += `- 有效数据点: ${summary.validDataPoints}\n`;
                    output += `- 异常数量: ${summary.anomaliesCount}\n`;
                    output += `- 整体状态: ${summary.overallStatus}\n\n`;
                    
                    output += `📈 速度分析:\n`;
                    output += `- 最小速度: ${speedAnalysis.minSpeed.toFixed(2)}\n`;
                    output += `- 最大速度: ${speedAnalysis.maxSpeed.toFixed(2)}\n`;
                    output += `- 平均速度: ${speedAnalysis.averageSpeed.toFixed(2)}\n`;
                    output += `- 标准差: ${speedAnalysis.standardDeviation.toFixed(2)}\n\n`;
                    
                    if (anomalies.length > 0) {
                        output += `⚠️ 检测到异常:\n`;
                        anomalies.forEach((anomaly, index) => {
                            output += `${index + 1}. ${anomaly.description}\n`;
                        });
                    } else {
                        output += `✅ 未发现异常数据\n`;
                    }
                    
                    showResult('analysisResult', output, 'success');
                } else {
                    showResult('analysisResult', `❌ 分析失败: ${result.error}`, 'error');
                }
            } catch (error) {
                console.error('[分析] XML分析失败:', error);
                showResult('analysisResult', `❌ XML分析失败: ${error.message}`, 'error');
            } finally {
                setButtonState('analyzeBtn', false, '分析XML');
            }
        }

        // 获取示例文件
        async function getExamples() {
            console.log('[示例] 获取示例文件列表');
            
            if (!isAPIOnline) {
                showResult('examplesResult', '❌ API服务不可用，请先检查API状态', 'error');
                return;
            }
            
            setButtonState('examplesBtn', true, '获取中...');
            showResult('examplesResult', '🔄 正在获取示例文件列表...', 'loading');
            
            try {
                const url = `${API_BASE}/examples`;
                console.log('[示例] 请求URL:', url);
                
                const response = await fetch(url);
                console.log('[示例] 响应状态:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('[示例] 响应数据:', data);
                
                if (data.success) {
                    let output = '📁 可用示例文件:\n\n';
                    data.data.forEach((example, index) => {
                        output += `${index + 1}. ${example.name}\n`;
                        output += `   描述: ${example.description}\n`;
                        output += `   类型: ${example.category}\n\n`;
                    });
                    showResult('examplesResult', output, 'success');
                } else {
                    showResult('examplesResult', '❌ 获取示例文件失败', 'error');
                }
            } catch (error) {
                console.error('[示例] 获取示例文件失败:', error);
                showResult('examplesResult', `❌ 获取示例文件失败: ${error.message}`, 'error');
            } finally {
                setButtonState('examplesBtn', false, '获取示例文件列表');
            }
        }

        // 清空输入
        function clearInput() {
            console.log('[操作] 清空输入');
            document.getElementById('xmlInput').value = '';
            document.getElementById('analysisResult').style.display = 'none';
        }

        // 页面加载完成后的初始化
        window.onload = function() {
            console.log('[初始化] 页面加载完成');
            updateConnectionStatus(false, '正在检查连接...');
            
            // 自动检查API状态
            setTimeout(() => {
                checkHealth();
            }, 1000);
        };
        
        // 全局错误处理
        window.onerror = function(message, source, lineno, colno, error) {
            console.error('[错误] 全局JavaScript错误:', message, 'at', source, lineno, colno);
            alert(`JavaScript错误: ${message}\n位置: ${source}:${lineno}:${colno}`);
        };
        
        // Promise错误处理
        window.addEventListener('unhandledrejection', function(event) {
            console.error('[错误] 未处理的Promise错误:', event.reason);
            alert(`Promise错误: ${event.reason}`);
        });
    </script>
</body>
</html>

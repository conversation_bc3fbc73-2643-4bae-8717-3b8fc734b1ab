<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VFX XML数据分析工具 - 专业演示版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .status-bar {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
        }
        
        .status-online {
            background: #28a745;
            box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
        }
        
        .status-offline {
            background: #dc3545;
        }
        
        .status-checking {
            background: #ffc107;
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            margin: 5px;
        }
        
        .btn:hover:not(:disabled) {
            background: #5a6fd8;
            transform: translateY(-1px);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover:not(:disabled) {
            background: #218838;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover:not(:disabled) {
            background: #5a6268;
        }
        
        textarea {
            width: 100%;
            height: 200px;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            resize: vertical;
            transition: border-color 0.3s;
        }
        
        textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .result-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            min-height: 100px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 13px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .result-success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .result-error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        
        .result-loading {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        
        .back-link {
            display: inline-block;
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .back-link:hover {
            color: white;
        }
        
        .section-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 8px;
        }
        
        .help-text {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 15px;
            font-size: 14px;
        }

        .drop-zone {
            position: relative;
            border: 3px dashed #667eea;
            border-radius: 12px;
            padding: 40px 20px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
            margin-bottom: 15px;
        }

        .drop-zone:hover {
            border-color: #5a6fd8;
            background: #e9ecef;
        }

        .drop-zone.dragover {
            border-color: #007bff;
            background: #e3f2fd;
            transform: scale(1.02);
        }

        .drop-content {
            position: relative;
            z-index: 2;
        }

        .drop-icon {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }

        .drop-zone h4 {
            color: #333;
            margin-bottom: 10px;
            font-size: 20px;
        }

        .drop-zone p {
            color: #666;
            margin-bottom: 20px;
        }

        .drop-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 123, 255, 0.9);
            border-radius: 12px;
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }

        .drop-overlay.active {
            display: flex;
        }

        .drop-message {
            text-align: center;
            color: white;
        }

        .drop-icon-large {
            font-size: 72px;
            margin-bottom: 15px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .status-bar {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="/" class="back-link">← 返回主页</a>
        
        <div class="header">
            <h1>🎬 VFX XML数据分析工具演示</h1>
            <p>专业版本 - 支持Maya、Blender、Cinema 4D等VFX软件</p>
        </div>
        
        <div class="card">
            <div class="status-bar">
                <div>
                    <span class="status-indicator status-checking" id="statusIndicator"></span>
                    <span id="statusMessage">正在检查服务状态...</span>
                </div>
                <button onclick="checkServiceStatus()" class="btn btn-secondary" id="statusBtn">
                    🔄 检查状态
                </button>
            </div>
        </div>
        
        <div class="card">
            <div class="section-title">📊 API健康检查</div>
            <div class="help-text">
                <strong>💡 说明：</strong>检查后端API服务是否正常运行，这是所有功能的基础。
            </div>
            <button onclick="testAPIHealth()" class="btn btn-success" id="healthBtn">
                🩺 检查API健康状态
            </button>
            <div class="result-area" id="healthResult" style="display: none;"></div>
        </div>
        
        <div class="card">
            <div class="section-title">📁 文件拖拽上传</div>
            <div class="help-text">
                <strong>💡 说明：</strong>直接拖拽VFX XML文件到下方区域，或点击选择文件进行快速分析。
            </div>
            <div class="drop-zone" id="dropZone">
                <div class="drop-content">
                    <div class="drop-icon">🎬</div>
                    <h4>拖拽XML文件到这里</h4>
                    <p>支持Maya、Blender、Cinema 4D等VFX软件的XML文件</p>
                    <input type="file" id="fileInput" accept=".xml" style="display: none;">
                    <button class="btn" onclick="document.getElementById('fileInput').click()">
                        选择XML文件
                    </button>
                </div>
                <div class="drop-overlay" id="dropOverlay">
                    <div class="drop-message">
                        <div class="drop-icon-large">📁</div>
                        <h3>释放文件开始分析</h3>
                    </div>
                </div>
            </div>
            <div class="result-area" id="fileAnalysisResult" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="section-title">📝 XML文本分析</div>
            <div class="help-text">
                <strong>💡 说明：</strong>直接输入XML内容进行分析，或加载示例XML进行测试。
            </div>
            <textarea id="xmlInput" placeholder="在此输入XML内容，或点击下方按钮加载示例..."></textarea>
            <div style="margin-top: 15px;">
                <button onclick="loadExampleXML()" class="btn btn-secondary" id="exampleBtn">
                    📄 加载示例XML
                </button>
                <button onclick="analyzeXMLContent()" class="btn btn-success" id="analyzeBtn">
                    🔍 分析XML内容
                </button>
                <button onclick="clearXMLInput()" class="btn btn-secondary">
                    🗑️ 清空内容
                </button>
            </div>
            <div class="result-area" id="analysisResult" style="display: none;"></div>
        </div>
        
        <div class="card">
            <div class="section-title">📁 示例文件管理</div>
            <div class="help-text">
                <strong>💡 说明：</strong>获取服务器上的示例XML文件列表，了解可用的测试数据。
            </div>
            <button onclick="getExampleFiles()" class="btn btn-success" id="examplesBtn">
                📋 获取示例文件列表
            </button>
            <div class="result-area" id="examplesResult" style="display: none;"></div>
        </div>
        
        <div class="card">
            <div class="section-title">🔧 调试信息</div>
            <div class="result-area" id="debugInfo">
页面加载时间: <span id="loadTime"></span>
当前URL: <span id="currentURL"></span>
API基础地址: <span id="apiBase"></span>
浏览器: <span id="browserInfo"></span>
            </div>
        </div>
    </div>

    <script>
        // 全局配置
        const CONFIG = {
            API_BASE: '/api',
            TIMEOUT: 10000, // 10秒超时
            MAX_RETRIES: 3
        };
        
        // 全局状态
        let isAPIOnline = false;
        let requestCount = 0;
        let currentFileAnalysisData = null;
        
        // 初始化拖拽功能
        function initializeDragAndDrop() {
            const dropZone = document.getElementById('dropZone');
            const dropOverlay = document.getElementById('dropOverlay');
            const fileInput = document.getElementById('fileInput');

            if (!dropZone || !dropOverlay || !fileInput) {
                console.error('[拖拽] 必要的DOM元素不存在');
                return;
            }

            // 防止默认拖拽行为
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                dropZone.addEventListener(eventName, preventDefaults, false);
                document.body.addEventListener(eventName, preventDefaults, false);
            });

            // 拖拽进入
            ['dragenter', 'dragover'].forEach(eventName => {
                dropZone.addEventListener(eventName, handleDragEnter, false);
            });

            // 拖拽离开
            ['dragleave', 'drop'].forEach(eventName => {
                dropZone.addEventListener(eventName, handleDragLeave, false);
            });

            // 文件释放
            dropZone.addEventListener('drop', handleDrop, false);

            // 点击选择文件
            dropZone.addEventListener('click', () => fileInput.click());
            fileInput.addEventListener('change', handleFileSelect);

            console.log('[拖拽] 拖拽功能初始化完成');
        }

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        function handleDragEnter(e) {
            const dropZone = document.getElementById('dropZone');
            const dropOverlay = document.getElementById('dropOverlay');

            dropZone.classList.add('dragover');
            dropOverlay.classList.add('active');
        }

        function handleDragLeave(e) {
            const dropZone = document.getElementById('dropZone');
            const dropOverlay = document.getElementById('dropOverlay');

            dropZone.classList.remove('dragover');
            dropOverlay.classList.remove('active');
        }

        function handleDrop(e) {
            const files = e.dataTransfer.files;
            handleFiles(files);
        }

        function handleFileSelect(e) {
            const files = e.target.files;
            handleFiles(files);
        }

        function handleFiles(files) {
            if (files.length === 0) return;

            const file = files[0];

            // 验证文件类型
            if (!file.name.toLowerCase().endsWith('.xml')) {
                showResult('fileAnalysisResult', '❌ 请选择XML文件', 'error');
                return;
            }

            // 验证文件大小 (10MB)
            if (file.size > 10 * 1024 * 1024) {
                showResult('fileAnalysisResult', '❌ 文件大小不能超过10MB', 'error');
                return;
            }

            console.log('[拖拽] 开始分析文件:', file.name);
            analyzeUploadedFile(file);
        }

        async function analyzeUploadedFile(file) {
            showResult('fileAnalysisResult', `🔄 正在分析文件: ${file.name}...`, 'loading');

            try {
                // 读取文件内容
                const xmlContent = await readFileAsText(file);

                // 发送到API进行分析
                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        xmlContent: xmlContent,
                        fileName: file.name
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const analysisData = await response.json();
                currentFileAnalysisData = analysisData;

                // 显示分析结果
                displayFileAnalysisResult(analysisData);

            } catch (error) {
                console.error('[拖拽] 分析失败:', error);
                showResult('fileAnalysisResult', `❌ 分析失败: ${error.message}`, 'error');
            }
        }

        function readFileAsText(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = e => resolve(e.target.result);
                reader.onerror = e => reject(new Error('文件读取失败'));
                reader.readAsText(file);
            });
        }

        function displayFileAnalysisResult(data) {
            const statusText = getStatusText(data.summary.overallStatus);
            const statusIcon = getStatusIcon(data.summary.overallStatus);

            let result = `${statusIcon} VFX文件分析完成！

📄 文件信息:
- 文件名: ${data.metadata.fileName}
- 文件大小: ${(data.metadata.fileSize / 1024).toFixed(1)} KB
- 处理时间: ${data.metadata.processingTime}ms
- 文件类型: ${data.metadata.fileType || 'Unknown'}
- VFX软件: ${data.metadata.vfxSoftware || 'Unknown'}

📊 数据统计:
- 整体状态: ${statusText}
- 总数据点: ${data.summary.totalDataPoints}
- 有效数据: ${data.summary.validDataPoints}
- 异常数量: ${data.summary.anomaliesCount}
- 验证错误: ${data.summary.validationErrors}
- 验证警告: ${data.summary.validationWarnings}`;

            if (data.summary.dataCategories && Object.keys(data.summary.dataCategories).length > 0) {
                result += `\n\n🎬 数据类别分布:`;
                Object.entries(data.summary.dataCategories).forEach(([category, count]) => {
                    result += `\n- ${getCategoryName(category)}: ${count}个`;
                });
            }

            if (data.recommendations && data.recommendations.length > 0) {
                result += `\n\n💡 建议:`;
                data.recommendations.forEach(rec => {
                    result += `\n- ${rec.title}: ${rec.description}`;
                });
            }

            showResult('fileAnalysisResult', result, data.summary.overallStatus === 'pass' ? 'success' :
                      data.summary.overallStatus === 'error' ? 'error' : 'default');
        }

        function getStatusIcon(status) {
            const icons = {
                'pass': '✅',
                'warning': '⚠️',
                'error': '❌',
                'info': 'ℹ️'
            };
            return icons[status] || '📋';
        }

        function getStatusText(status) {
            const texts = {
                'pass': '通过',
                'warning': '警告',
                'error': '错误',
                'info': '信息'
            };
            return texts[status] || '未知';
        }

        function getCategoryName(category) {
            const names = {
                'animation': '动画数据',
                'transform': '变换数据',
                'material': '材质数据',
                'physics': '物理数据',
                'particle': '粒子数据',
                'camera': '摄像机数据',
                'lighting': '光照数据',
                'other': '其他数据'
            };
            return names[category] || category;
        }

        // 初始化调试信息
        function initializeDebugInfo() {
            document.getElementById('loadTime').textContent = new Date().toLocaleString();
            document.getElementById('currentURL').textContent = window.location.href;
            document.getElementById('apiBase').textContent = CONFIG.API_BASE;
            document.getElementById('browserInfo').textContent = navigator.userAgent.split(' ').slice(-2).join(' ');
        }
        
        // 通用的结果显示函数
        function showResult(elementId, content, type = 'default') {
            const element = document.getElementById(elementId);
            if (!element) {
                console.error(`元素不存在: ${elementId}`);
                return;
            }
            
            element.style.display = 'block';
            element.textContent = content;
            element.className = `result-area result-${type}`;
            
            console.log(`[结果显示] ${elementId}: ${type} - ${content.substring(0, 100)}...`);
        }
        
        // 设置按钮状态
        function setButtonState(buttonId, disabled, text) {
            const button = document.getElementById(buttonId);
            if (button) {
                button.disabled = disabled;
                if (text) {
                    button.textContent = text;
                }
            }
        }
        
        // 更新服务状态
        function updateServiceStatus(online, message) {
            const indicator = document.getElementById('statusIndicator');
            const messageEl = document.getElementById('statusMessage');
            
            isAPIOnline = online;
            
            if (indicator) {
                indicator.className = `status-indicator ${online ? 'status-online' : 'status-offline'}`;
            }
            
            if (messageEl) {
                messageEl.textContent = message;
            }
        }
        
        // 通用的API请求函数
        async function makeAPIRequest(endpoint, options = {}) {
            requestCount++;
            const requestId = requestCount;
            
            console.log(`[API请求 #${requestId}] 开始: ${endpoint}`);
            
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), CONFIG.TIMEOUT);
                
                const response = await fetch(`${CONFIG.API_BASE}${endpoint}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    signal: controller.signal,
                    ...options
                });
                
                clearTimeout(timeoutId);
                
                console.log(`[API请求 #${requestId}] 响应状态: ${response.status}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log(`[API请求 #${requestId}] 成功完成`);
                
                return data;
            } catch (error) {
                console.error(`[API请求 #${requestId}] 失败:`, error);
                throw error;
            }
        }
        
        // 检查服务状态
        async function checkServiceStatus() {
            console.log('[服务状态] 开始检查');
            setButtonState('statusBtn', true, '检查中...');
            
            const indicator = document.getElementById('statusIndicator');
            if (indicator) {
                indicator.className = 'status-indicator status-checking';
            }
            
            try {
                const data = await makeAPIRequest('/health');
                
                if (data && data.status === 'ok') {
                    updateServiceStatus(true, `服务正常运行 (v${data.version || '1.0.0'})`);
                    console.log('[服务状态] 检查成功');
                } else {
                    throw new Error('API返回异常状态');
                }
            } catch (error) {
                updateServiceStatus(false, `服务异常: ${error.message}`);
                console.error('[服务状态] 检查失败:', error);
            } finally {
                setButtonState('statusBtn', false, '🔄 检查状态');
            }
        }
        
        // 测试API健康状态
        async function testAPIHealth() {
            console.log('[API健康] 开始测试');
            setButtonState('healthBtn', true, '测试中...');
            showResult('healthResult', '🔄 正在检查API健康状态...', 'loading');
            
            try {
                const data = await makeAPIRequest('/health');
                
                const result = `✅ API健康检查成功！

📊 服务信息:
- 状态: ${data.status}
- 版本: ${data.version || '1.0.0'}
- 时间戳: ${data.timestamp}
- 响应时间: < 1秒

🔗 服务地址: ${CONFIG.API_BASE}
📡 连接状态: 正常`;
                
                showResult('healthResult', result, 'success');
                updateServiceStatus(true, '服务正常运行');
                
            } catch (error) {
                const result = `❌ API健康检查失败

🚨 错误信息: ${error.message}

🔧 可能的原因:
1. 后端服务器未启动
2. 网络连接问题
3. 端口被占用或防火墙阻止
4. 服务器配置错误

💡 解决建议:
- 检查服务器是否在端口3001上运行
- 确认网络连接正常
- 查看浏览器控制台的详细错误信息`;
                
                showResult('healthResult', result, 'error');
                updateServiceStatus(false, '服务异常');
            } finally {
                setButtonState('healthBtn', false, '🩺 检查API健康状态');
            }
        }
        
        // 加载示例XML
        function loadExampleXML() {
            console.log('[示例XML] 加载示例');
            
            const exampleXML = `<?xml version="1.0" encoding="UTF-8"?>
<vehicle_test>
    <metadata>
        <test_id>DEMO_001</test_id>
        <date>2024-01-15</date>
        <description>演示用车辆速度测试数据</description>
    </metadata>
    
    <speed_data>
        <measurement speed="0.0" time="0.0"/>
        <measurement speed="15.5" time="1.0"/>
        <measurement speed="32.1" time="2.0"/>
        <measurement speed="48.7" time="3.0"/>
        <measurement speed="52.3" time="4.0"/>
        <measurement speed="45.8" time="5.0"/>
        <measurement speed="28.2" time="6.0"/>
        <measurement speed="12.1" time="7.0"/>
        <measurement speed="0.0" time="8.0"/>
        <measurement speed="25.6" time="9.0"/>
        <measurement speed="41.2" time="10.0"/>
    </speed_data>
</vehicle_test>`;
            
            document.getElementById('xmlInput').value = exampleXML;
            showResult('analysisResult', '✅ 示例XML已加载完成\n\n点击"分析XML内容"按钮开始分析', 'success');
        }
        
        // 分析XML内容
        async function analyzeXMLContent() {
            console.log('[XML分析] 开始分析');
            
            const xmlContent = document.getElementById('xmlInput').value.trim();
            
            if (!xmlContent) {
                showResult('analysisResult', '❌ 请先输入XML内容或加载示例', 'error');
                return;
            }
            
            if (!isAPIOnline) {
                showResult('analysisResult', '❌ API服务不可用，请先检查服务状态', 'error');
                return;
            }
            
            setButtonState('analyzeBtn', true, '分析中...');
            showResult('analysisResult', '🔄 正在分析XML内容，请稍候...', 'loading');
            
            try {
                const data = await makeAPIRequest('/analyze-text', {
                    method: 'POST',
                    body: JSON.stringify({
                        xmlContent: xmlContent,
                        fileName: 'demo.xml'
                    })
                });
                
                if (data.success) {
                    const { summary, speedAnalysis, anomalies, metadata } = data.data;
                    
                    const result = `✅ XML分析成功完成！

📁 文件信息:
- 文件名: ${data.fileName}
- 文件大小: ${(data.fileSize / 1024).toFixed(2)} KB
- 处理时间: ${metadata.processingTime} ms

📊 数据摘要:
- 数据点总数: ${summary.totalDataPoints}
- 有效数据点: ${summary.validDataPoints}
- 异常数量: ${summary.anomaliesCount}
- 整体状态: ${summary.overallStatus.toUpperCase()}

📈 速度分析:
- 最小速度: ${speedAnalysis.minSpeed.toFixed(2)}
- 最大速度: ${speedAnalysis.maxSpeed.toFixed(2)}
- 平均速度: ${speedAnalysis.averageSpeed.toFixed(2)}
- 标准差: ${speedAnalysis.standardDeviation.toFixed(2)}

${anomalies.length > 0 ? 
`⚠️ 检测到 ${anomalies.length} 个异常:
${anomalies.map((a, i) => `${i + 1}. ${a.description}`).join('\n')}` : 
'✅ 未发现数据异常'}`;
                    
                    showResult('analysisResult', result, 'success');
                } else {
                    showResult('analysisResult', `❌ 分析失败: ${data.error}`, 'error');
                }
                
            } catch (error) {
                showResult('analysisResult', `❌ 分析过程中发生错误: ${error.message}`, 'error');
            } finally {
                setButtonState('analyzeBtn', false, '🔍 分析XML内容');
            }
        }
        
        // 获取示例文件
        async function getExampleFiles() {
            console.log('[示例文件] 获取列表');
            
            if (!isAPIOnline) {
                showResult('examplesResult', '❌ API服务不可用，请先检查服务状态', 'error');
                return;
            }
            
            setButtonState('examplesBtn', true, '获取中...');
            showResult('examplesResult', '🔄 正在获取示例文件列表...', 'loading');
            
            try {
                const data = await makeAPIRequest('/examples');
                
                if (data.success && data.data) {
                    const result = `✅ 成功获取示例文件列表

📁 可用示例文件 (${data.data.length} 个):

${data.data.map((example, index) => 
`${index + 1}. ${example.name}
   📝 描述: ${example.description}
   🏷️ 类型: ${example.category === 'normal' ? '正常数据' : '异常数据'}
`).join('\n')}

💡 使用说明:
- 这些是预置的测试文件
- 可以通过API获取具体内容
- 适合用于功能测试和演示`;
                    
                    showResult('examplesResult', result, 'success');
                } else {
                    showResult('examplesResult', '❌ 获取示例文件列表失败', 'error');
                }
                
            } catch (error) {
                showResult('examplesResult', `❌ 获取示例文件时发生错误: ${error.message}`, 'error');
            } finally {
                setButtonState('examplesBtn', false, '📋 获取示例文件列表');
            }
        }
        
        // 清空XML输入
        function clearXMLInput() {
            document.getElementById('xmlInput').value = '';
            document.getElementById('analysisResult').style.display = 'none';
            console.log('[操作] 清空XML输入');
        }
        
        // 页面初始化
        function initializePage() {
            console.log('[页面] 开始初始化');

            // 初始化调试信息
            initializeDebugInfo();

            // 初始化拖拽功能
            initializeDragAndDrop();

            // 检查服务状态
            checkServiceStatus();

            console.log('[页面] 初始化完成');
        }
        
        // 确保DOM完全加载后再执行
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializePage);
        } else {
            initializePage();
        }
        
        // 全局错误处理
        window.addEventListener('error', function(event) {
            console.error('[全局错误]', event.error);
            alert(`页面错误: ${event.error.message}\n\n请刷新页面重试`);
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            console.error('[Promise错误]', event.reason);
        });
        
        console.log('[脚本] 演示页面脚本加载完成');
    </script>
</body>
</html>

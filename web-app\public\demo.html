<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XML变速数据检测工具 - 演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        textarea {
            width: 100%;
            height: 150px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 XML变速数据检测工具演示</h1>
        
        <div class="section">
            <h3>📊 API状态检查</h3>
            <button onclick="checkHealth()">检查API健康状态</button>
            <div id="healthResult" class="result" style="display:none;"></div>
        </div>

        <div class="section">
            <h3>📝 XML文本分析</h3>
            <textarea id="xmlInput" placeholder="输入XML内容..."></textarea>
            <br>
            <button onclick="loadExample()">加载示例XML</button>
            <button onclick="analyzeXML()">分析XML</button>
            <button onclick="clearInput()">清空</button>
            <div id="analysisResult" class="result" style="display:none;"></div>
        </div>

        <div class="section">
            <h3>📁 示例文件</h3>
            <button onclick="getExamples()">获取示例文件列表</button>
            <div id="examplesResult" class="result" style="display:none;"></div>
        </div>
    </div>

    <script>
        // API基础URL
        const API_BASE = '/api';

        // 检查API健康状态
        async function checkHealth() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                showResult('healthResult', JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                showResult('healthResult', `错误: ${error.message}`, 'error');
            }
        }

        // 加载示例XML
        function loadExample() {
            const exampleXML = `<?xml version="1.0" encoding="UTF-8"?>
<vehicle_test>
    <metadata>
        <test_id>DEMO_001</test_id>
        <date>2024-01-15</date>
    </metadata>
    
    <speed_data>
        <measurement speed="0.0" time="0.0"/>
        <measurement speed="15.5" time="1.0"/>
        <measurement speed="32.1" time="2.0"/>
        <measurement speed="48.7" time="3.0"/>
        <measurement speed="52.3" time="4.0"/>
        <measurement speed="45.8" time="5.0"/>
        <measurement speed="28.2" time="6.0"/>
        <measurement speed="12.1" time="7.0"/>
        <measurement speed="0.0" time="8.0"/>
    </speed_data>
</vehicle_test>`;
            
            document.getElementById('xmlInput').value = exampleXML;
        }

        // 分析XML
        async function analyzeXML() {
            const xmlContent = document.getElementById('xmlInput').value.trim();
            if (!xmlContent) {
                showResult('analysisResult', '请输入XML内容', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/analyze-text`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        xmlContent: xmlContent,
                        fileName: 'demo.xml'
                    })
                });

                const result = await response.json();
                if (result.success) {
                    const summary = result.data.summary;
                    const speedAnalysis = result.data.speedAnalysis;
                    const anomalies = result.data.anomalies;
                    
                    let output = `✅ 分析成功！\n\n`;
                    output += `📊 基本统计:\n`;
                    output += `- 数据点总数: ${summary.totalDataPoints}\n`;
                    output += `- 有效数据点: ${summary.validDataPoints}\n`;
                    output += `- 异常数量: ${summary.anomaliesCount}\n`;
                    output += `- 整体状态: ${summary.overallStatus}\n\n`;
                    
                    output += `📈 速度分析:\n`;
                    output += `- 最小速度: ${speedAnalysis.minSpeed.toFixed(2)}\n`;
                    output += `- 最大速度: ${speedAnalysis.maxSpeed.toFixed(2)}\n`;
                    output += `- 平均速度: ${speedAnalysis.averageSpeed.toFixed(2)}\n`;
                    output += `- 标准差: ${speedAnalysis.standardDeviation.toFixed(2)}\n\n`;
                    
                    if (anomalies.length > 0) {
                        output += `⚠️ 检测到异常:\n`;
                        anomalies.forEach((anomaly, index) => {
                            output += `${index + 1}. ${anomaly.description}\n`;
                        });
                    } else {
                        output += `✅ 未发现异常数据\n`;
                    }
                    
                    showResult('analysisResult', output, 'success');
                } else {
                    showResult('analysisResult', `分析失败: ${result.error}`, 'error');
                }
            } catch (error) {
                showResult('analysisResult', `网络错误: ${error.message}`, 'error');
            }
        }

        // 获取示例文件
        async function getExamples() {
            try {
                const response = await fetch(`${API_BASE}/examples`);
                const data = await response.json();
                if (data.success) {
                    let output = '📁 可用示例文件:\n\n';
                    data.data.forEach((example, index) => {
                        output += `${index + 1}. ${example.name}\n`;
                        output += `   描述: ${example.description}\n`;
                        output += `   类型: ${example.category}\n\n`;
                    });
                    showResult('examplesResult', output, 'success');
                } else {
                    showResult('examplesResult', '获取示例文件失败', 'error');
                }
            } catch (error) {
                showResult('examplesResult', `错误: ${error.message}`, 'error');
            }
        }

        // 清空输入
        function clearInput() {
            document.getElementById('xmlInput').value = '';
            document.getElementById('analysisResult').style.display = 'none';
        }

        // 显示结果
        function showResult(elementId, content, type) {
            const element = document.getElementById(elementId);
            element.textContent = content;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }

        // 页面加载完成后自动检查API状态
        window.onload = function() {
            checkHealth();
        };
    </script>
</body>
</html>

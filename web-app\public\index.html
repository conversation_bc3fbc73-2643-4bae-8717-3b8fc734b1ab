<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VFX XML数据分析工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 50px;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            text-align: center;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }
        
        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #333;
        }
        
        .feature-card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .btn {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: background 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 1rem;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }

        .upload-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            color: white;
        }

        .upload-section h3 {
            color: white;
            margin-bottom: 20px;
            text-align: center;
        }

        .drop-zone {
            position: relative;
            border: 3px dashed rgba(255, 255, 255, 0.5);
            border-radius: 12px;
            padding: 40px 20px;
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .drop-zone:hover {
            border-color: rgba(255, 255, 255, 0.8);
            background: rgba(255, 255, 255, 0.15);
        }

        .drop-zone.dragover {
            border-color: #fff;
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.02);
        }

        .drop-content {
            position: relative;
            z-index: 2;
        }

        .drop-icon {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }

        .drop-zone h4 {
            color: white;
            margin-bottom: 10px;
            font-size: 24px;
        }

        .drop-zone p {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 20px;
        }

        .upload-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.5);
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .upload-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.8);
        }

        .drop-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 123, 255, 0.9);
            border-radius: 12px;
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }

        .drop-overlay.active {
            display: flex;
        }

        .drop-message {
            text-align: center;
            color: white;
        }

        .drop-icon-large {
            font-size: 72px;
            margin-bottom: 15px;
        }

        .analysis-result {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
        }

        .analysis-result h4 {
            color: white;
            margin-bottom: 15px;
        }

        .result-actions {
            margin-top: 15px;
            text-align: center;
        }

        .result-actions .btn {
            margin: 0 5px;
        }

        .status-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online {
            background: #28a745;
            box-shadow: 0 0 10px #28a745;
        }
        
        .status-offline {
            background: #dc3545;
        }
        
        .footer {
            text-align: center;
            margin-top: 50px;
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .container {
                padding: 20px 15px;
            }
            
            .features {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 VFX XML数据分析工具</h1>
            <p>专业的视效XML文件数据提取与分析平台。支持Maya、Blender、Cinema 4D等主流软件的XML格式，智能识别动画、变换、材质、粒子等VFX相关数据。</p>
        </div>

        <div class="upload-section">
            <h3>📁 快速VFX文件分析</h3>
            <div class="drop-zone" id="dropZone">
                <div class="drop-content">
                    <div class="drop-icon">🎬</div>
                    <h4>拖拽XML文件到这里</h4>
                    <p>支持Maya、Blender、Cinema 4D等VFX软件的XML文件</p>
                    <input type="file" id="fileInput" accept=".xml" style="display: none;">
                    <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                        选择XML文件
                    </button>
                </div>
                <div class="drop-overlay" id="dropOverlay">
                    <div class="drop-message">
                        <div class="drop-icon-large">📁</div>
                        <h3>释放文件开始分析</h3>
                    </div>
                </div>
            </div>
            <div class="analysis-result" id="analysisResult" style="display: none;">
                <h4>📊 分析结果</h4>
                <div id="resultContent"></div>
                <div class="result-actions">
                    <button onclick="viewDetailedReport()" class="btn">查看详细报告</button>
                    <button onclick="downloadReport()" class="btn btn-secondary">下载报告</button>
                </div>
            </div>
        </div>

        <div class="status-section">
            <h3>🌐 服务状态</h3>
            <p id="statusText">
                <span class="status-indicator" id="statusIndicator"></span>
                <span id="statusMessage">正在检查服务状态...</span>
            </p>
            <button onclick="refreshStatus()" class="btn btn-secondary" style="margin-top: 10px;">
                🔄 刷新状态
            </button>
        </div>
        
        <div class="features">
            <div class="feature-card">
                <span class="feature-icon">🎯</span>
                <h3>VFX数据分析</h3>
                <p>体验完整的VFX XML数据分析功能，支持动画、变换、材质、粒子等数据的智能识别和分析。</p>
                <a href="/demo-stable.html" class="btn">开始演示</a>
            </div>

            <div class="feature-card">
                <span class="feature-icon">🌐</span>
                <h3>UNC路径读取</h3>
                <p>直接读取网络共享文件夹中的XML文件，支持UNC路径浏览和文件选择，无需本地上传。</p>
                <a href="/unc-reader.html" class="btn">网络文件读取</a>
            </div>

            <div class="feature-card">
                <span class="feature-icon">🎨</span>
                <h3>完整界面</h3>
                <p>使用完整的Web界面，支持文件拖拽上传、实时分析结果展示和数据可视化图表。</p>
                <a href="/demo-stable.html" class="btn">打开界面</a>
            </div>

            <div class="feature-card">
                <span class="feature-icon">🔍</span>
                <h3>XML诊断</h3>
                <p>深度分析XML文件结构，识别VFX软件类型，查看所有属性和元素，帮助理解数据组织方式。</p>
                <a href="/xml-diagnostic.html" class="btn">诊断工具</a>
            </div>

            <div class="feature-card">
                <span class="feature-icon">📚</span>
                <h3>API文档</h3>
                <p>查看详细的API接口文档，了解如何集成XML检测功能到您的应用中。</p>
                <a href="/api-docs.html" class="btn btn-secondary">查看文档</a>
            </div>
        </div>
        
        <div class="features">
            <div class="feature-card">
                <span class="feature-icon">📊</span>
                <h3>核心功能</h3>
                <p>
                    • 自动检测speed、velocity、rate属性<br>
                    • 异常数据识别和分析<br>
                    • 统计分析和数据验证<br>
                    • 时间序列数据处理
                </p>
            </div>
            
            <div class="feature-card">
                <span class="feature-icon">🛡️</span>
                <h3>安全特性</h3>
                <p>
                    • 文件大小限制（10MB）<br>
                    • 请求频率限制<br>
                    • 输入数据验证<br>
                    • 安全的错误处理
                </p>
            </div>
            
            <div class="feature-card">
                <span class="feature-icon">⚡</span>
                <h3>性能优势</h3>
                <p>
                    • 高效的XML解析引擎<br>
                    • 实时数据处理<br>
                    • 响应式Web设计<br>
                    • 跨平台兼容性
                </p>
            </div>
        </div>
        
        <div class="footer">
            <p>VFX XML数据分析工具 ©2024 - 专业视效数据处理平台</p>
            <p>支持Windows、macOS、Linux平台</p>
        </div>
    </div>
    
    <script>
        // 全局变量
        let statusCheckAttempts = 0;
        const maxStatusCheckAttempts = 3;
        let currentAnalysisData = null;

        // 拖拽上传功能
        function initializeDragAndDrop() {
            const dropZone = document.getElementById('dropZone');
            const dropOverlay = document.getElementById('dropOverlay');
            const fileInput = document.getElementById('fileInput');

            if (!dropZone || !dropOverlay || !fileInput) {
                console.error('[拖拽] 必要的DOM元素不存在');
                return;
            }

            // 防止默认拖拽行为
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                dropZone.addEventListener(eventName, preventDefaults, false);
                document.body.addEventListener(eventName, preventDefaults, false);
            });

            // 拖拽进入
            ['dragenter', 'dragover'].forEach(eventName => {
                dropZone.addEventListener(eventName, handleDragEnter, false);
            });

            // 拖拽离开
            ['dragleave', 'drop'].forEach(eventName => {
                dropZone.addEventListener(eventName, handleDragLeave, false);
            });

            // 文件释放
            dropZone.addEventListener('drop', handleDrop, false);

            // 点击选择文件
            dropZone.addEventListener('click', () => fileInput.click());
            fileInput.addEventListener('change', handleFileSelect);

            console.log('[拖拽] 拖拽功能初始化完成');
        }

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        function handleDragEnter(e) {
            const dropZone = document.getElementById('dropZone');
            const dropOverlay = document.getElementById('dropOverlay');

            dropZone.classList.add('dragover');
            dropOverlay.classList.add('active');
        }

        function handleDragLeave(e) {
            const dropZone = document.getElementById('dropZone');
            const dropOverlay = document.getElementById('dropOverlay');

            dropZone.classList.remove('dragover');
            dropOverlay.classList.remove('active');
        }

        function handleDrop(e) {
            const files = e.dataTransfer.files;
            handleFiles(files);
        }

        function handleFileSelect(e) {
            const files = e.target.files;
            handleFiles(files);
        }

        function handleFiles(files) {
            if (files.length === 0) return;

            const file = files[0];

            // 验证文件类型
            if (!file.name.toLowerCase().endsWith('.xml')) {
                showError('请选择XML文件');
                return;
            }

            // 验证文件大小 (10MB)
            if (file.size > 10 * 1024 * 1024) {
                showError('文件大小不能超过10MB');
                return;
            }

            console.log('[拖拽] 开始分析文件:', file.name);
            analyzeFile(file);
        }

        async function analyzeFile(file) {
            const resultSection = document.getElementById('analysisResult');
            const resultContent = document.getElementById('resultContent');

            try {
                // 显示加载状态
                resultSection.style.display = 'block';
                resultContent.innerHTML = `
                    <div style="text-align: center; padding: 20px;">
                        <div style="font-size: 24px; margin-bottom: 10px;">🔄</div>
                        <p>正在分析 ${file.name}...</p>
                    </div>
                `;

                // 读取文件内容
                const xmlContent = await readFileAsText(file);

                // 发送到API进行分析
                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        xmlContent: xmlContent,
                        fileName: file.name
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const responseData = await response.json();

                // 检查响应格式
                if (responseData.success && responseData.data) {
                    currentAnalysisData = responseData.data;
                    // 显示分析结果
                    displayAnalysisResult(responseData.data);
                } else {
                    throw new Error(responseData.error || '未知错误');
                }

            } catch (error) {
                console.error('[拖拽] 分析失败:', error);

                // 尝试解析更详细的错误信息
                let errorMessage = error.message;
                if (error.message.includes('HTTP 400')) {
                    errorMessage = '请求格式错误，请检查XML文件格式';
                } else if (error.message.includes('HTTP 500')) {
                    errorMessage = '服务器内部错误，请稍后重试';
                }

                showError('分析失败: ' + errorMessage);
            }
        }

        function readFileAsText(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = e => resolve(e.target.result);
                reader.onerror = e => reject(new Error('文件读取失败'));
                reader.readAsText(file);
            });
        }

        function displayAnalysisResult(data) {
            const resultContent = document.getElementById('resultContent');

            const statusColor = getStatusColor(data.summary.overallStatus);
            const statusText = getStatusText(data.summary.overallStatus);

            resultContent.innerHTML = `
                <div style="background: rgba(255,255,255,0.1); border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <span style="color: ${statusColor}; font-size: 20px; margin-right: 10px;">●</span>
                        <strong>整体状态: ${statusText}</strong>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; font-size: 14px;">
                        <div>📄 文件: ${data.metadata.fileName}</div>
                        <div>📊 数据点: ${data.summary.totalDataPoints}</div>
                        <div>✅ 有效数据: ${data.summary.validDataPoints}</div>
                        <div>⚠️ 异常: ${data.summary.anomaliesCount}</div>
                        <div>🎬 文件类型: ${data.metadata.fileType || 'Unknown'}</div>
                        <div>🛠️ 软件: ${data.metadata.vfxSoftware || 'Unknown'}</div>
                    </div>
                </div>

                ${data.summary.dataCategories ? `
                <div style="background: rgba(255,255,255,0.1); border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                    <strong>数据类别分布:</strong>
                    <div style="margin-top: 10px; display: flex; flex-wrap: wrap; gap: 10px;">
                        ${Object.entries(data.summary.dataCategories).map(([category, count]) =>
                            `<span style="background: rgba(255,255,255,0.2); padding: 5px 10px; border-radius: 15px; font-size: 12px;">
                                ${getCategoryIcon(category)} ${category}: ${count}
                            </span>`
                        ).join('')}
                    </div>
                </div>
                ` : ''}

                ${data.timewarpAnalysis && data.timewarpAnalysis.hasTimewarp ? `
                <div style="background: rgba(255,165,0,0.2); border: 1px solid rgba(255,165,0,0.5); border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                    <strong>🎬 变速效果检测:</strong>
                    <div style="margin-top: 10px; font-size: 14px;">
                        <div>类型: ${getTimewarpTypeText(data.timewarpAnalysis.timewarpType)}</div>
                        <div>速度范围: ${data.timewarpAnalysis.speedRange.min}x - ${data.timewarpAnalysis.speedRange.max}x</div>
                        <div>整体变化: ${data.timewarpAnalysis.overallSpeedChange.toFixed(1)}%</div>
                        ${data.timewarpAnalysis.segments.length > 0 ? `<div>变速段: ${data.timewarpAnalysis.segments.length}个</div>` : ''}
                    </div>
                </div>
                ` : ''}

                ${data.resizeAnalysis && data.resizeAnalysis.hasResize ? `
                <div style="background: rgba(0,191,255,0.2); border: 1px solid rgba(0,191,255,0.5); border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                    <strong>📏 缩放效果检测:</strong>
                    <div style="margin-top: 10px; font-size: 14px;">
                        <div>类型: ${getResizeTypeText(data.resizeAnalysis.resizeType)}</div>
                        <div>整体缩放: ${data.resizeAnalysis.overallScale.toFixed(2)}倍</div>
                        <div>变化: ${data.resizeAnalysis.isEnlarged ? '放大' : data.resizeAnalysis.isReduced ? '缩小' : '无变化'} ${Math.abs(data.resizeAnalysis.scaleChange).toFixed(1)}%</div>
                        ${data.resizeAnalysis.resizeType === 'non_uniform' ?
                            `<div>XYZ: ${data.resizeAnalysis.scaleFactors.x}, ${data.resizeAnalysis.scaleFactors.y}, ${data.resizeAnalysis.scaleFactors.z}</div>` : ''}
                    </div>
                </div>
                ` : ''}
            `;
        }

        function getStatusColor(status) {
            const colors = {
                'pass': '#28a745',
                'warning': '#ffc107',
                'error': '#dc3545',
                'info': '#17a2b8'
            };
            return colors[status] || '#6c757d';
        }

        function getStatusText(status) {
            const texts = {
                'pass': '通过',
                'warning': '警告',
                'error': '错误',
                'info': '信息'
            };
            return texts[status] || '未知';
        }

        function getCategoryIcon(category) {
            const icons = {
                'animation': '🎬',
                'transform': '📐',
                'material': '🎨',
                'physics': '⚡',
                'particle': '✨',
                'camera': '📷',
                'lighting': '💡',
                'other': '📋'
            };
            return icons[category] || '📋';
        }

        function getTimewarpTypeText(type) {
            const types = {
                'none': '无变速',
                'uniform': '整段变速',
                'segmented': '分段变速',
                'curve': '曲线变速'
            };
            return types[type] || type;
        }

        function getResizeTypeText(type) {
            const types = {
                'none': '无缩放',
                'uniform': '均匀缩放',
                'non_uniform': '非均匀缩放',
                'animated': '动画缩放'
            };
            return types[type] || type;
        }

        function showError(message) {
            const resultSection = document.getElementById('analysisResult');
            const resultContent = document.getElementById('resultContent');

            resultSection.style.display = 'block';
            resultContent.innerHTML = `
                <div style="background: rgba(220, 53, 69, 0.2); border: 1px solid rgba(220, 53, 69, 0.5); border-radius: 8px; padding: 15px; text-align: center;">
                    <div style="font-size: 24px; margin-bottom: 10px;">❌</div>
                    <p style="color: #fff; margin: 0;">${message}</p>
                </div>
            `;
        }

        function viewDetailedReport() {
            if (currentAnalysisData) {
                // 将数据存储到sessionStorage
                sessionStorage.setItem('analysisData', JSON.stringify(currentAnalysisData));
                // 跳转到详细报告页面
                window.open('/demo-stable.html', '_blank');
            }
        }

        function downloadReport() {
            if (currentAnalysisData) {
                const dataStr = JSON.stringify(currentAnalysisData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `vfx-analysis-${currentAnalysisData.metadata.fileName}-${new Date().toISOString().slice(0,10)}.json`;
                link.click();
                URL.revokeObjectURL(url);
            }
        }

        // 检查API状态 - 增强版，解决代理问题
        async function checkAPIStatus() {
            console.log('[主页] 开始检查API状态，尝试次数:', statusCheckAttempts + 1);

            const indicator = document.getElementById('statusIndicator');
            const message = document.getElementById('statusMessage');

            // 确保元素存在
            if (!indicator || !message) {
                console.error('[主页] 状态显示元素不存在');
                return;
            }

            // 显示检查中状态
            indicator.className = 'status-indicator';
            message.textContent = '正在检查服务状态...';

            // 尝试多种连接方式
            const connectionMethods = [
                { name: '相对路径', url: '/api/health' },
                { name: '127.0.0.1', url: 'http://127.0.0.1:3001/api/health' },
                { name: 'localhost', url: 'http://localhost:3001/api/health' }
            ];

            for (const method of connectionMethods) {
                try {
                    console.log(`[主页] 尝试${method.name}: ${method.url}`);

                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), 3000); // 3秒超时

                    const response = await fetch(method.url, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                            'Cache-Control': 'no-cache'
                        },
                        signal: controller.signal
                    });

                    clearTimeout(timeoutId);
                    console.log(`[主页] ${method.name}响应状态:`, response.status);

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const data = await response.json();
                    console.log(`[主页] ${method.name}响应数据:`, data);

                    if (data && data.status === 'ok') {
                        indicator.className = 'status-indicator status-online';
                        message.textContent = `服务正常运行 (v${data.version || '1.0.0'}) - 通过${method.name}`;
                        console.log(`[主页] API状态检查成功 - 使用${method.name}`);
                        statusCheckAttempts = 0; // 重置尝试次数
                        return; // 成功后退出
                    } else {
                        throw new Error('API返回异常状态: ' + (data ? data.status : 'unknown'));
                    }
                } catch (error) {
                    console.warn(`[主页] ${method.name}失败:`, error.message);
                    // 继续尝试下一种方法
                }
            }

            // 所有方法都失败了
            console.error('[主页] 所有连接方法都失败');
            statusCheckAttempts++;

            indicator.className = 'status-indicator status-offline';

            if (statusCheckAttempts >= maxStatusCheckAttempts) {
                message.innerHTML = `服务不可用 (已尝试${statusCheckAttempts}次)<br><small>可能是代理或网络问题，请查看故障排除指南</small>`;
            } else {
                message.textContent = `服务检查失败，正在重试... (${statusCheckAttempts}/${maxStatusCheckAttempts})`;
                // 3秒后重试
                setTimeout(checkAPIStatus, 3000);
            }
        }

        // 手动刷新状态
        function refreshStatus() {
            statusCheckAttempts = 0;
            checkAPIStatus();
        }

        // 页面加载完成后的处理
        function initializePage() {
            console.log('[主页] 页面初始化开始');

            // 检查必要的DOM元素
            const requiredElements = ['statusIndicator', 'statusMessage', 'dropZone', 'dropOverlay', 'fileInput'];
            const missingElements = requiredElements.filter(id => !document.getElementById(id));

            if (missingElements.length > 0) {
                console.error('[主页] 缺少必要的DOM元素:', missingElements);
                return;
            }

            // 初始化拖拽功能
            initializeDragAndDrop();

            // 开始状态检查
            checkAPIStatus();

            // 设置定期检查（每30秒）
            setInterval(() => {
                if (statusCheckAttempts < maxStatusCheckAttempts) {
                    checkAPIStatus();
                }
            }, 30000);

            console.log('[主页] 页面初始化完成');
        }

        // 确保DOM完全加载后再执行
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializePage);
        } else {
            initializePage();
        }

        // 备用的window.onload
        window.onload = function() {
            console.log('[主页] window.onload 触发');
            if (statusCheckAttempts === 0) {
                initializePage();
            }
        };

        // 全局错误处理
        window.addEventListener('error', function(event) {
            console.error('[主页] 全局错误:', event.error);
        });

        window.addEventListener('unhandledrejection', function(event) {
            console.error('[主页] 未处理的Promise错误:', event.reason);
        });
    </script>
</body>
</html>

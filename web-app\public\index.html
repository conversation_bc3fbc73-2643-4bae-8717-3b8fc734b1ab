<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XML变速数据检测工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 50px;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            text-align: center;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }
        
        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #333;
        }
        
        .feature-card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .btn {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: background 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 1rem;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .status-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online {
            background: #28a745;
            box-shadow: 0 0 10px #28a745;
        }
        
        .status-offline {
            background: #dc3545;
        }
        
        .footer {
            text-align: center;
            margin-top: 50px;
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .container {
                padding: 20px 15px;
            }
            
            .features {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 XML变速数据检测工具</h1>
            <p>智能分析XML文件中的速度数据，检测异常并生成详细报告。支持多种速度属性识别，提供完整的数据验证和可视化分析。</p>
        </div>
        
        <div class="status-section">
            <h3>🌐 服务状态</h3>
            <p id="statusText">
                <span class="status-indicator" id="statusIndicator"></span>
                <span id="statusMessage">正在检查服务状态...</span>
            </p>
            <button onclick="checkAPIStatus()" class="btn btn-secondary" style="margin-top: 10px;">
                🔄 刷新状态
            </button>
        </div>
        
        <div class="features">
            <div class="feature-card">
                <span class="feature-icon">🎯</span>
                <h3>功能演示</h3>
                <p>体验完整的XML分析功能，包括文本输入、示例加载和API测试。适合快速了解工具能力。</p>
                <a href="/demo-fixed.html" class="btn">开始演示</a>
            </div>
            
            <div class="feature-card">
                <span class="feature-icon">🎨</span>
                <h3>完整界面</h3>
                <p>使用完整的Web界面，支持文件拖拽上传、实时分析结果展示和数据可视化图表。</p>
                <a href="/test.html" class="btn">打开界面</a>
            </div>
            
            <div class="feature-card">
                <span class="feature-icon">📚</span>
                <h3>API文档</h3>
                <p>查看详细的API接口文档，了解如何集成XML检测功能到您的应用中。</p>
                <a href="/api-docs.html" class="btn btn-secondary">查看文档</a>
            </div>
        </div>
        
        <div class="features">
            <div class="feature-card">
                <span class="feature-icon">📊</span>
                <h3>核心功能</h3>
                <p>
                    • 自动检测speed、velocity、rate属性<br>
                    • 异常数据识别和分析<br>
                    • 统计分析和数据验证<br>
                    • 时间序列数据处理
                </p>
            </div>
            
            <div class="feature-card">
                <span class="feature-icon">🛡️</span>
                <h3>安全特性</h3>
                <p>
                    • 文件大小限制（10MB）<br>
                    • 请求频率限制<br>
                    • 输入数据验证<br>
                    • 安全的错误处理
                </p>
            </div>
            
            <div class="feature-card">
                <span class="feature-icon">⚡</span>
                <h3>性能优势</h3>
                <p>
                    • 高效的XML解析引擎<br>
                    • 实时数据处理<br>
                    • 响应式Web设计<br>
                    • 跨平台兼容性
                </p>
            </div>
        </div>
        
        <div class="footer">
            <p>XML变速数据检测工具 ©2024 - 基于现代Web技术构建</p>
            <p>支持Windows、macOS、Linux平台</p>
        </div>
    </div>
    
    <script>
        // 检查API状态
        async function checkAPIStatus() {
            console.log('[主页] 开始检查API状态');

            try {
                // 显示检查中状态
                const indicator = document.getElementById('statusIndicator');
                const message = document.getElementById('statusMessage');

                if (indicator) indicator.className = 'status-indicator';
                if (message) message.textContent = '正在检查服务状态...';

                console.log('[主页] 发送API请求到 /api/health');
                const response = await fetch('/api/health', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                console.log('[主页] API响应状态:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('[主页] API响应数据:', data);

                if (data.status === 'ok') {
                    if (indicator) indicator.className = 'status-indicator status-online';
                    if (message) message.textContent = `服务正常运行 (v${data.version})`;
                    console.log('[主页] API状态检查成功');
                } else {
                    throw new Error('API返回异常状态: ' + data.status);
                }
            } catch (error) {
                console.error('[主页] API状态检查失败:', error);
                const indicator = document.getElementById('statusIndicator');
                const message = document.getElementById('statusMessage');

                if (indicator) indicator.className = 'status-indicator status-offline';
                if (message) message.textContent = `服务异常: ${error.message}`;
            }
        }
        
        // 页面加载时检查状态
        window.onload = function() {
            checkAPIStatus();
            
            // 每30秒检查一次状态
            setInterval(checkAPIStatus, 30000);
        };
    </script>
</body>
</html>

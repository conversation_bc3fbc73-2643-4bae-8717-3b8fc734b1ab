<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript基础测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
            display: block;
            width: 200px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            min-height: 50px;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 JavaScript基础测试</h1>
        <p><strong>目的：</strong>检查JavaScript是否能正常执行</p>
        
        <h3>测试1: 内联事件</h3>
        <button onclick="alert('内联事件正常工作！')">测试内联onclick</button>
        
        <h3>测试2: 函数调用</h3>
        <button onclick="testFunction()">测试函数调用</button>
        <div id="functionResult" class="result">等待测试...</div>
        
        <h3>测试3: DOM操作</h3>
        <button onclick="testDOM()">测试DOM操作</button>
        <div id="domResult" class="result">等待测试...</div>
        
        <h3>测试4: 事件监听器</h3>
        <button id="eventButton">测试事件监听器</button>
        <div id="eventResult" class="result">等待测试...</div>
        
        <h3>测试5: 控制台输出</h3>
        <button onclick="testConsole()">测试控制台</button>
        <div id="consoleResult" class="result">等待测试...</div>
        
        <h3>调试信息</h3>
        <div id="debugInfo" class="result">
            <strong>页面加载时间:</strong> <span id="loadTime"></span><br>
            <strong>JavaScript状态:</strong> <span id="jsStatus">检查中...</span><br>
            <strong>浏览器:</strong> <span id="browserInfo"></span><br>
            <strong>错误信息:</strong> <span id="errorInfo">无</span>
        </div>
        
        <div style="margin-top: 20px;">
            <a href="/" style="color: #007bff; text-decoration: none;">← 返回主页</a>
        </div>
    </div>

    <script>
        // 立即执行的代码
        console.log('JavaScript文件开始执行');
        
        // 设置加载时间
        document.getElementById('loadTime').textContent = new Date().toLocaleString();
        document.getElementById('browserInfo').textContent = navigator.userAgent.split(' ').slice(-2).join(' ');
        
        // 测试函数
        function testFunction() {
            console.log('testFunction被调用');
            try {
                const result = document.getElementById('functionResult');
                result.textContent = '✅ 函数调用成功！时间: ' + new Date().toLocaleTimeString();
                result.className = 'result success';
                console.log('函数测试成功');
            } catch (error) {
                console.error('函数测试失败:', error);
                document.getElementById('functionResult').textContent = '❌ 函数调用失败: ' + error.message;
                document.getElementById('functionResult').className = 'result error';
            }
        }
        
        // 测试DOM操作
        function testDOM() {
            console.log('testDOM被调用');
            try {
                const result = document.getElementById('domResult');
                const testDiv = document.createElement('div');
                testDiv.textContent = '这是动态创建的元素';
                testDiv.style.background = '#e7f3ff';
                testDiv.style.padding = '10px';
                testDiv.style.marginTop = '10px';
                testDiv.style.borderRadius = '4px';
                
                result.innerHTML = '✅ DOM操作成功！';
                result.appendChild(testDiv);
                result.className = 'result success';
                console.log('DOM测试成功');
            } catch (error) {
                console.error('DOM测试失败:', error);
                document.getElementById('domResult').textContent = '❌ DOM操作失败: ' + error.message;
                document.getElementById('domResult').className = 'result error';
            }
        }
        
        // 测试控制台
        function testConsole() {
            console.log('testConsole被调用');
            console.log('这是一条测试日志');
            console.warn('这是一条测试警告');
            console.error('这是一条测试错误（仅用于测试）');
            
            try {
                const result = document.getElementById('consoleResult');
                result.textContent = '✅ 控制台测试完成！请按F12查看开发者工具的控制台标签页';
                result.className = 'result success';
            } catch (error) {
                console.error('控制台测试失败:', error);
            }
        }
        
        // 页面加载完成后的处理
        function initializePage() {
            console.log('页面初始化开始');
            
            try {
                // 设置JavaScript状态
                document.getElementById('jsStatus').textContent = '正常';
                document.getElementById('jsStatus').style.color = '#28a745';
                
                // 添加事件监听器
                const eventButton = document.getElementById('eventButton');
                if (eventButton) {
                    eventButton.addEventListener('click', function() {
                        console.log('事件监听器被触发');
                        const result = document.getElementById('eventResult');
                        result.textContent = '✅ 事件监听器正常工作！';
                        result.className = 'result success';
                    });
                    console.log('事件监听器添加成功');
                } else {
                    console.error('找不到事件按钮元素');
                }
                
                console.log('页面初始化完成');
            } catch (error) {
                console.error('页面初始化失败:', error);
                document.getElementById('jsStatus').textContent = '异常: ' + error.message;
                document.getElementById('jsStatus').style.color = '#dc3545';
            }
        }
        
        // 全局错误处理
        window.addEventListener('error', function(event) {
            console.error('全局JavaScript错误:', event);
            const errorInfo = document.getElementById('errorInfo');
            if (errorInfo) {
                errorInfo.textContent = `${event.error ? event.error.message : event.message} (${event.filename}:${event.lineno})`;
                errorInfo.style.color = '#dc3545';
            }
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            console.error('未处理的Promise错误:', event.reason);
            const errorInfo = document.getElementById('errorInfo');
            if (errorInfo) {
                errorInfo.textContent = 'Promise错误: ' + event.reason;
                errorInfo.style.color = '#dc3545';
            }
        });
        
        // 确保DOM加载完成后执行
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializePage);
        } else {
            initializePage();
        }
        
        // 备用初始化
        window.onload = function() {
            console.log('window.onload触发');
            if (document.getElementById('jsStatus').textContent === '检查中...') {
                initializePage();
            }
        };
        
        console.log('JavaScript文件执行完成');
    </script>
</body>
</html>

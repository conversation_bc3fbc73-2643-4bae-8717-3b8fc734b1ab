<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理和网络诊断</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .result {
            background: #e9ecef;
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .info-box {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 代理和网络诊断</h1>
        
        <div class="info-box">
            <h3>🎯 诊断目的</h3>
            <p><strong>服务状态</strong>是指后端API服务器的运行状态。如果一直显示"正在检查中"，通常是因为：</p>
            <ul>
                <li><strong>网络代理问题</strong>：HTTP代理拦截了API请求</li>
                <li><strong>CORS问题</strong>：跨域请求被浏览器阻止</li>
                <li><strong>JavaScript错误</strong>：代码执行异常</li>
                <li><strong>网络连接问题</strong>：无法连接到API服务器</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>🌐 网络环境检测</h3>
            <button onclick="checkNetworkEnvironment()">检测网络环境</button>
            <div id="networkResult" class="result" style="display:none;"></div>
        </div>
        
        <div class="test-section">
            <h3>🔗 直接API测试</h3>
            <button onclick="testAPIDirectly()">直接测试API</button>
            <div id="apiResult" class="result" style="display:none;"></div>
        </div>
        
        <div class="test-section">
            <h3>🚫 绕过代理测试</h3>
            <button onclick="testWithoutProxy()">绕过代理测试</button>
            <div id="proxyResult" class="result" style="display:none;"></div>
        </div>
        
        <div class="test-section">
            <h3>🔧 CORS测试</h3>
            <button onclick="testCORS()">测试CORS设置</button>
            <div id="corsResult" class="result" style="display:none;"></div>
        </div>
        
        <div class="test-section">
            <h3>📊 综合诊断</h3>
            <button onclick="runFullDiagnosis()">运行完整诊断</button>
            <div id="diagnosisResult" class="result" style="display:none;"></div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="/" style="color: #007bff; text-decoration: none;">← 返回主页</a>
        </div>
    </div>

    <script>
        console.log('代理诊断页面加载完成');
        
        function showResult(elementId, content, type = 'result') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.textContent = content;
            element.className = `result ${type}`;
        }
        
        // 检测网络环境
        function checkNetworkEnvironment() {
            console.log('检测网络环境');
            
            const info = {
                userAgent: navigator.userAgent,
                language: navigator.language,
                platform: navigator.platform,
                cookieEnabled: navigator.cookieEnabled,
                onLine: navigator.onLine,
                url: window.location.href,
                protocol: window.location.protocol,
                host: window.location.host,
                port: window.location.port || '默认端口',
                referrer: document.referrer || '无',
                timestamp: new Date().toLocaleString()
            };
            
            const result = `🌐 网络环境信息:

📱 用户代理: ${info.userAgent}
🌍 语言: ${info.language}
💻 平台: ${info.platform}
🍪 Cookie启用: ${info.cookieEnabled}
📡 在线状态: ${info.onLine}

🔗 页面信息:
- URL: ${info.url}
- 协议: ${info.protocol}
- 主机: ${info.host}
- 端口: ${info.port}
- 来源: ${info.referrer}

⏰ 检测时间: ${info.timestamp}

${info.onLine ? '✅ 浏览器显示在线状态' : '❌ 浏览器显示离线状态'}`;
            
            showResult('networkResult', result, info.onLine ? 'success' : 'error');
        }
        
        // 直接测试API
        async function testAPIDirectly() {
            console.log('直接测试API');
            showResult('apiResult', '🔄 正在测试API连接...', 'warning');
            
            try {
                const startTime = Date.now();
                
                // 测试多种请求方式
                const tests = [];
                
                // 测试1: 基本fetch
                try {
                    const response1 = await fetch('/api/health');
                    const endTime1 = Date.now();
                    const data1 = await response1.json();
                    tests.push(`✅ 基本fetch: 成功 (${endTime1 - startTime}ms)`);
                    tests.push(`   状态: ${response1.status}`);
                    tests.push(`   数据: ${JSON.stringify(data1)}`);
                } catch (error1) {
                    tests.push(`❌ 基本fetch: 失败 - ${error1.message}`);
                }
                
                // 测试2: 带完整URL
                try {
                    const response2 = await fetch('http://localhost:3001/api/health');
                    const data2 = await response2.json();
                    tests.push(`✅ 完整URL: 成功`);
                    tests.push(`   数据: ${JSON.stringify(data2)}`);
                } catch (error2) {
                    tests.push(`❌ 完整URL: 失败 - ${error2.message}`);
                }
                
                // 测试3: 带超时控制
                try {
                    const controller = new AbortController();
                    setTimeout(() => controller.abort(), 5000);
                    
                    const response3 = await fetch('/api/health', {
                        signal: controller.signal,
                        headers: {
                            'Accept': 'application/json',
                            'Cache-Control': 'no-cache'
                        }
                    });
                    const data3 = await response3.json();
                    tests.push(`✅ 超时控制: 成功`);
                } catch (error3) {
                    if (error3.name === 'AbortError') {
                        tests.push(`⏰ 超时控制: 请求超时`);
                    } else {
                        tests.push(`❌ 超时控制: 失败 - ${error3.message}`);
                    }
                }
                
                const result = `🔗 API连接测试结果:

${tests.join('\n')}

⏰ 总测试时间: ${Date.now() - startTime}ms`;
                
                showResult('apiResult', result, tests.some(t => t.includes('✅')) ? 'success' : 'error');
                
            } catch (error) {
                showResult('apiResult', `❌ API测试失败: ${error.message}`, 'error');
            }
        }
        
        // 绕过代理测试
        async function testWithoutProxy() {
            console.log('绕过代理测试');
            showResult('proxyResult', '🔄 正在测试代理绕过...', 'warning');
            
            try {
                // 尝试使用127.0.0.1而不是localhost
                const tests = [];
                
                // 测试1: 使用127.0.0.1
                try {
                    const response1 = await fetch('http://127.0.0.1:3001/api/health');
                    const data1 = await response1.json();
                    tests.push(`✅ 127.0.0.1: 成功`);
                    tests.push(`   数据: ${JSON.stringify(data1)}`);
                } catch (error1) {
                    tests.push(`❌ 127.0.0.1: 失败 - ${error1.message}`);
                }
                
                // 测试2: 使用相对路径
                try {
                    const response2 = await fetch('./api/health');
                    const data2 = await response2.json();
                    tests.push(`✅ 相对路径: 成功`);
                } catch (error2) {
                    tests.push(`❌ 相对路径: 失败 - ${error2.message}`);
                }
                
                // 测试3: 检查代理设置
                const proxyInfo = [];
                if (typeof navigator.connection !== 'undefined') {
                    proxyInfo.push(`网络类型: ${navigator.connection.effectiveType || '未知'}`);
                }
                
                const result = `🚫 代理绕过测试:

${tests.join('\n')}

🔍 代理检测:
- 当前页面协议: ${window.location.protocol}
- 当前页面主机: ${window.location.host}
- 是否HTTPS: ${window.location.protocol === 'https:' ? '是' : '否'}
${proxyInfo.length > 0 ? proxyInfo.join('\n') : ''}

💡 建议:
${tests.some(t => t.includes('127.0.0.1') && t.includes('✅')) ? 
'✅ 使用127.0.0.1可以绕过代理问题' : 
'⚠️ 可能存在代理或网络配置问题'}`;
                
                showResult('proxyResult', result, tests.some(t => t.includes('✅')) ? 'success' : 'warning');
                
            } catch (error) {
                showResult('proxyResult', `❌ 代理测试失败: ${error.message}`, 'error');
            }
        }
        
        // 测试CORS
        async function testCORS() {
            console.log('测试CORS');
            showResult('corsResult', '🔄 正在测试CORS设置...', 'warning');
            
            try {
                const response = await fetch('/api/health', {
                    method: 'GET',
                    headers: {
                        'Origin': window.location.origin,
                        'Access-Control-Request-Method': 'GET',
                        'Access-Control-Request-Headers': 'Content-Type'
                    }
                });
                
                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
                    'Access-Control-Allow-Credentials': response.headers.get('Access-Control-Allow-Credentials')
                };
                
                const result = `🔧 CORS测试结果:

📡 请求状态: ${response.status} ${response.statusText}

🔒 CORS头部:
- Allow-Origin: ${corsHeaders['Access-Control-Allow-Origin'] || '未设置'}
- Allow-Methods: ${corsHeaders['Access-Control-Allow-Methods'] || '未设置'}
- Allow-Headers: ${corsHeaders['Access-Control-Allow-Headers'] || '未设置'}
- Allow-Credentials: ${corsHeaders['Access-Control-Allow-Credentials'] || '未设置'}

🌐 请求信息:
- 来源: ${window.location.origin}
- 目标: ${window.location.origin}/api/health

${response.ok ? '✅ CORS配置正常' : '❌ CORS配置可能有问题'}`;
                
                showResult('corsResult', result, response.ok ? 'success' : 'error');
                
            } catch (error) {
                showResult('corsResult', `❌ CORS测试失败: ${error.message}`, 'error');
            }
        }
        
        // 运行完整诊断
        async function runFullDiagnosis() {
            console.log('运行完整诊断');
            showResult('diagnosisResult', '🔄 正在运行完整诊断...', 'warning');
            
            const diagnosis = [];
            const startTime = Date.now();
            
            // 1. 基础环境检查
            diagnosis.push('🔍 完整诊断报告');
            diagnosis.push('=' .repeat(50));
            diagnosis.push('');
            
            diagnosis.push('📱 浏览器环境:');
            diagnosis.push(`- 用户代理: ${navigator.userAgent.split(' ').slice(-2).join(' ')}`);
            diagnosis.push(`- 在线状态: ${navigator.onLine ? '在线' : '离线'}`);
            diagnosis.push(`- Cookie: ${navigator.cookieEnabled ? '启用' : '禁用'}`);
            diagnosis.push('');
            
            // 2. 网络连接测试
            diagnosis.push('🌐 网络连接测试:');
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                diagnosis.push(`✅ API连接: 成功 (${response.status})`);
                diagnosis.push(`✅ 数据解析: ${JSON.stringify(data)}`);
            } catch (error) {
                diagnosis.push(`❌ API连接: 失败 - ${error.message}`);
            }
            diagnosis.push('');
            
            // 3. 代理检测
            diagnosis.push('🚫 代理检测:');
            try {
                const response127 = await fetch('http://127.0.0.1:3001/api/health');
                diagnosis.push(`✅ 127.0.0.1: 可访问`);
            } catch (error) {
                diagnosis.push(`❌ 127.0.0.1: ${error.message}`);
            }
            diagnosis.push('');
            
            // 4. 问题诊断和解决方案
            diagnosis.push('🔧 问题诊断:');
            
            if (navigator.onLine) {
                diagnosis.push('✅ 网络连接正常');
            } else {
                diagnosis.push('❌ 网络连接异常');
            }
            
            diagnosis.push('');
            diagnosis.push('💡 解决方案:');
            diagnosis.push('1. 如果使用代理，请尝试:');
            diagnosis.push('   - 在浏览器中禁用代理');
            diagnosis.push('   - 将localhost添加到代理例外列表');
            diagnosis.push('   - 使用127.0.0.1:3001替代localhost:3001');
            diagnosis.push('');
            diagnosis.push('2. 如果仍有问题，请尝试:');
            diagnosis.push('   - 清除浏览器缓存');
            diagnosis.push('   - 使用无痕模式');
            diagnosis.push('   - 检查防火墙设置');
            diagnosis.push('');
            
            diagnosis.push(`⏰ 诊断完成时间: ${Date.now() - startTime}ms`);
            
            showResult('diagnosisResult', diagnosis.join('\n'), 'success');
        }
        
        // 页面加载完成后自动运行基础检测
        window.onload = function() {
            console.log('页面加载完成');
            setTimeout(checkNetworkEnvironment, 1000);
        };
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务状态检查</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .status-online {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .status-offline {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .status-checking {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .info {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        pre {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 服务状态检查</h1>
        
        <div class="info">
            <h3>📋 状态说明</h3>
            <p><strong>服务状态</strong>是指后端API服务器的运行状态：</p>
            <ul>
                <li>✅ <strong>绿色</strong>：服务正常运行，可以处理XML分析请求</li>
                <li>❌ <strong>红色</strong>：服务离线或出现错误</li>
                <li>🔄 <strong>黄色</strong>：正在检查服务状态</li>
            </ul>
            <p><strong>无需手动连接</strong>，页面会自动检查服务状态。</p>
        </div>
        
        <div class="status-card status-checking" id="apiStatus">
            <h3>🌐 API服务状态</h3>
            <p id="apiMessage">正在检查...</p>
            <button onclick="checkAPI()">🔄 重新检查</button>
        </div>
        
        <div class="status-card status-checking" id="serverStatus">
            <h3>🖥️ 服务器状态</h3>
            <p id="serverMessage">正在检查...</p>
            <button onclick="checkServer()">🔄 重新检查</button>
        </div>
        
        <div class="status-card" id="networkStatus">
            <h3>🌍 网络连接</h3>
            <p id="networkMessage">正在检查...</p>
            <button onclick="checkNetwork()">🔄 重新检查</button>
        </div>
        
        <div class="info">
            <h3>🔧 详细信息</h3>
            <p><strong>当前时间:</strong> <span id="currentTime"></span></p>
            <p><strong>页面URL:</strong> <span id="pageURL"></span></p>
            <p><strong>API地址:</strong> <span id="apiURL"></span></p>
            <p><strong>浏览器:</strong> <span id="browserInfo"></span></p>
        </div>
        
        <div class="info">
            <h3>📊 检查结果</h3>
            <pre id="checkResults">等待检查...</pre>
        </div>
        
        <div style="text-align: center; margin-top: 20px;">
            <button onclick="checkAll()" style="background: #28a745;">🚀 全面检查</button>
            <a href="/" style="margin-left: 10px; color: #007bff; text-decoration: none;">← 返回主页</a>
        </div>
    </div>

    <script>
        // 更新页面信息
        document.getElementById('currentTime').textContent = new Date().toLocaleString();
        document.getElementById('pageURL').textContent = window.location.href;
        document.getElementById('apiURL').textContent = window.location.origin + '/api';
        document.getElementById('browserInfo').textContent = navigator.userAgent.split(' ').slice(-2).join(' ');
        
        let checkResults = [];
        
        // 添加检查结果
        function addResult(test, status, message, details = '') {
            const timestamp = new Date().toLocaleTimeString();
            checkResults.push(`[${timestamp}] ${test}: ${status} - ${message}`);
            if (details) {
                checkResults.push(`    详情: ${details}`);
            }
            document.getElementById('checkResults').textContent = checkResults.join('\n');
        }
        
        // 更新状态卡片
        function updateStatus(cardId, messageId, status, message) {
            const card = document.getElementById(cardId);
            const messageEl = document.getElementById(messageId);
            
            card.className = `status-card status-${status}`;
            messageEl.textContent = message;
        }
        
        // 检查API服务
        async function checkAPI() {
            console.log('开始检查API服务');
            updateStatus('apiStatus', 'apiMessage', 'checking', '正在检查API服务...');
            
            try {
                const startTime = Date.now();
                const response = await fetch('/api/health');
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                if (data.status === 'ok') {
                    const message = `✅ API服务正常 (v${data.version}, 响应时间: ${responseTime}ms)`;
                    updateStatus('apiStatus', 'apiMessage', 'online', message);
                    addResult('API检查', '成功', `服务正常运行，版本 ${data.version}`, `响应时间: ${responseTime}ms`);
                } else {
                    throw new Error('API返回异常状态: ' + data.status);
                }
            } catch (error) {
                const message = `❌ API服务异常: ${error.message}`;
                updateStatus('apiStatus', 'apiMessage', 'offline', message);
                addResult('API检查', '失败', error.message);
            }
        }
        
        // 检查服务器
        async function checkServer() {
            console.log('开始检查服务器');
            updateStatus('serverStatus', 'serverMessage', 'checking', '正在检查服务器...');
            
            try {
                const startTime = Date.now();
                const response = await fetch('/');
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                if (response.ok) {
                    const message = `✅ 服务器正常 (响应时间: ${responseTime}ms)`;
                    updateStatus('serverStatus', 'serverMessage', 'online', message);
                    addResult('服务器检查', '成功', '服务器正常响应', `响应时间: ${responseTime}ms`);
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                const message = `❌ 服务器异常: ${error.message}`;
                updateStatus('serverStatus', 'serverMessage', 'offline', message);
                addResult('服务器检查', '失败', error.message);
            }
        }
        
        // 检查网络连接
        async function checkNetwork() {
            console.log('开始检查网络连接');
            updateStatus('networkStatus', 'networkMessage', 'checking', '正在检查网络连接...');
            
            try {
                // 检查是否在线
                if (!navigator.onLine) {
                    throw new Error('浏览器显示离线状态');
                }
                
                // 尝试访问当前页面
                const response = await fetch(window.location.href, { 
                    method: 'HEAD',
                    cache: 'no-cache'
                });
                
                if (response.ok) {
                    const message = `✅ 网络连接正常`;
                    updateStatus('networkStatus', 'networkMessage', 'online', message);
                    addResult('网络检查', '成功', '网络连接正常');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                const message = `❌ 网络连接异常: ${error.message}`;
                updateStatus('networkStatus', 'networkMessage', 'offline', message);
                addResult('网络检查', '失败', error.message);
            }
        }
        
        // 全面检查
        async function checkAll() {
            console.log('开始全面检查');
            checkResults = []; // 清空之前的结果
            addResult('全面检查', '开始', '开始进行全面系统检查');
            
            await checkNetwork();
            await new Promise(resolve => setTimeout(resolve, 500)); // 等待500ms
            
            await checkServer();
            await new Promise(resolve => setTimeout(resolve, 500)); // 等待500ms
            
            await checkAPI();
            
            addResult('全面检查', '完成', '所有检查项目已完成');
        }
        
        // 页面加载完成后自动检查
        window.onload = function() {
            console.log('页面加载完成，开始自动检查');
            setTimeout(checkAll, 1000); // 1秒后开始检查
        };
        
        // 监听网络状态变化
        window.addEventListener('online', function() {
            addResult('网络状态', '变化', '网络连接已恢复');
            checkAll();
        });
        
        window.addEventListener('offline', function() {
            addResult('网络状态', '变化', '网络连接已断开');
            updateStatus('networkStatus', 'networkMessage', 'offline', '❌ 网络连接已断开');
        });
    </script>
</body>
</html>

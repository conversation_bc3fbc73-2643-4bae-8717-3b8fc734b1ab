<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 13px;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 简单功能测试</h1>
        
        <div class="info">
            <strong>测试目的：</strong>验证所有基础功能是否正常工作，确保工具可以分享给他人使用。
        </div>
        
        <h3>1. 基础JavaScript测试</h3>
        <button onclick="testBasicJS()">测试JavaScript</button>
        <div id="jsResult" class="result" style="display:none;"></div>
        
        <h3>2. API连接测试</h3>
        <button onclick="testAPI()">测试API连接</button>
        <div id="apiResult" class="result" style="display:none;"></div>
        
        <h3>3. XML分析测试</h3>
        <button onclick="testXMLAnalysis()">测试XML分析</button>
        <div id="xmlResult" class="result" style="display:none;"></div>
        
        <h3>4. 综合测试</h3>
        <button onclick="runAllTests()">运行所有测试</button>
        <div id="allResult" class="result" style="display:none;"></div>
        
        <div style="margin-top: 30px; text-align: center;">
            <a href="/" style="color: #007bff; text-decoration: none;">← 返回主页</a>
        </div>
    </div>

    <script>
        console.log('简单测试页面加载完成');
        
        function showResult(elementId, content, type = 'result') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.textContent = content;
            element.className = `result ${type}`;
        }
        
        function testBasicJS() {
            console.log('测试基础JavaScript功能');
            try {
                const testData = {
                    time: new Date().toLocaleString(),
                    random: Math.random().toFixed(4),
                    userAgent: navigator.userAgent.split(' ').slice(-2).join(' ')
                };
                
                const result = `✅ JavaScript功能正常！

测试时间: ${testData.time}
随机数: ${testData.random}
浏览器: ${testData.userAgent}
页面URL: ${window.location.href}

所有基础功能都正常工作。`;
                
                showResult('jsResult', result, 'success');
            } catch (error) {
                showResult('jsResult', `❌ JavaScript错误: ${error.message}`, 'error');
            }
        }
        
        async function testAPI() {
            console.log('测试API连接');
            try {
                showResult('apiResult', '🔄 正在测试API连接...', 'info');
                
                const response = await fetch('/api/health');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                const result = `✅ API连接成功！

API状态: ${data.status}
API版本: ${data.version}
响应时间: ${data.timestamp}
服务器正常运行。`;
                
                showResult('apiResult', result, 'success');
            } catch (error) {
                const result = `❌ API连接失败: ${error.message}

可能的原因:
1. 后端服务器未启动
2. 网络连接问题
3. 端口被占用

请检查服务器状态。`;
                
                showResult('apiResult', result, 'error');
            }
        }
        
        async function testXMLAnalysis() {
            console.log('测试XML分析功能');
            try {
                showResult('xmlResult', '🔄 正在测试XML分析...', 'info');
                
                const testXML = `<?xml version="1.0"?>
<test>
    <measurement speed="50.0" time="1.0"/>
    <measurement speed="60.0" time="2.0"/>
</test>`;
                
                const response = await fetch('/api/analyze-text', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        xmlContent: testXML,
                        fileName: 'test.xml'
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                if (data.success) {
                    const summary = data.data.summary;
                    const result = `✅ XML分析功能正常！

数据点总数: ${summary.totalDataPoints}
有效数据点: ${summary.validDataPoints}
异常数量: ${summary.anomaliesCount}
整体状态: ${summary.overallStatus}

XML分析引擎工作正常。`;
                    
                    showResult('xmlResult', result, 'success');
                } else {
                    throw new Error(data.error || '分析失败');
                }
            } catch (error) {
                showResult('xmlResult', `❌ XML分析测试失败: ${error.message}`, 'error');
            }
        }
        
        async function runAllTests() {
            console.log('运行所有测试');
            showResult('allResult', '🔄 正在运行所有测试...', 'info');
            
            const results = [];
            
            // 测试1: JavaScript
            try {
                testBasicJS();
                results.push('✅ JavaScript功能: 正常');
            } catch (error) {
                results.push('❌ JavaScript功能: 失败');
            }
            
            // 测试2: API连接
            try {
                const response = await fetch('/api/health');
                if (response.ok) {
                    results.push('✅ API连接: 正常');
                } else {
                    results.push('❌ API连接: 失败');
                }
            } catch (error) {
                results.push('❌ API连接: 失败');
            }
            
            // 测试3: XML分析
            try {
                const testXML = '<?xml version="1.0"?><test><measurement speed="50.0" time="1.0"/></test>';
                const response = await fetch('/api/analyze-text', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ xmlContent: testXML, fileName: 'test.xml' })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        results.push('✅ XML分析: 正常');
                    } else {
                        results.push('❌ XML分析: 失败');
                    }
                } else {
                    results.push('❌ XML分析: 失败');
                }
            } catch (error) {
                results.push('❌ XML分析: 失败');
            }
            
            const allPassed = results.every(r => r.includes('✅'));
            const summary = allPassed ? 
                '🎉 所有测试通过！工具可以正常分享给他人使用。' : 
                '⚠️ 部分测试失败，请检查相关功能。';
            
            const finalResult = `${summary}

测试结果:
${results.join('\n')}

测试时间: ${new Date().toLocaleString()}
测试环境: ${navigator.userAgent.split(' ').slice(-2).join(' ')}`;
            
            showResult('allResult', finalResult, allPassed ? 'success' : 'error');
        }
        
        // 页面加载完成后自动运行基础测试
        window.onload = function() {
            console.log('页面加载完成，运行基础测试');
            setTimeout(testBasicJS, 1000);
        };
    </script>
</body>
</html>

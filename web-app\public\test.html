<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XML变速数据检测工具 - 测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 30px;
            color: #333;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            background: #f9f9f9;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #1890ff;
            background: #f0f8ff;
        }
        .upload-area.dragover {
            border-color: #1890ff;
            background: #e6f7ff;
        }
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .btn:hover {
            background: #40a9ff;
        }
        .results {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            display: none;
        }
        .loading {
            text-align: center;
            padding: 20px;
            display: none;
        }
        .error {
            color: #ff4d4f;
            background: #fff2f0;
            border: 1px solid #ffccc7;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            color: #52c41a;
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        textarea {
            width: 100%;
            height: 200px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            font-size: 13px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 XML变速数据检测工具</h1>
            <p>智能分析XML文件中的速度数据，检测异常并生成详细报告</p>
        </div>

        <div class="upload-section">
            <h3>📁 文件上传分析</h3>
            <div class="upload-area" id="uploadArea">
                <p>🔍 拖拽XML文件到此处，或点击选择文件</p>
                <input type="file" id="fileInput" accept=".xml" style="display: none;">
                <button class="btn" onclick="document.getElementById('fileInput').click()">选择文件</button>
            </div>
        </div>

        <div class="text-section">
            <h3>📝 文本编辑器</h3>
            <textarea id="xmlContent" placeholder="在此粘贴或输入XML内容..."></textarea>
            <div style="margin-top: 10px;">
                <button class="btn" onclick="loadExample()">加载示例</button>
                <button class="btn" onclick="analyzeText()">分析XML</button>
                <button class="btn" onclick="clearText()">清空</button>
            </div>
        </div>

        <div class="loading" id="loading">
            <p>🔄 正在分析XML数据...</p>
        </div>

        <div class="results" id="results">
            <h3>📊 分析结果</h3>
            <div id="resultContent"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api';

        // 文件上传处理
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');

        uploadArea.addEventListener('click', () => fileInput.click());
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                analyzeFile(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                analyzeFile(e.target.files[0]);
            }
        });

        // 分析文件
        async function analyzeFile(file) {
            if (!file.name.toLowerCase().endsWith('.xml')) {
                showError('请选择XML文件');
                return;
            }

            const formData = new FormData();
            formData.append('xmlFile', file);

            showLoading(true);
            try {
                const response = await fetch(`${API_BASE}/analyze`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                if (result.success) {
                    showResults(result.data);
                } else {
                    showError(result.error || '分析失败');
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 分析文本
        async function analyzeText() {
            const xmlContent = document.getElementById('xmlContent').value.trim();
            if (!xmlContent) {
                showError('请输入XML内容');
                return;
            }

            showLoading(true);
            try {
                const response = await fetch(`${API_BASE}/analyze-text`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        xmlContent: xmlContent,
                        fileName: 'custom.xml'
                    })
                });

                const result = await response.json();
                if (result.success) {
                    showResults(result.data);
                } else {
                    showError(result.error || '分析失败');
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 加载示例
        function loadExample() {
            const exampleXML = `<?xml version="1.0" encoding="UTF-8"?>
<vehicle_test>
    <metadata>
        <test_id>DEMO_001</test_id>
        <date>2024-01-15</date>
    </metadata>
    
    <speed_data>
        <measurement speed="0.0" time="0.0"/>
        <measurement speed="15.5" time="1.0"/>
        <measurement speed="32.1" time="2.0"/>
        <measurement speed="48.7" time="3.0"/>
        <measurement speed="52.3" time="4.0"/>
        <measurement speed="45.8" time="5.0"/>
        <measurement speed="28.2" time="6.0"/>
        <measurement speed="12.1" time="7.0"/>
        <measurement speed="0.0" time="8.0"/>
    </speed_data>
</vehicle_test>`;
            
            document.getElementById('xmlContent').value = exampleXML;
        }

        // 清空文本
        function clearText() {
            document.getElementById('xmlContent').value = '';
        }

        // 显示加载状态
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
            document.getElementById('results').style.display = 'none';
        }

        // 显示错误
        function showError(message) {
            const resultContent = document.getElementById('resultContent');
            resultContent.innerHTML = `<div class="error">❌ ${message}</div>`;
            document.getElementById('results').style.display = 'block';
        }

        // 显示结果
        function showResults(data) {
            const { metadata, summary, speedAnalysis, anomalies } = data;
            
            const statsHtml = `
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-value">${summary.totalDataPoints}</div>
                        <div class="stat-label">数据点总数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${summary.validDataPoints}</div>
                        <div class="stat-label">有效数据</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${summary.anomaliesCount}</div>
                        <div class="stat-label">异常数量</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${metadata.processingTime}ms</div>
                        <div class="stat-label">处理时间</div>
                    </div>
                </div>
            `;

            const speedStatsHtml = `
                <h4>📈 速度统计</h4>
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-value">${speedAnalysis.minSpeed.toFixed(2)}</div>
                        <div class="stat-label">最小速度</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${speedAnalysis.maxSpeed.toFixed(2)}</div>
                        <div class="stat-label">最大速度</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${speedAnalysis.averageSpeed.toFixed(2)}</div>
                        <div class="stat-label">平均速度</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${speedAnalysis.standardDeviation.toFixed(2)}</div>
                        <div class="stat-label">标准差</div>
                    </div>
                </div>
            `;

            const anomaliesHtml = anomalies.length > 0 
                ? `<h4>⚠️ 检测到 ${anomalies.length} 个异常</h4>
                   ${anomalies.map(a => `<div class="error">• ${a.description}</div>`).join('')}`
                : `<div class="success">✅ 未发现异常数据</div>`;

            const resultContent = document.getElementById('resultContent');
            resultContent.innerHTML = `
                <div class="success">✅ 分析完成！文件: ${metadata.fileName}</div>
                ${statsHtml}
                ${speedStatsHtml}
                ${anomaliesHtml}
            `;
            
            document.getElementById('results').style.display = 'block';
        }
    </script>
</body>
</html>

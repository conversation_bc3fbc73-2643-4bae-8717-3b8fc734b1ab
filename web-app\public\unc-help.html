<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UNC路径使用帮助</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .header h1 {
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .section {
            margin-bottom: 30px;
        }
        
        .section h2 {
            color: #667eea;
            border-left: 4px solid #667eea;
            padding-left: 15px;
            margin-bottom: 15px;
        }
        
        .example-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 14px;
        }
        
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            color: #155724;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            color: #856404;
        }
        
        .error-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            color: #721c24;
        }
        
        .btn {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            text-decoration: none;
            margin: 10px 5px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        ul, ol {
            padding-left: 20px;
        }
        
        li {
            margin-bottom: 8px;
        }
        
        code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
            color: #e83e8c;
        }
        
        .toc {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .toc ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .toc a {
            color: #667eea;
            text-decoration: none;
        }
        
        .toc a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 UNC路径使用帮助</h1>
            <p>XML变速数据检测工具 - 网络文件读取功能详细说明</p>
        </div>
        
        <div class="toc">
            <h3>📋 目录</h3>
            <ul>
                <li><a href="#what-is-unc">什么是UNC路径</a></li>
                <li><a href="#path-format">路径格式说明</a></li>
                <li><a href="#examples">使用示例</a></li>
                <li><a href="#permissions">权限配置</a></li>
                <li><a href="#troubleshooting">故障排除</a></li>
                <li><a href="#security">安全注意事项</a></li>
            </ul>
        </div>
        
        <div class="section" id="what-is-unc">
            <h2>🤔 什么是UNC路径</h2>
            <p><strong>UNC (Universal Naming Convention)</strong> 是一种在网络上标识共享资源的标准方法。它允许您直接访问网络上其他计算机的共享文件夹和文件。</p>
            
            <div class="success-box">
                <strong>✅ UNC路径的优势：</strong>
                <ul>
                    <li>无需将文件复制到本地</li>
                    <li>直接访问服务器上的最新文件</li>
                    <li>支持大文件处理</li>
                    <li>保持文件的原始位置</li>
                </ul>
            </div>
        </div>
        
        <div class="section" id="path-format">
            <h2>📝 路径格式说明</h2>
            
            <h3>基本格式</h3>
            <div class="example-box">
\\服务器名\共享文件夹\路径\文件名.xml
            </div>
            
            <h3>格式组成部分</h3>
            <ul>
                <li><strong>\\\\</strong> - UNC路径标识符（必须）</li>
                <li><strong>服务器名</strong> - 计算机名或IP地址</li>
                <li><strong>共享文件夹</strong> - 在服务器上共享的文件夹名</li>
                <li><strong>路径</strong> - 共享文件夹内的子目录路径（可选）</li>
                <li><strong>文件名.xml</strong> - 要分析的XML文件名</li>
            </ul>
            
            <h3>支持的格式</h3>
            <div class="example-box">
Windows格式：\\server\share\file.xml
Unix格式：  //server/share/file.xml
            </div>
        </div>
        
        <div class="section" id="examples">
            <h2>💡 使用示例</h2>
            
            <h3>1. 使用IP地址</h3>
            <div class="example-box">
\\*************\xmldata\test\sample.xml
\\*********\shared\documents\speed_data.xml
            </div>
            
            <h3>2. 使用计算机名</h3>
            <div class="example-box">
\\FILESERVER\xmlfiles\analysis\data.xml
\\SERVER01\public\xml\vehicle_test.xml
            </div>
            
            <h3>3. 使用域名</h3>
            <div class="example-box">
\\fileserver.company.com\data\xml\test.xml
\\nas.local\backup\xml_files\sample.xml
            </div>
            
            <h3>4. Unix风格路径</h3>
            <div class="example-box">
//*************/xmldata/test/sample.xml
//fileserver/shared/documents/data.xml
            </div>
        </div>
        
        <div class="section" id="permissions">
            <h2>🔐 权限配置</h2>
            
            <h3>Windows网络共享设置</h3>
            <ol>
                <li><strong>创建共享文件夹</strong>
                    <ul>
                        <li>右键点击要共享的文件夹</li>
                        <li>选择"属性" → "共享"标签</li>
                        <li>点击"高级共享"</li>
                        <li>勾选"共享此文件夹"</li>
                    </ul>
                </li>
                
                <li><strong>设置权限</strong>
                    <ul>
                        <li>点击"权限"按钮</li>
                        <li>添加需要访问的用户或组</li>
                        <li>设置适当的权限（至少需要"读取"权限）</li>
                    </ul>
                </li>
                
                <li><strong>网络发现</strong>
                    <ul>
                        <li>确保启用了网络发现</li>
                        <li>确保启用了文件和打印机共享</li>
                    </ul>
                </li>
            </ol>
            
            <div class="warning-box">
                <strong>⚠️ 注意：</strong>确保运行Web服务器的用户账户有权限访问网络共享文件夹。
            </div>
        </div>
        
        <div class="section" id="troubleshooting">
            <h2>🔧 故障排除</h2>
            
            <h3>常见错误及解决方案</h3>
            
            <div class="error-box">
                <strong>❌ 错误：文件或路径不存在</strong>
                <ul>
                    <li>检查UNC路径是否正确</li>
                    <li>确认服务器名或IP地址正确</li>
                    <li>验证共享文件夹名称</li>
                    <li>确认文件确实存在</li>
                </ul>
            </div>
            
            <div class="error-box">
                <strong>❌ 错误：访问被拒绝</strong>
                <ul>
                    <li>检查网络权限设置</li>
                    <li>确认用户账户有访问权限</li>
                    <li>尝试使用管理员权限运行服务器</li>
                    <li>检查防火墙设置</li>
                </ul>
            </div>
            
            <div class="error-box">
                <strong>❌ 错误：网络路径未找到</strong>
                <ul>
                    <li>检查网络连接</li>
                    <li>确认目标服务器在线</li>
                    <li>验证网络发现设置</li>
                    <li>尝试ping目标服务器</li>
                </ul>
            </div>
            
            <h3>测试连接</h3>
            <p>在命令提示符中测试UNC路径：</p>
            <div class="example-box">
dir \\服务器名\共享文件夹
            </div>
        </div>
        
        <div class="section" id="security">
            <h2>🛡️ 安全注意事项</h2>
            
            <div class="warning-box">
                <strong>🔒 安全建议：</strong>
                <ul>
                    <li><strong>最小权限原则</strong>：只授予必要的读取权限</li>
                    <li><strong>网络隔离</strong>：在受信任的网络环境中使用</li>
                    <li><strong>访问日志</strong>：监控文件访问记录</li>
                    <li><strong>定期审查</strong>：定期检查共享权限设置</li>
                </ul>
            </div>
            
            <h3>支持的文件限制</h3>
            <ul>
                <li><strong>文件类型</strong>：仅支持.xml文件</li>
                <li><strong>文件大小</strong>：最大10MB</li>
                <li><strong>访问方式</strong>：只读访问，不会修改原文件</li>
                <li><strong>内容验证</strong>：自动验证XML格式</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #dee2e6;">
            <a href="/unc-reader.html" class="btn">🌐 开始使用UNC读取</a>
            <a href="/" class="btn btn-secondary">🏠 返回主页</a>
        </div>
    </div>
</body>
</html>

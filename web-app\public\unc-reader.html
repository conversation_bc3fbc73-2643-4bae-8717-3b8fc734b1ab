<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UNC路径文件读取 - XML变速数据检测工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .input-group {
            margin-bottom: 20px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .input-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .input-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            margin: 5px;
        }
        
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .result-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .result-area.success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .result-area.error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        
        .result-area.loading {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        
        .file-browser {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .file-item {
            padding: 10px 15px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background 0.2s;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .file-item:hover {
            background: #e9ecef;
        }
        
        .file-item.xml {
            background: #e7f3ff;
        }
        
        .file-item.xml:hover {
            background: #cce7ff;
        }
        
        .file-item.directory {
            background: #fff3cd;
            font-weight: 500;
        }
        
        .file-item.directory:hover {
            background: #ffeaa7;
        }
        
        .file-info {
            display: flex;
            align-items: center;
        }
        
        .file-icon {
            margin-right: 10px;
            font-size: 16px;
        }
        
        .file-details {
            font-size: 12px;
            color: #666;
        }
        
        .breadcrumb {
            background: #e9ecef;
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-family: monospace;
            font-size: 13px;
            word-break: break-all;
        }
        
        .help-text {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        
        .stat-item {
            text-align: center;
            background: white;
            padding: 10px;
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .stat-value {
            font-size: 20px;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }
        
        .back-link {
            display: inline-block;
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .back-link:hover {
            color: white;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="/" class="back-link">← 返回主页</a>
        <a href="/unc-help.html" class="back-link" style="margin-left: 20px;">📖 使用帮助</a>
        
        <div class="header">
            <h1>🌐 UNC路径文件读取</h1>
            <p>直接读取网络共享文件夹中的XML文件进行分析</p>
        </div>
        
        <div class="card">
            <h3>📁 UNC路径输入</h3>
            
            <div class="help-text">
                <strong>💡 使用说明：</strong><br>
                • UNC路径格式：<code>\\服务器名\共享文件夹\文件路径</code><br>
                • 示例：<code>\\*************\shared\data\test.xml</code><br>
                • 支持：<code>\\server\share\file.xml</code> 或 <code>//server/share/file.xml</code><br>
                • 仅支持.xml文件，最大10MB
            </div>
            
            <div class="input-group">
                <label for="uncPath">UNC路径：</label>
                <input 
                    type="text" 
                    id="uncPath" 
                    placeholder="\\服务器名\共享文件夹\路径\文件.xml"
                    value=""
                >
            </div>
            
            <div style="margin-bottom: 20px;">
                <button onclick="browseDirectory()" class="btn btn-secondary" id="browseBtn">
                    📂 浏览目录
                </button>
                <button onclick="readUNCFile()" class="btn btn-success" id="readBtn">
                    📖 读取文件
                </button>
                <button onclick="clearResults()" class="btn btn-secondary">
                    🗑️ 清空结果
                </button>
            </div>
        </div>
        
        <div class="card" id="browserCard" style="display: none;">
            <h3>📂 文件浏览器</h3>
            <div class="breadcrumb" id="currentPath">当前路径：</div>
            
            <div class="stats" id="directoryStats" style="display: none;">
                <div class="stat-item">
                    <div class="stat-value" id="xmlCount">0</div>
                    <div class="stat-label">XML文件</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="fileCount">0</div>
                    <div class="stat-label">总文件</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="dirCount">0</div>
                    <div class="stat-label">文件夹</div>
                </div>
            </div>
            
            <div class="file-browser" id="fileBrowser">
                <div style="padding: 20px; text-align: center; color: #666;">
                    输入UNC路径并点击"浏览目录"开始浏览
                </div>
            </div>
        </div>
        
        <div class="card" id="resultsCard" style="display: none;">
            <h3>📊 分析结果</h3>
            <div class="result-area" id="results">
                等待分析结果...
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '/api';
        let currentDirectory = '';
        
        // 浏览目录
        async function browseDirectory() {
            const uncPath = document.getElementById('uncPath').value.trim();
            
            if (!uncPath) {
                showResult('请输入UNC路径', 'error');
                return;
            }
            
            if (!uncPath.startsWith('\\\\') && !uncPath.startsWith('//')) {
                showResult('UNC路径必须以 \\\\\\\\ 或 // 开头', 'error');
                return;
            }
            
            setButtonState('browseBtn', true, '浏览中...');
            showResult('🔄 正在浏览目录...', 'loading');
            
            try {
                const response = await fetch(`${API_BASE}/browse-unc-directory`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ uncPath })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    currentDirectory = result.data.path;
                    displayDirectoryContents(result.data);
                    showResult('✅ 目录浏览成功', 'success');
                } else {
                    showResult(`❌ 浏览失败: ${result.error}`, 'error');
                }
            } catch (error) {
                console.error('浏览目录失败:', error);
                showResult(`❌ 网络错误: ${error.message}`, 'error');
            } finally {
                setButtonState('browseBtn', false, '📂 浏览目录');
            }
        }
        
        // 显示目录内容
        function displayDirectoryContents(data) {
            const browserCard = document.getElementById('browserCard');
            const currentPath = document.getElementById('currentPath');
            const fileBrowser = document.getElementById('fileBrowser');
            const directoryStats = document.getElementById('directoryStats');
            
            // 显示浏览器卡片
            browserCard.style.display = 'block';
            
            // 更新路径
            currentPath.textContent = `当前路径：${data.path}`;
            
            // 更新统计信息
            document.getElementById('xmlCount').textContent = data.xmlFiles;
            document.getElementById('fileCount').textContent = data.totalFiles;
            document.getElementById('dirCount').textContent = data.directories;
            directoryStats.style.display = 'grid';
            
            // 清空并重新填充文件列表
            fileBrowser.innerHTML = '';
            
            if (data.items.length === 0) {
                fileBrowser.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">目录为空</div>';
                return;
            }
            
            data.items.forEach(item => {
                const fileItem = document.createElement('div');
                fileItem.className = `file-item ${item.type}${item.isXML ? ' xml' : ''}`;
                
                const fileInfo = document.createElement('div');
                fileInfo.className = 'file-info';
                
                const icon = document.createElement('span');
                icon.className = 'file-icon';
                if (item.type === 'directory') {
                    icon.textContent = '📁';
                } else if (item.isXML) {
                    icon.textContent = '📄';
                } else {
                    icon.textContent = '📄';
                }
                
                const name = document.createElement('span');
                name.textContent = item.name;
                
                fileInfo.appendChild(icon);
                fileInfo.appendChild(name);
                
                const details = document.createElement('div');
                details.className = 'file-details';
                
                if (item.type === 'file') {
                    const size = formatFileSize(item.size);
                    const modified = new Date(item.modified).toLocaleString();
                    details.innerHTML = `${size}<br>${modified}`;
                } else {
                    details.textContent = '文件夹';
                }
                
                fileItem.appendChild(fileInfo);
                fileItem.appendChild(details);
                
                // 添加点击事件
                fileItem.addEventListener('click', () => {
                    if (item.type === 'directory') {
                        // 点击目录，更新路径并重新浏览
                        document.getElementById('uncPath').value = item.path;
                        browseDirectory();
                    } else if (item.isXML) {
                        // 点击XML文件，设置路径并可以读取
                        document.getElementById('uncPath').value = item.path;
                        showResult(`✅ 已选择文件: ${item.name}`, 'success');
                    }
                });
                
                fileBrowser.appendChild(fileItem);
            });
        }
        
        // 读取UNC文件
        async function readUNCFile() {
            const uncPath = document.getElementById('uncPath').value.trim();
            
            if (!uncPath) {
                showResult('请输入UNC文件路径', 'error');
                return;
            }
            
            if (!uncPath.startsWith('\\\\') && !uncPath.startsWith('//')) {
                showResult('UNC路径必须以 \\\\\\\\ 或 // 开头', 'error');
                return;
            }
            
            if (!uncPath.toLowerCase().endsWith('.xml')) {
                showResult('请选择XML文件', 'error');
                return;
            }
            
            setButtonState('readBtn', true, '读取中...');
            showResult('🔄 正在读取UNC文件...', 'loading');
            
            try {
                const response = await fetch(`${API_BASE}/read-unc-file`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ 
                        uncPath: uncPath,
                        fileName: uncPath.split(/[\\\/]/).pop()
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    displayAnalysisResults(result);
                    showResult('✅ 文件读取和分析成功', 'success');
                } else {
                    showResult(`❌ 读取失败: ${result.error}`, 'error');
                }
            } catch (error) {
                console.error('读取UNC文件失败:', error);
                showResult(`❌ 网络错误: ${error.message}`, 'error');
            } finally {
                setButtonState('readBtn', false, '📖 读取文件');
            }
        }
        
        // 显示分析结果
        function displayAnalysisResults(result) {
            const resultsCard = document.getElementById('resultsCard');
            const resultsDiv = document.getElementById('results');
            
            resultsCard.style.display = 'block';
            
            const { data, uncPath, fileName, fileSize } = result;
            const { summary, speedAnalysis, anomalies, metadata } = data;
            
            let html = `
                <div style="margin-bottom: 20px;">
                    <h4>📁 文件信息</h4>
                    <p><strong>UNC路径:</strong> ${uncPath}</p>
                    <p><strong>文件名:</strong> ${fileName}</p>
                    <p><strong>文件大小:</strong> ${formatFileSize(fileSize)}</p>
                    <p><strong>处理时间:</strong> ${metadata.processingTime}ms</p>
                </div>
                
                <div style="margin-bottom: 20px;">
                    <h4>📊 分析摘要</h4>
                    <div class="stats">
                        <div class="stat-item">
                            <div class="stat-value">${summary.totalDataPoints}</div>
                            <div class="stat-label">数据点总数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${summary.validDataPoints}</div>
                            <div class="stat-label">有效数据</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${summary.anomaliesCount}</div>
                            <div class="stat-label">异常数量</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" style="color: ${getStatusColor(summary.overallStatus)}">${summary.overallStatus.toUpperCase()}</div>
                            <div class="stat-label">整体状态</div>
                        </div>
                    </div>
                </div>
                
                <div style="margin-bottom: 20px;">
                    <h4>📈 速度分析</h4>
                    <div class="stats">
                        <div class="stat-item">
                            <div class="stat-value">${speedAnalysis.minSpeed.toFixed(2)}</div>
                            <div class="stat-label">最小速度</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${speedAnalysis.maxSpeed.toFixed(2)}</div>
                            <div class="stat-label">最大速度</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${speedAnalysis.averageSpeed.toFixed(2)}</div>
                            <div class="stat-label">平均速度</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${speedAnalysis.standardDeviation.toFixed(2)}</div>
                            <div class="stat-label">标准差</div>
                        </div>
                    </div>
                </div>
            `;
            
            if (anomalies.length > 0) {
                html += `
                    <div style="margin-bottom: 20px;">
                        <h4>⚠️ 检测到的异常 (${anomalies.length}个)</h4>
                        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 15px;">
                `;
                
                anomalies.forEach((anomaly, index) => {
                    html += `<p><strong>${index + 1}.</strong> ${anomaly.description}</p>`;
                });
                
                html += `</div></div>`;
            } else {
                html += `
                    <div style="margin-bottom: 20px;">
                        <h4>✅ 异常检测</h4>
                        <div style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; padding: 15px; color: #155724;">
                            <p>未发现异常数据，所有数据点都正常。</p>
                        </div>
                    </div>
                `;
            }
            
            resultsDiv.innerHTML = html;
        }
        
        // 辅助函数
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        function getStatusColor(status) {
            switch (status) {
                case 'pass': return '#28a745';
                case 'warning': return '#ffc107';
                case 'fail': return '#dc3545';
                default: return '#6c757d';
            }
        }
        
        function setButtonState(buttonId, disabled, text) {
            const button = document.getElementById(buttonId);
            button.disabled = disabled;
            if (text) button.textContent = text;
        }
        
        function showResult(message, type) {
            const resultsCard = document.getElementById('resultsCard');
            const resultsDiv = document.getElementById('results');
            
            resultsCard.style.display = 'block';
            resultsDiv.className = `result-area ${type}`;
            resultsDiv.textContent = message;
        }
        
        function clearResults() {
            document.getElementById('resultsCard').style.display = 'none';
            document.getElementById('browserCard').style.display = 'none';
            document.getElementById('uncPath').value = '';
            currentDirectory = '';
        }
        
        // 页面加载完成后的初始化
        window.onload = function() {
            console.log('UNC路径文件读取页面加载完成');
            
            // 设置一些示例路径
            const examples = [
                '\\\\*************\\shared\\data\\test.xml',
                '\\\\server\\xmlfiles\\sample.xml',
                '//fileserver/documents/data.xml'
            ];
            
            const uncPathInput = document.getElementById('uncPath');
            uncPathInput.placeholder = examples[Math.floor(Math.random() * examples.length)];
        };
    </script>
</body>
</html>

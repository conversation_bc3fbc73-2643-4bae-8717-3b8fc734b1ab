<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XML文件诊断工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #007bff;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background: #f8f9fa;
        }
        .file-input {
            display: none;
        }
        .upload-btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
        }
        .upload-btn:hover {
            background: #0056b3;
        }
        .result-section {
            margin-top: 30px;
            display: none;
        }
        .diagnostic-result {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .result-item {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 6px;
        }
        .result-found {
            background: #d4edda;
            border-left: 4px solid #28a745;
        }
        .result-not-found {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        .result-info {
            background: #d1ecf1;
            border-left: 4px solid #17a2b8;
        }
        .xml-structure {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .back-btn {
            background: #6c757d;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 20px;
        }
        .back-btn:hover {
            background: #545b62;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="/" class="back-btn">← 返回主页</a>
        
        <h1>🔍 XML文件诊断工具</h1>
        <p style="text-align: center; color: #666; margin-bottom: 30px;">
            分析XML文件结构，诊断为什么检测不到速度数据
        </p>

        <div class="upload-area" id="uploadArea">
            <p>📁 拖拽XML文件到这里，或点击选择文件</p>
            <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                选择XML文件
            </button>
            <input type="file" id="fileInput" class="file-input" accept=".xml" onchange="handleFile(this.files[0])">
        </div>

        <div class="result-section" id="resultSection">
            <h2>📊 诊断结果</h2>
            <div id="diagnosticResults"></div>
            
            <h3>🏗️ XML文件结构</h3>
            <div class="xml-structure" id="xmlStructure"></div>
        </div>
    </div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const resultSection = document.getElementById('resultSection');
        const diagnosticResults = document.getElementById('diagnosticResults');
        const xmlStructure = document.getElementById('xmlStructure');

        // 拖拽功能
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        function handleFile(file) {
            if (!file) return;
            
            if (!file.name.toLowerCase().endsWith('.xml')) {
                alert('请选择XML文件');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                const xmlContent = e.target.result;
                diagnoseXML(xmlContent, file.name);
            };
            reader.readAsText(file);
        }

        function diagnoseXML(xmlContent, fileName) {
            try {
                // 解析XML
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(xmlContent, 'text/xml');
                
                // 检查解析错误
                const parseError = xmlDoc.querySelector('parsererror');
                if (parseError) {
                    showError('XML解析失败: ' + parseError.textContent);
                    return;
                }

                // 分析XML结构
                const analysis = analyzeXMLStructure(xmlDoc, xmlContent);
                displayResults(analysis, fileName);
                
                resultSection.style.display = 'block';
                
            } catch (error) {
                showError('分析失败: ' + error.message);
            }
        }

        function analyzeXMLStructure(xmlDoc, xmlContent) {
            const speedAttributes = [
                'speed', 'velocity', 'rate', 'fps', 'framerate', 'frame_rate',
                'playback_speed', 'animation_speed', 'motion_speed', 'tempo',
                'velocity_x', 'velocity_y', 'velocity_z', 'vel', 'v'
            ];
            
            const timeAttributes = [
                'time', 'timestamp', 't', 'frame', 'frame_number', 'timecode',
                'duration', 'start_time', 'end_time', 'offset', 'delay'
            ];

            const analysis = {
                fileSize: xmlContent.length,
                rootElement: xmlDoc.documentElement.tagName,
                totalElements: xmlDoc.getElementsByTagName('*').length,
                speedAttributesFound: [],
                timeAttributesFound: [],
                allAttributes: new Set(),
                elementTypes: new Set(),
                structure: generateStructure(xmlDoc.documentElement, 0, 3)
            };

            // 查找所有元素和属性
            const allElements = xmlDoc.getElementsByTagName('*');
            for (let element of allElements) {
                analysis.elementTypes.add(element.tagName);
                
                // 检查属性
                for (let attr of element.attributes) {
                    analysis.allAttributes.add(attr.name);
                    
                    // 检查是否是速度属性
                    if (speedAttributes.includes(attr.name.toLowerCase())) {
                        analysis.speedAttributesFound.push({
                            element: element.tagName,
                            attribute: attr.name,
                            value: attr.value
                        });
                    }
                    
                    // 检查是否是时间属性
                    if (timeAttributes.includes(attr.name.toLowerCase())) {
                        analysis.timeAttributesFound.push({
                            element: element.tagName,
                            attribute: attr.name,
                            value: attr.value
                        });
                    }
                }
            }

            return analysis;
        }

        function generateStructure(element, depth, maxDepth) {
            if (depth > maxDepth) return '...';
            
            const indent = '  '.repeat(depth);
            let result = `${indent}<${element.tagName}`;
            
            // 添加属性
            for (let attr of element.attributes) {
                result += ` ${attr.name}="${attr.value}"`;
            }
            result += '>\n';
            
            // 添加子元素（限制数量）
            const children = Array.from(element.children).slice(0, 5);
            for (let child of children) {
                result += generateStructure(child, depth + 1, maxDepth);
            }
            
            if (element.children.length > 5) {
                result += `${indent}  ... (还有 ${element.children.length - 5} 个子元素)\n`;
            }
            
            result += `${indent}</${element.tagName}>\n`;
            return result;
        }

        function displayResults(analysis, fileName) {
            let html = `
                <div class="diagnostic-result">
                    <h3>📄 文件信息</h3>
                    <div class="result-item result-info">
                        <strong>文件名:</strong> ${fileName}<br>
                        <strong>文件大小:</strong> ${analysis.fileSize} 字节<br>
                        <strong>根元素:</strong> ${analysis.rootElement}<br>
                        <strong>总元素数:</strong> ${analysis.totalElements}
                    </div>
                </div>

                <div class="diagnostic-result">
                    <h3>🚀 速度属性检测</h3>
            `;

            if (analysis.speedAttributesFound.length > 0) {
                html += `<div class="result-item result-found">
                    <strong>✅ 找到 ${analysis.speedAttributesFound.length} 个速度属性:</strong><br>`;
                analysis.speedAttributesFound.forEach(item => {
                    html += `&lt;${item.element} ${item.attribute}="${item.value}"&gt;<br>`;
                });
                html += `</div>`;
            } else {
                html += `<div class="result-item result-not-found">
                    <strong>❌ 未找到速度属性</strong><br>
                    工具查找的属性: speed, velocity, rate, fps, framerate, frame_rate, playback_speed, animation_speed, motion_speed, tempo, velocity_x, velocity_y, velocity_z, vel, v
                </div>`;
            }

            html += `</div><div class="diagnostic-result">
                <h3>⏰ 时间属性检测</h3>`;

            if (analysis.timeAttributesFound.length > 0) {
                html += `<div class="result-item result-found">
                    <strong>✅ 找到 ${analysis.timeAttributesFound.length} 个时间属性:</strong><br>`;
                analysis.timeAttributesFound.forEach(item => {
                    html += `&lt;${item.element} ${item.attribute}="${item.value}"&gt;<br>`;
                });
                html += `</div>`;
            } else {
                html += `<div class="result-item result-not-found">
                    <strong>❌ 未找到时间属性</strong><br>
                    工具查找的属性: time, timestamp, t, frame, frame_number, timecode, duration, start_time, end_time, offset, delay
                </div>`;
            }

            html += `</div><div class="diagnostic-result">
                <h3>📋 所有属性列表</h3>
                <div class="result-item result-info">
                    <strong>发现的属性:</strong> ${Array.from(analysis.allAttributes).join(', ')}
                </div>
            </div>`;

            diagnosticResults.innerHTML = html;
            xmlStructure.textContent = analysis.structure;
        }

        function showError(message) {
            diagnosticResults.innerHTML = `
                <div class="diagnostic-result">
                    <div class="result-item result-not-found">
                        <strong>❌ 错误:</strong> ${message}
                    </div>
                </div>
            `;
            resultSection.style.display = 'block';
        }
    </script>
</body>
</html>

const express = require('express');
const cors = require('cors');
const multer = require('multer');
const path = require('path');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');

const xmlDetector = require('./src/xmlDetector');

const app = express();
const PORT = process.env.PORT || 3001;

// 安全中间件
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// 压缩响应
app.use(compression());

// 日志记录
app.use(morgan('combined'));

// CORS配置
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://yourdomain.com'] 
    : ['http://localhost:3000'],
  credentials: true
}));

// 请求限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 15分钟内最多100个请求
  message: {
    error: '请求过于频繁，请稍后再试'
  }
});
app.use('/api/', limiter);

// 解析JSON
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 文件上传配置
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB限制
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'text/xml' || 
        file.mimetype === 'application/xml' || 
        file.originalname.toLowerCase().endsWith('.xml')) {
      cb(null, true);
    } else {
      cb(new Error('只支持XML文件'), false);
    }
  }
});

// 静态文件服务
app.use(express.static(path.join(__dirname, 'public')));

// React应用静态文件服务（生产环境）
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, 'client/build')));
}

// API路由

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// XML文件上传和检测
app.post('/api/analyze', upload.single('xmlFile'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        error: '请上传XML文件'
      });
    }

    const xmlContent = req.file.buffer.toString('utf8');
    const fileName = req.file.originalname;

    console.log(`分析文件: ${fileName}, 大小: ${xmlContent.length} 字节`);

    // 执行XML检测
    const result = await xmlDetector.analyzeXML(xmlContent, fileName);

    res.json({
      success: true,
      data: result,
      fileName: fileName,
      fileSize: xmlContent.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('XML分析错误:', error);
    res.status(500).json({
      error: error.message || '分析XML文件时发生错误',
      timestamp: new Date().toISOString()
    });
  }
});

// XML文本内容检测
app.post('/api/analyze-text', async (req, res) => {
  try {
    const { xmlContent, fileName = 'untitled.xml' } = req.body;

    if (!xmlContent || typeof xmlContent !== 'string') {
      return res.status(400).json({
        error: '请提供有效的XML内容'
      });
    }

    if (xmlContent.length > 10 * 1024 * 1024) {
      return res.status(400).json({
        error: 'XML内容过大，请限制在10MB以内'
      });
    }

    console.log(`分析文本内容: ${fileName}, 大小: ${xmlContent.length} 字节`);

    // 执行XML检测
    const result = await xmlDetector.analyzeXML(xmlContent, fileName);

    res.json({
      success: true,
      data: result,
      fileName: fileName,
      fileSize: xmlContent.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('XML分析错误:', error);
    res.status(500).json({
      error: error.message || '分析XML内容时发生错误',
      timestamp: new Date().toISOString()
    });
  }
});

// 获取示例XML文件列表
app.get('/api/examples', (req, res) => {
  const examples = [
    {
      name: 'sample_speed_data.xml',
      description: '正常的车辆速度测试数据',
      category: 'normal'
    },
    {
      name: 'anomaly_data.xml', 
      description: '包含各种异常的测试数据',
      category: 'anomaly'
    }
  ];

  res.json({
    success: true,
    data: examples
  });
});

// 获取示例XML文件内容
app.get('/api/examples/:filename', (req, res) => {
  const { filename } = req.params;
  const fs = require('fs');
  const examplePath = path.join(__dirname, '../examples', filename);

  try {
    if (fs.existsSync(examplePath)) {
      const content = fs.readFileSync(examplePath, 'utf8');
      res.json({
        success: true,
        data: {
          filename: filename,
          content: content,
          size: content.length
        }
      });
    } else {
      res.status(404).json({
        error: '示例文件不存在'
      });
    }
  } catch (error) {
    console.error('读取示例文件错误:', error);
    res.status(500).json({
      error: '读取示例文件失败'
    });
  }
});

// UNC路径文件读取接口
app.post('/api/read-unc-file', async (req, res) => {
  try {
    const { uncPath, fileName } = req.body;

    if (!uncPath || typeof uncPath !== 'string') {
      return res.status(400).json({
        error: '请提供有效的UNC路径'
      });
    }

    // 验证UNC路径格式
    if (!uncPath.startsWith('\\\\') && !uncPath.startsWith('//')) {
      return res.status(400).json({
        error: 'UNC路径必须以 \\\\ 或 // 开头'
      });
    }

    console.log(`读取UNC文件: ${uncPath}`);

    const fs = require('fs');
    const path = require('path');

    // 检查文件是否存在
    if (!fs.existsSync(uncPath)) {
      return res.status(404).json({
        error: `文件不存在: ${uncPath}`
      });
    }

    // 检查是否为文件（不是目录）
    const stats = fs.statSync(uncPath);
    if (!stats.isFile()) {
      return res.status(400).json({
        error: `指定路径不是文件: ${uncPath}`
      });
    }

    // 检查文件大小
    if (stats.size > 10 * 1024 * 1024) { // 10MB限制
      return res.status(400).json({
        error: `文件过大，超过10MB限制: ${(stats.size / 1024 / 1024).toFixed(2)}MB`
      });
    }

    // 检查文件扩展名
    const fileExtension = path.extname(uncPath).toLowerCase();
    if (fileExtension !== '.xml') {
      return res.status(400).json({
        error: `不支持的文件类型: ${fileExtension}，仅支持.xml文件`
      });
    }

    // 读取文件内容
    const xmlContent = fs.readFileSync(uncPath, 'utf8');
    const actualFileName = fileName || path.basename(uncPath);

    console.log(`成功读取UNC文件: ${uncPath}, 大小: ${xmlContent.length} 字节`);

    // 执行XML检测
    const result = await xmlDetector.analyzeXML(xmlContent, actualFileName);

    res.json({
      success: true,
      data: result,
      uncPath: uncPath,
      fileName: actualFileName,
      fileSize: xmlContent.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('UNC文件读取错误:', error);

    let errorMessage = '读取UNC文件时发生错误';

    if (error.code === 'ENOENT') {
      errorMessage = '文件或路径不存在，请检查UNC路径是否正确';
    } else if (error.code === 'EACCES') {
      errorMessage = '访问被拒绝，请检查网络权限和文件权限';
    } else if (error.code === 'ENOTDIR') {
      errorMessage = '路径中的某个部分不是目录';
    } else if (error.code === 'EISDIR') {
      errorMessage = '指定路径是目录，不是文件';
    } else if (error.message.includes('XML解析失败')) {
      errorMessage = error.message;
    }

    res.status(500).json({
      error: errorMessage,
      details: error.message,
      code: error.code,
      timestamp: new Date().toISOString()
    });
  }
});

// UNC路径目录浏览接口
app.post('/api/browse-unc-directory', (req, res) => {
  try {
    const { uncPath } = req.body;

    if (!uncPath || typeof uncPath !== 'string') {
      return res.status(400).json({
        error: '请提供有效的UNC路径'
      });
    }

    // 验证UNC路径格式
    if (!uncPath.startsWith('\\\\') && !uncPath.startsWith('//')) {
      return res.status(400).json({
        error: 'UNC路径必须以 \\\\ 或 // 开头'
      });
    }

    console.log(`浏览UNC目录: ${uncPath}`);

    const fs = require('fs');
    const path = require('path');

    // 检查路径是否存在
    if (!fs.existsSync(uncPath)) {
      return res.status(404).json({
        error: `路径不存在: ${uncPath}`
      });
    }

    // 检查是否为目录
    const stats = fs.statSync(uncPath);
    if (!stats.isDirectory()) {
      return res.status(400).json({
        error: `指定路径不是目录: ${uncPath}`
      });
    }

    // 读取目录内容
    const items = fs.readdirSync(uncPath);
    const fileList = [];

    items.forEach(item => {
      try {
        const itemPath = path.join(uncPath, item);
        const itemStats = fs.statSync(itemPath);

        if (itemStats.isFile()) {
          const extension = path.extname(item).toLowerCase();
          fileList.push({
            name: item,
            path: itemPath,
            size: itemStats.size,
            modified: itemStats.mtime,
            isXML: extension === '.xml',
            type: 'file'
          });
        } else if (itemStats.isDirectory()) {
          fileList.push({
            name: item,
            path: itemPath,
            type: 'directory'
          });
        }
      } catch (error) {
        console.warn(`无法读取项目信息: ${item}`, error.message);
      }
    });

    // 按类型和名称排序
    fileList.sort((a, b) => {
      if (a.type !== b.type) {
        return a.type === 'directory' ? -1 : 1;
      }
      return a.name.localeCompare(b.name);
    });

    res.json({
      success: true,
      data: {
        path: uncPath,
        items: fileList,
        xmlFiles: fileList.filter(item => item.isXML).length,
        totalFiles: fileList.filter(item => item.type === 'file').length,
        directories: fileList.filter(item => item.type === 'directory').length
      }
    });

  } catch (error) {
    console.error('UNC目录浏览错误:', error);

    let errorMessage = '浏览UNC目录时发生错误';

    if (error.code === 'ENOENT') {
      errorMessage = '目录不存在，请检查UNC路径是否正确';
    } else if (error.code === 'EACCES') {
      errorMessage = '访问被拒绝，请检查网络权限';
    }

    res.status(500).json({
      error: errorMessage,
      details: error.message,
      code: error.code,
      timestamp: new Date().toISOString()
    });
  }
});

// 生产环境下的前端路由处理
if (process.env.NODE_ENV === 'production') {
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, 'client/build', 'index.html'));
  });
}

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        error: '文件大小超过限制（最大10MB）'
      });
    }
  }

  res.status(500).json({
    error: '服务器内部错误',
    timestamp: new Date().toISOString()
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    error: '接口不存在',
    path: req.path
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 XML变速数据检测工具Web服务器启动成功`);
  console.log(`📡 服务器地址: http://localhost:${PORT}`);
  console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);
  console.log(`⏰ 启动时间: ${new Date().toLocaleString()}`);
});

module.exports = app;

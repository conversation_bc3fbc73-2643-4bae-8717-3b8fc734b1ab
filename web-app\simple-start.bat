@echo off
chcp 65001 >nul
title VFX XML Analysis Tool

echo ========================================
echo   VFX XML Analysis Tool v2.0
echo ========================================
echo.
echo Professional VFX XML file analysis
echo Supports Maya, Blender, Cinema 4D, After Effects
echo Features: Drag Drop, Timewarp, Resize Detection
echo.

:: Change to script directory
cd /d "%~dp0"

:: Check Node.js
echo Checking Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found!
    echo.
    echo Please install Node.js from:
    echo https://nodejs.org
    echo.
    echo After installation, restart this script.
    echo.
    pause
    exit /b 1
)

:: Check server file
if not exist "server.js" (
    echo ERROR: server.js not found!
    echo Please run this script in the correct directory.
    echo.
    pause
    exit /b 1
)

:: Install dependencies if needed
if not exist "node_modules" (
    echo.
    echo Installing dependencies...
    echo This may take a few minutes...
    echo.
    npm install
    if errorlevel 1 (
        echo.
        echo ERROR: Failed to install dependencies
        echo.
        echo Please check your internet connection and try again.
        echo.
        pause
        exit /b 1
    )
    echo.
    echo Dependencies installed successfully!
)

:: Kill existing Node processes
echo.
echo Stopping any existing servers...
taskkill /f /im node.exe >nul 2>&1
timeout /t 2 /nobreak >nul

:: Start server
echo.
echo Starting VFX Analysis Server...
echo.
echo ========================================
echo   Server Information
echo ========================================
echo   URL: http://localhost:3001
echo   Features: Drag Drop, Timewarp, Resize
echo ========================================
echo.
echo Available Pages:
echo   Main Page: http://localhost:3001
echo   Professional Analysis: http://localhost:3001/demo-stable.html
echo   XML Diagnostic: http://localhost:3001/xml-diagnostic.html
echo   UNC File Reader: http://localhost:3001/unc-reader.html
echo   API Documentation: http://localhost:3001/api-docs.html
echo.
echo Server is starting...
echo Press Ctrl+C to stop the server
echo.

:: Set port and start
set PORT=3001
node server.js

:: Cleanup when server stops
echo.
echo Server stopped.
echo.
echo Thank you for using VFX XML Analysis Tool!
echo.
pause

const { XMLParser } = require('fast-xml-parser');

class XMLSpeedDetector {
  constructor() {
    this.config = {
      speedAttributes: [
        'speed', 'velocity', 'rate', 'fps', 'framerate', 'frame_rate',
        'playback_speed', 'animation_speed', 'motion_speed', 'tempo',
        'velocity_x', 'velocity_y', 'velocity_z', 'vel', 'v'
      ],
      timeAttributes: [
        'time', 'timestamp', 't', 'frame', 'frame_number', 'timecode',
        'duration', 'start_time', 'end_time', 'offset', 'delay'
      ],
      minSpeed: -1000.0, // 允许负值，适应更多场景
      maxSpeed: 10000.0,  // 扩大范围
      speedChangeThreshold: 50.0, // 百分比
    };

    this.xmlParser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: '@_',
      textNodeName: '#text',
      parseAttributeValue: true,
      parseTagValue: true,
      trimValues: true
    });
  }

  async analyzeXML(xmlContent, fileName = 'unknown.xml') {
    const startTime = Date.now();

    try {
      // 解析XML
      const parsedXML = this.xmlParser.parse(xmlContent);
      
      // 提取速度数据点
      const speedDataPoints = this.extractSpeedDataPoints(parsedXML);
      
      // 分析速度数据
      const speedAnalysis = this.analyzeSpeedData(speedDataPoints);
      
      // 检测异常
      const anomalies = this.detectAnomalies(speedDataPoints, speedAnalysis);
      
      // 验证数据
      const validation = this.validateData(speedDataPoints);
      
      // 生成报告
      const processingTime = Date.now() - startTime;
      
      return {
        metadata: {
          fileName: fileName,
          fileSize: xmlContent.length,
          processingTime: processingTime,
          timestamp: new Date().toISOString()
        },
        summary: {
          totalDataPoints: speedDataPoints.length,
          validDataPoints: speedDataPoints.filter(p => p.speed >= 0).length,
          anomaliesCount: anomalies.length,
          validationErrors: validation.errors.length,
          validationWarnings: validation.warnings.length,
          overallStatus: this.determineOverallStatus(anomalies, validation)
        },
        speedDataPoints: speedDataPoints,
        speedAnalysis: speedAnalysis,
        anomalies: anomalies,
        validation: validation,
        recommendations: this.generateRecommendations(speedDataPoints, anomalies, validation)
      };

    } catch (error) {
      throw new Error(`XML解析失败: ${error.message}`);
    }
  }

  extractSpeedDataPoints(obj, path = '', dataPoints = []) {
    if (typeof obj !== 'object' || obj === null) {
      return dataPoints;
    }

    // 检查当前对象是否包含速度数据
    const speedData = this.extractSpeedFromObject(obj, path);
    if (speedData) {
      dataPoints.push(speedData);
    }

    // 递归处理子对象
    for (const [key, value] of Object.entries(obj)) {
      if (key.startsWith('@_')) continue; // 跳过属性标记
      
      const currentPath = path ? `${path}/${key}` : key;
      
      if (Array.isArray(value)) {
        value.forEach((item, index) => {
          this.extractSpeedDataPoints(item, `${currentPath}[${index}]`, dataPoints);
        });
      } else if (typeof value === 'object') {
        this.extractSpeedDataPoints(value, currentPath, dataPoints);
      }
    }

    return dataPoints;
  }

  extractSpeedFromObject(obj, path) {
    if (typeof obj !== 'object' || obj === null) {
      return null;
    }

    let speed = null;
    let time = null;
    const attributes = {};

    // 查找速度属性（属性形式）
    for (const speedAttr of this.config.speedAttributes) {
      const attrKey = `@_${speedAttr}`;
      if (obj[attrKey] !== undefined) {
        const speedValue = parseFloat(obj[attrKey]);
        if (!isNaN(speedValue)) {
          speed = speedValue;
          attributes[speedAttr] = speedValue;
          break;
        }
      }
    }

    // 查找速度属性（元素值形式）
    if (speed === null) {
      for (const speedAttr of this.config.speedAttributes) {
        if (obj[speedAttr] !== undefined) {
          const speedValue = parseFloat(obj[speedAttr]);
          if (!isNaN(speedValue)) {
            speed = speedValue;
            attributes[speedAttr] = speedValue;
            break;
          }
        }
      }
    }

    // 查找时间属性（属性形式）
    for (const timeAttr of this.config.timeAttributes) {
      const attrKey = `@_${timeAttr}`;
      if (obj[attrKey] !== undefined) {
        const timeValue = parseFloat(obj[attrKey]);
        if (!isNaN(timeValue)) {
          time = timeValue;
          attributes[timeAttr] = timeValue;
          break;
        }
      }
    }

    // 查找时间属性（元素值形式）
    if (time === null) {
      for (const timeAttr of this.config.timeAttributes) {
        if (obj[timeAttr] !== undefined) {
          const timeValue = parseFloat(obj[timeAttr]);
          if (!isNaN(timeValue)) {
            time = timeValue;
            attributes[timeAttr] = timeValue;
            break;
          }
        }
      }
    }

    // 收集所有属性
    for (const [key, value] of Object.entries(obj)) {
      if (key.startsWith('@_')) {
        const attrName = key.substring(2);
        attributes[attrName] = value;
      } else if (typeof value === 'string' || typeof value === 'number') {
        attributes[key] = value;
      }
    }

    if (speed !== null) {
      return {
        nodePath: path,
        speed: speed,
        time: time,
        attributes: attributes
      };
    }

    return null;
  }

  analyzeSpeedData(dataPoints) {
    if (dataPoints.length === 0) {
      return {
        minSpeed: 0,
        maxSpeed: 0,
        averageSpeed: 0,
        standardDeviation: 0,
        speedChanges: []
      };
    }

    const speeds = dataPoints.map(p => p.speed);
    const minSpeed = Math.min(...speeds);
    const maxSpeed = Math.max(...speeds);
    const averageSpeed = speeds.reduce((sum, speed) => sum + speed, 0) / speeds.length;

    // 计算标准差
    const variance = speeds.reduce((sum, speed) => sum + Math.pow(speed - averageSpeed, 2), 0) / speeds.length;
    const standardDeviation = Math.sqrt(variance);

    // 检测速度变化
    const speedChanges = this.detectSpeedChanges(dataPoints);

    return {
      minSpeed,
      maxSpeed,
      averageSpeed,
      standardDeviation,
      speedChanges
    };
  }

  detectSpeedChanges(dataPoints) {
    const changes = [];

    for (let i = 1; i < dataPoints.length; i++) {
      const prevSpeed = dataPoints[i - 1].speed;
      const currSpeed = dataPoints[i].speed;

      if (prevSpeed === 0) continue; // 避免除零

      const changePercentage = Math.abs((currSpeed - prevSpeed) / prevSpeed) * 100;

      if (changePercentage >= this.config.speedChangeThreshold) {
        const changeType = currSpeed > prevSpeed 
          ? (changePercentage > 100 ? 'sudden_acceleration' : 'acceleration')
          : (changePercentage > 100 ? 'sudden_deceleration' : 'deceleration');

        changes.push({
          fromIndex: i - 1,
          toIndex: i,
          fromSpeed: prevSpeed,
          toSpeed: currSpeed,
          changePercentage: changePercentage,
          changeType: changeType
        });
      }
    }

    return changes;
  }

  detectAnomalies(dataPoints, speedAnalysis) {
    const anomalies = [];

    dataPoints.forEach((dataPoint, index) => {
      // 范围异常
      if (dataPoint.speed < this.config.minSpeed || dataPoint.speed > this.config.maxSpeed) {
        anomalies.push({
          dataPointIndex: index,
          type: 'out_of_range',
          description: `速度 ${dataPoint.speed} 超出有效范围 [${this.config.minSpeed}, ${this.config.maxSpeed}]`,
          severity: 'high',
          nodePath: dataPoint.nodePath
        });
      }

      // 统计异常（超过3个标准差）
      if (Math.abs(dataPoint.speed - speedAnalysis.averageSpeed) > 3 * speedAnalysis.standardDeviation) {
        anomalies.push({
          dataPointIndex: index,
          type: 'statistical_outlier',
          description: `速度 ${dataPoint.speed} 偏离平均值 ${speedAnalysis.averageSpeed.toFixed(2)} 超过3个标准差`,
          severity: 'medium',
          nodePath: dataPoint.nodePath
        });
      }

      // 负速度异常
      if (dataPoint.speed < 0) {
        anomalies.push({
          dataPointIndex: index,
          type: 'negative_speed',
          description: `检测到负速度值: ${dataPoint.speed}`,
          severity: 'high',
          nodePath: dataPoint.nodePath
        });
      }
    });

    return anomalies;
  }

  validateData(dataPoints) {
    const errors = [];
    const warnings = [];

    // 检查是否有速度数据
    if (dataPoints.length === 0) {
      errors.push({
        type: 'no_speed_data',
        description: '未找到速度数据',
        suggestion: '请确保XML文件包含speed、velocity或rate属性'
      });
    }

    // 检查时间序列一致性
    const timePoints = dataPoints
      .filter(p => p.time !== null)
      .map((p, i) => ({ index: i, time: p.time }))
      .sort((a, b) => a.time - b.time);

    for (let i = 1; i < timePoints.length; i++) {
      if (timePoints[i].time <= timePoints[i - 1].time) {
        warnings.push({
          type: 'time_sequence_issue',
          description: '时间序列不是单调递增的',
          suggestion: '检查时间戳的正确性'
        });
        break;
      }
    }

    // 检查缺失时间数据
    const missingTimeCount = dataPoints.filter(p => p.time === null).length;
    if (missingTimeCount > 0) {
      warnings.push({
        type: 'missing_time_data',
        description: `${missingTimeCount} 个数据点缺少时间信息`,
        suggestion: '建议为所有数据点添加时间戳'
      });
    }

    return { errors, warnings };
  }

  determineOverallStatus(anomalies, validation) {
    if (validation.errors.length > 0) {
      return 'error';
    }
    if (anomalies.some(a => a.severity === 'high')) {
      return 'fail';
    }
    if (anomalies.length > 0 || validation.warnings.length > 0) {
      return 'warning';
    }
    return 'pass';
  }

  generateRecommendations(dataPoints, anomalies, validation) {
    const recommendations = [];

    if (anomalies.length > 0) {
      recommendations.push({
        type: 'data_quality',
        title: '检查数据异常',
        description: `发现 ${anomalies.length} 个数据异常，建议检查数据来源和采集过程`,
        priority: 'high'
      });
    }

    if (validation.errors.length > 0) {
      recommendations.push({
        type: 'data_validation',
        title: '修复数据验证错误',
        description: `发现 ${validation.errors.length} 个验证错误，需要修复以确保数据质量`,
        priority: 'critical'
      });
    }

    if (dataPoints.length > 10000) {
      recommendations.push({
        type: 'performance',
        title: '考虑数据分批处理',
        description: '数据点数量较大，建议考虑分批处理以提高性能',
        priority: 'medium'
      });
    }

    return recommendations;
  }
}

// 导出单例实例
const detector = new XMLSpeedDetector();

module.exports = {
  analyzeXML: (xmlContent, fileName) => detector.analyzeXML(xmlContent, fileName)
};

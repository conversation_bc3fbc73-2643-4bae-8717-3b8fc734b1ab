const { XMLParser } = require('fast-xml-parser');

class VFXDataDetector {
  constructor() {
    this.config = {
      // VFX相关的数值属性
      vfxAttributes: [
        // 动画和时间相关
        'speed', 'velocity', 'rate', 'fps', 'framerate', 'frame_rate',
        'playback_speed', 'animation_speed', 'motion_speed', 'tempo',
        'velocity_x', 'velocity_y', 'velocity_z', 'vel', 'v',

        // 变换和位置相关
        'x', 'y', 'z', 'position_x', 'position_y', 'position_z',
        'rotation_x', 'rotation_y', 'rotation_z', 'scale_x', 'scale_y', 'scale_z',
        'translate_x', 'translate_y', 'translate_z',

        // 材质和渲染相关
        'opacity', 'alpha', 'transparency', 'intensity', 'brightness',
        'contrast', 'saturation', 'hue', 'gamma', 'exposure',

        // 物理和动力学相关
        'mass', 'density', 'friction', 'bounce', 'damping', 'force',
        'acceleration', 'gravity', 'wind_speed', 'turbulence',

        // 粒子系统相关
        'particle_count', 'emission_rate', 'life_span', 'size', 'birth_rate',
        'death_rate', 'spread', 'direction', 'flow_rate',

        // 摄像机相关
        'focal_length', 'aperture', 'focus_distance', 'zoom', 'fov',
        'near_clip', 'far_clip', 'depth_of_field',

        // 光照相关
        'light_intensity', 'shadow_softness', 'falloff', 'cone_angle',
        'beam_angle', 'color_temperature', 'lux', 'candela'
      ],
      timeAttributes: [
        'time', 'timestamp', 't', 'frame', 'frame_number', 'timecode',
        'duration', 'start_time', 'end_time', 'offset', 'delay',
        'keyframe', 'key_time', 'in_time', 'out_time', 'hold_time'
      ],
      // 变速相关属性
      timewarpAttributes: [
        'timewarp', 'time_warp', 'speed', 'playback_speed', 'rate', 'tempo',
        'time_scale', 'time_stretch', 'speed_factor', 'velocity_factor',
        'frame_rate', 'fps', 'playback_rate', 'time_remap', 'time_remapping',
        'speed_ramp', 'velocity_curve', 'time_curve', 'speed_change'
      ],
      // 缩放相关属性
      resizeAttributes: [
        'scale', 'scale_x', 'scale_y', 'scale_z', 'size', 'width', 'height',
        'resize', 'zoom', 'magnification', 'scale_factor', 'size_factor',
        'transform_scale', 'uniform_scale', 'non_uniform_scale'
      ],
      minValue: -100000.0, // VFX数值范围更大
      maxValue: 100000.0,
      changeThreshold: 30.0, // VFX变化阈值更敏感
    };

    this.xmlParser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: '@_',
      textNodeName: '#text',
      parseAttributeValue: true,
      parseTagValue: true,
      trimValues: true
    });
  }

  async analyzeXML(xmlContent, fileName = 'unknown.xml') {
    const startTime = Date.now();

    try {
      // 解析XML
      const parsedXML = this.xmlParser.parse(xmlContent);

      // 提取VFX数据点
      const vfxDataPoints = this.extractVFXDataPoints(parsedXML);

      // 分析VFX数据
      const vfxAnalysis = this.analyzeVFXData(vfxDataPoints);

      // 检测异常
      const anomalies = this.detectAnomalies(vfxDataPoints, vfxAnalysis);

      // 验证数据
      const validation = this.validateData(vfxDataPoints);

      // 分析文件类型和结构
      const fileAnalysis = this.analyzeFileStructure(parsedXML, fileName);

      // 检测变速信息
      const timewarpAnalysis = this.analyzeTimewarp(parsedXML, vfxDataPoints);

      // 检测缩放信息
      const resizeAnalysis = this.analyzeResize(parsedXML, vfxDataPoints);

      // 生成报告
      const processingTime = Date.now() - startTime;

      return {
        metadata: {
          fileName: fileName,
          fileSize: xmlContent.length,
          processingTime: processingTime,
          timestamp: new Date().toISOString(),
          fileType: fileAnalysis.fileType,
          vfxSoftware: fileAnalysis.detectedSoftware
        },
        summary: {
          totalDataPoints: vfxDataPoints.length,
          validDataPoints: vfxDataPoints.filter(p => this.isValidValue(p.value)).length,
          anomaliesCount: anomalies.length,
          validationErrors: validation.errors.length,
          validationWarnings: validation.warnings.length,
          overallStatus: this.determineOverallStatus(anomalies, validation, vfxDataPoints),
          dataCategories: this.categorizeData(vfxDataPoints)
        },
        vfxDataPoints: vfxDataPoints,
        vfxAnalysis: vfxAnalysis,
        fileAnalysis: fileAnalysis,
        timewarpAnalysis: timewarpAnalysis,
        resizeAnalysis: resizeAnalysis,
        anomalies: anomalies,
        validation: validation,
        recommendations: this.generateRecommendations(vfxDataPoints, anomalies, validation, fileAnalysis, timewarpAnalysis, resizeAnalysis)
      };

    } catch (error) {
      throw new Error(`XML解析失败: ${error.message}`);
    }
  }

  extractVFXDataPoints(obj, path = '', dataPoints = []) {
    if (typeof obj !== 'object' || obj === null) {
      return dataPoints;
    }

    // 检查当前对象是否包含VFX数据
    const vfxData = this.extractVFXFromObject(obj, path);
    if (vfxData) {
      dataPoints.push(...vfxData); // 展开数组
    }

    // 递归处理子对象
    for (const [key, value] of Object.entries(obj)) {
      if (key.startsWith('@_')) continue; // 跳过属性标记

      const currentPath = path ? `${path}/${key}` : key;

      if (Array.isArray(value)) {
        value.forEach((item, index) => {
          this.extractVFXDataPoints(item, `${currentPath}[${index}]`, dataPoints);
        });
      } else if (typeof value === 'object') {
        this.extractVFXDataPoints(value, currentPath, dataPoints);
      }
    }

    return dataPoints;
  }

  extractVFXFromObject(obj, path) {
    if (typeof obj !== 'object' || obj === null) {
      return null;
    }

    let foundData = [];
    let time = null;
    const attributes = {};

    // 查找VFX相关属性（属性形式）
    for (const vfxAttr of this.config.vfxAttributes) {
      const attrKey = `@_${vfxAttr}`;
      if (obj[attrKey] !== undefined) {
        const value = parseFloat(obj[attrKey]);
        if (!isNaN(value)) {
          foundData.push({
            attribute: vfxAttr,
            value: value,
            type: this.categorizeAttribute(vfxAttr),
            source: 'attribute'
          });
          attributes[vfxAttr] = value;
        }
      }
    }

    // 查找VFX相关属性（元素值形式）
    for (const vfxAttr of this.config.vfxAttributes) {
      if (obj[vfxAttr] !== undefined) {
        const value = parseFloat(obj[vfxAttr]);
        if (!isNaN(value)) {
          foundData.push({
            attribute: vfxAttr,
            value: value,
            type: this.categorizeAttribute(vfxAttr),
            source: 'element'
          });
          attributes[vfxAttr] = value;
        }
      }
    }

    // 查找时间属性
    for (const timeAttr of this.config.timeAttributes) {
      const attrKey = `@_${timeAttr}`;
      if (obj[attrKey] !== undefined) {
        const timeValue = parseFloat(obj[attrKey]);
        if (!isNaN(timeValue)) {
          time = timeValue;
          attributes[timeAttr] = timeValue;
          break;
        }
      }
    }

    if (time === null) {
      for (const timeAttr of this.config.timeAttributes) {
        if (obj[timeAttr] !== undefined) {
          const timeValue = parseFloat(obj[timeAttr]);
          if (!isNaN(timeValue)) {
            time = timeValue;
            attributes[timeAttr] = timeValue;
            break;
          }
        }
      }
    }

    // 收集所有属性
    for (const [key, value] of Object.entries(obj)) {
      if (key.startsWith('@_')) {
        const attrName = key.substring(2);
        attributes[attrName] = value;
      } else if (typeof value === 'string' || typeof value === 'number') {
        attributes[key] = value;
      }
    }

    // 如果找到任何VFX数据，返回数据点
    if (foundData.length > 0) {
      return foundData.map(data => ({
        nodePath: path,
        attribute: data.attribute,
        value: data.value,
        type: data.type,
        source: data.source,
        time: time,
        allAttributes: attributes
      }));
    }

    return null;
  }

  categorizeAttribute(attribute) {
    const categories = {
      animation: ['speed', 'velocity', 'rate', 'fps', 'framerate', 'frame_rate', 'playback_speed', 'animation_speed', 'motion_speed', 'tempo'],
      transform: ['x', 'y', 'z', 'position_x', 'position_y', 'position_z', 'rotation_x', 'rotation_y', 'rotation_z', 'scale_x', 'scale_y', 'scale_z', 'translate_x', 'translate_y', 'translate_z'],
      material: ['opacity', 'alpha', 'transparency', 'intensity', 'brightness', 'contrast', 'saturation', 'hue', 'gamma', 'exposure'],
      physics: ['mass', 'density', 'friction', 'bounce', 'damping', 'force', 'acceleration', 'gravity', 'wind_speed', 'turbulence'],
      particle: ['particle_count', 'emission_rate', 'life_span', 'size', 'birth_rate', 'death_rate', 'spread', 'direction', 'flow_rate'],
      camera: ['focal_length', 'aperture', 'focus_distance', 'zoom', 'fov', 'near_clip', 'far_clip', 'depth_of_field'],
      lighting: ['light_intensity', 'shadow_softness', 'falloff', 'cone_angle', 'beam_angle', 'color_temperature', 'lux', 'candela']
    };

    for (const [category, attrs] of Object.entries(categories)) {
      if (attrs.includes(attribute.toLowerCase())) {
        return category;
      }
    }
    return 'other';
  }

  isValidValue(value) {
    return !isNaN(value) && isFinite(value) && value >= this.config.minValue && value <= this.config.maxValue;
  }

  categorizeData(dataPoints) {
    const categories = {};
    dataPoints.forEach(point => {
      if (!categories[point.type]) {
        categories[point.type] = 0;
      }
      categories[point.type]++;
    });
    return categories;
  }

  analyzeFileStructure(parsedXML, fileName) {
    const rootElement = Object.keys(parsedXML)[0];
    const fileType = this.detectFileType(rootElement, fileName);
    const detectedSoftware = this.detectVFXSoftware(parsedXML, fileName);

    return {
      rootElement: rootElement,
      fileType: fileType,
      detectedSoftware: detectedSoftware,
      elementCount: this.countElements(parsedXML),
      hasAnimationData: this.hasAnimationData(parsedXML),
      hasTransformData: this.hasTransformData(parsedXML)
    };
  }

  detectFileType(rootElement, fileName) {
    const typeIndicators = {
      'scene': ['scene', 'project', 'composition'],
      'animation': ['animation', 'keyframe', 'timeline'],
      'model': ['mesh', 'geometry', 'model', 'object'],
      'material': ['material', 'shader', 'texture'],
      'camera': ['camera', 'view', 'viewport'],
      'lighting': ['light', 'illumination', 'shadow'],
      'particle': ['particle', 'emitter', 'system'],
      'effect': ['effect', 'filter', 'post']
    };

    const lowerRoot = rootElement.toLowerCase();
    const lowerFileName = fileName.toLowerCase();

    for (const [type, indicators] of Object.entries(typeIndicators)) {
      if (indicators.some(indicator =>
        lowerRoot.includes(indicator) || lowerFileName.includes(indicator)
      )) {
        return type;
      }
    }

    return 'unknown';
  }

  detectVFXSoftware(parsedXML, fileName) {
    const softwareIndicators = {
      'Maya': ['maya', 'autodesk', 'mel'],
      'Blender': ['blender', 'blend'],
      'Cinema 4D': ['cinema4d', 'c4d', 'maxon'],
      'After Effects': ['aftereffects', 'aep', 'adobe'],
      'Nuke': ['nuke', 'foundry'],
      'Houdini': ['houdini', 'sidefx', 'hip'],
      '3ds Max': ['3dsmax', 'max', 'autodesk'],
      'Unity': ['unity', 'unityengine'],
      'Unreal': ['unreal', 'ue4', 'ue5']
    };

    const xmlString = JSON.stringify(parsedXML).toLowerCase();
    const lowerFileName = fileName.toLowerCase();

    for (const [software, indicators] of Object.entries(softwareIndicators)) {
      if (indicators.some(indicator =>
        xmlString.includes(indicator) || lowerFileName.includes(indicator)
      )) {
        return software;
      }
    }

    return 'Unknown';
  }

  countElements(obj) {
    let count = 0;
    if (typeof obj === 'object' && obj !== null) {
      count++;
      for (const value of Object.values(obj)) {
        if (Array.isArray(value)) {
          value.forEach(item => count += this.countElements(item));
        } else if (typeof value === 'object') {
          count += this.countElements(value);
        }
      }
    }
    return count;
  }

  hasAnimationData(obj) {
    const animationKeywords = ['keyframe', 'animation', 'timeline', 'curve', 'interpolation'];
    const objString = JSON.stringify(obj).toLowerCase();
    return animationKeywords.some(keyword => objString.includes(keyword));
  }

  hasTransformData(obj) {
    const transformKeywords = ['transform', 'position', 'rotation', 'scale', 'translate'];
    const objString = JSON.stringify(obj).toLowerCase();
    return transformKeywords.some(keyword => objString.includes(keyword));
  }

  analyzeVFXData(dataPoints) {
    if (dataPoints.length === 0) {
      return {
        minValue: 0,
        maxValue: 0,
        averageValue: 0,
        standardDeviation: 0,
        valueChanges: [],
        categoryStats: {}
      };
    }

    const values = dataPoints.map(p => p.value);
    const minValue = Math.min(...values);
    const maxValue = Math.max(...values);
    const averageValue = values.reduce((sum, val) => sum + val, 0) / values.length;

    // 计算标准差
    const variance = values.reduce((sum, val) => sum + Math.pow(val - averageValue, 2), 0) / values.length;
    const standardDeviation = Math.sqrt(variance);

    // 按类型统计
    const categoryStats = {};
    dataPoints.forEach(point => {
      if (!categoryStats[point.type]) {
        categoryStats[point.type] = {
          count: 0,
          values: [],
          attributes: new Set()
        };
      }
      categoryStats[point.type].count++;
      categoryStats[point.type].values.push(point.value);
      categoryStats[point.type].attributes.add(point.attribute);
    });

    // 计算每个类型的统计信息
    for (const [type, stats] of Object.entries(categoryStats)) {
      const typeValues = stats.values;
      stats.min = Math.min(...typeValues);
      stats.max = Math.max(...typeValues);
      stats.average = typeValues.reduce((sum, val) => sum + val, 0) / typeValues.length;
      stats.attributes = Array.from(stats.attributes);
    }

    // 检测数值变化
    const valueChanges = this.detectValueChanges(dataPoints);

    return {
      minValue,
      maxValue,
      averageValue,
      standardDeviation,
      valueChanges,
      categoryStats
    };
  }

  detectValueChanges(dataPoints) {
    const changes = [];

    // 按时间排序的数据点
    const timeOrderedPoints = dataPoints
      .filter(p => p.time !== null)
      .sort((a, b) => a.time - b.time);

    for (let i = 1; i < timeOrderedPoints.length; i++) {
      const prevPoint = timeOrderedPoints[i - 1];
      const currPoint = timeOrderedPoints[i];

      if (prevPoint.attribute === currPoint.attribute && prevPoint.value !== 0) {
        const changePercentage = Math.abs((currPoint.value - prevPoint.value) / prevPoint.value) * 100;

        if (changePercentage >= this.config.changeThreshold) {
          changes.push({
            attribute: currPoint.attribute,
            type: currPoint.type,
            fromValue: prevPoint.value,
            toValue: currPoint.value,
            fromTime: prevPoint.time,
            toTime: currPoint.time,
            changePercentage: changePercentage,
            changeType: currPoint.value > prevPoint.value ? 'increase' : 'decrease'
          });
        }
      }
    }

    return changes;
  }

  detectAnomalies(dataPoints, vfxAnalysis) {
    const anomalies = [];

    dataPoints.forEach((dataPoint, index) => {
      // 范围异常
      if (dataPoint.value < this.config.minValue || dataPoint.value > this.config.maxValue) {
        anomalies.push({
          dataPointIndex: index,
          type: 'out_of_range',
          description: `${dataPoint.attribute} 值 ${dataPoint.value} 超出有效范围 [${this.config.minValue}, ${this.config.maxValue}]`,
          severity: 'high',
          nodePath: dataPoint.nodePath,
          attribute: dataPoint.attribute,
          category: dataPoint.type
        });
      }

      // 统计异常（超过3个标准差）
      if (vfxAnalysis.standardDeviation > 0) {
        const deviation = Math.abs(dataPoint.value - vfxAnalysis.averageValue);
        if (deviation > 3 * vfxAnalysis.standardDeviation) {
          anomalies.push({
            dataPointIndex: index,
            type: 'statistical_outlier',
            description: `${dataPoint.attribute} 值 ${dataPoint.value} 偏离平均值 ${vfxAnalysis.averageValue.toFixed(2)} 超过3个标准差`,
            severity: 'medium',
            nodePath: dataPoint.nodePath,
            attribute: dataPoint.attribute,
            category: dataPoint.type
          });
        }
      }

      // 特殊值检查
      if (!isFinite(dataPoint.value)) {
        anomalies.push({
          dataPointIndex: index,
          type: 'invalid_value',
          description: `${dataPoint.attribute} 包含无效值: ${dataPoint.value}`,
          severity: 'high',
          nodePath: dataPoint.nodePath,
          attribute: dataPoint.attribute,
          category: dataPoint.type
        });
      }
    });

    return anomalies;
  }

  validateData(dataPoints) {
    const errors = [];
    const warnings = [];

    // 检查是否有VFX数据
    if (dataPoints.length === 0) {
      warnings.push({
        type: 'no_vfx_data',
        description: '未找到VFX相关数据',
        suggestion: '这可能是一个不包含数值属性的XML文件，或者属性名称不在支持列表中'
      });
    }

    // 检查数据质量
    const validDataCount = dataPoints.filter(p => this.isValidValue(p.value)).length;
    if (validDataCount < dataPoints.length * 0.8) {
      warnings.push({
        type: 'low_data_quality',
        description: `只有 ${validDataCount}/${dataPoints.length} 个数据点有效`,
        suggestion: '检查数据源的完整性'
      });
    }

    // 检查时间序列一致性
    const timePoints = dataPoints
      .filter(p => p.time !== null)
      .map((p, i) => ({ index: i, time: p.time }))
      .sort((a, b) => a.time - b.time);

    if (timePoints.length > 1) {
      for (let i = 1; i < timePoints.length; i++) {
        if (timePoints[i].time <= timePoints[i - 1].time) {
          warnings.push({
            type: 'time_sequence_issue',
            description: '时间序列不是单调递增的',
            suggestion: '检查时间戳的正确性'
          });
          break;
        }
      }
    }

    // 检查缺失时间数据
    const missingTimeCount = dataPoints.filter(p => p.time === null).length;
    if (missingTimeCount > 0 && missingTimeCount < dataPoints.length) {
      warnings.push({
        type: 'missing_time_data',
        description: `${missingTimeCount} 个数据点缺少时间信息`,
        suggestion: '建议为所有数据点添加时间戳以支持时间序列分析'
      });
    }

    // 检查数据类型分布
    const categories = this.categorizeData(dataPoints);
    if (Object.keys(categories).length === 1 && categories['other'] > 0) {
      warnings.push({
        type: 'unknown_attributes',
        description: '所有属性都未被识别为标准VFX属性',
        suggestion: '可能需要扩展属性识别列表'
      });
    }

    return { errors, warnings };
  }

  determineOverallStatus(anomalies, validation, dataPoints) {
    if (validation.errors.length > 0) {
      return 'error';
    }
    if (dataPoints.length === 0) {
      return 'info'; // 没有数据不算错误，只是信息
    }
    if (anomalies.some(a => a.severity === 'high')) {
      return 'warning'; // 降低严重性，VFX数据可能有特殊情况
    }
    if (anomalies.length > 0 || validation.warnings.length > 0) {
      return 'warning';
    }
    return 'pass';
  }

  // 分析变速信息
  analyzeTimewarp(parsedXML, dataPoints) {
    const timewarpData = {
      hasTimewarp: false,
      timewarpType: 'none', // 'none', 'uniform', 'segmented', 'curve'
      segments: [],
      overallSpeedChange: 0,
      speedRange: { min: 1.0, max: 1.0 },
      details: []
    };

    // 查找变速相关的数据点
    const timewarpPoints = dataPoints.filter(point =>
      this.config.timewarpAttributes.some(attr =>
        point.attribute.toLowerCase().includes(attr.toLowerCase())
      )
    );

    if (timewarpPoints.length === 0) {
      // 深度搜索XML结构中的变速信息
      const timewarpElements = this.findTimewarpElements(parsedXML);
      if (timewarpElements.length > 0) {
        timewarpData.hasTimewarp = true;
        timewarpData.details = timewarpElements;
        timewarpData.timewarpType = this.determineTimewarpType(timewarpElements);
      }
      return timewarpData;
    }

    timewarpData.hasTimewarp = true;

    // 按时间排序分析变速段
    const timeOrderedPoints = timewarpPoints
      .filter(p => p.time !== null)
      .sort((a, b) => a.time - b.time);

    if (timeOrderedPoints.length > 0) {
      const speeds = timeOrderedPoints.map(p => p.value);
      timewarpData.speedRange.min = Math.min(...speeds);
      timewarpData.speedRange.max = Math.max(...speeds);

      // 计算整体速度变化
      if (speeds.length > 1) {
        const firstSpeed = speeds[0];
        const lastSpeed = speeds[speeds.length - 1];
        timewarpData.overallSpeedChange = ((lastSpeed - firstSpeed) / firstSpeed) * 100;
      }

      // 分析变速段
      timewarpData.segments = this.analyzeTimewarpSegments(timeOrderedPoints);
      timewarpData.timewarpType = this.determineTimewarpType(timeOrderedPoints);
    }

    // 添加详细信息
    timewarpData.details = timewarpPoints.map(point => ({
      attribute: point.attribute,
      value: point.value,
      time: point.time,
      nodePath: point.nodePath,
      speedDescription: this.describeSpeed(point.value)
    }));

    return timewarpData;
  }

  // 分析缩放信息
  analyzeResize(parsedXML, dataPoints) {
    const resizeData = {
      hasResize: false,
      resizeType: 'none', // 'none', 'uniform', 'non_uniform', 'animated'
      scaleFactors: { x: 1.0, y: 1.0, z: 1.0 },
      overallScale: 1.0,
      scaleChange: 0,
      isEnlarged: false,
      isReduced: false,
      details: []
    };

    // 查找缩放相关的数据点
    const resizePoints = dataPoints.filter(point =>
      this.config.resizeAttributes.some(attr =>
        point.attribute.toLowerCase().includes(attr.toLowerCase())
      )
    );

    if (resizePoints.length === 0) {
      // 深度搜索XML结构中的缩放信息
      const resizeElements = this.findResizeElements(parsedXML);
      if (resizeElements.length > 0) {
        resizeData.hasResize = true;
        resizeData.details = resizeElements;
        resizeData.resizeType = this.determineResizeType(resizeElements);
      }
      return resizeData;
    }

    resizeData.hasResize = true;

    // 分析缩放因子
    const scaleX = resizePoints.filter(p => p.attribute.toLowerCase().includes('scale_x') || p.attribute.toLowerCase().includes('x'));
    const scaleY = resizePoints.filter(p => p.attribute.toLowerCase().includes('scale_y') || p.attribute.toLowerCase().includes('y'));
    const scaleZ = resizePoints.filter(p => p.attribute.toLowerCase().includes('scale_z') || p.attribute.toLowerCase().includes('z'));
    const uniformScale = resizePoints.filter(p => p.attribute.toLowerCase() === 'scale' || p.attribute.toLowerCase() === 'size');

    if (scaleX.length > 0) resizeData.scaleFactors.x = scaleX[scaleX.length - 1].value;
    if (scaleY.length > 0) resizeData.scaleFactors.y = scaleY[scaleY.length - 1].value;
    if (scaleZ.length > 0) resizeData.scaleFactors.z = scaleZ[scaleZ.length - 1].value;

    // 如果有统一缩放，使用统一缩放值
    if (uniformScale.length > 0) {
      const uniformValue = uniformScale[uniformScale.length - 1].value;
      resizeData.scaleFactors.x = uniformValue;
      resizeData.scaleFactors.y = uniformValue;
      resizeData.scaleFactors.z = uniformValue;
      resizeData.overallScale = uniformValue;
    } else {
      // 计算整体缩放（几何平均）
      resizeData.overallScale = Math.cbrt(
        resizeData.scaleFactors.x *
        resizeData.scaleFactors.y *
        resizeData.scaleFactors.z
      );
    }

    // 判断放大还是缩小
    resizeData.scaleChange = ((resizeData.overallScale - 1.0) * 100);
    resizeData.isEnlarged = resizeData.overallScale > 1.0;
    resizeData.isReduced = resizeData.overallScale < 1.0;

    // 确定缩放类型
    resizeData.resizeType = this.determineResizeType(resizePoints);

    // 添加详细信息
    resizeData.details = resizePoints.map(point => ({
      attribute: point.attribute,
      value: point.value,
      time: point.time,
      nodePath: point.nodePath,
      scaleDescription: this.describeScale(point.value)
    }));

    return resizeData;
  }

  // 辅助方法：查找变速元素
  findTimewarpElements(obj, path = '') {
    const elements = [];
    if (typeof obj !== 'object' || obj === null) return elements;

    for (const [key, value] of Object.entries(obj)) {
      const currentPath = path ? `${path}/${key}` : key;

      // 检查元素名是否包含变速关键词
      if (this.config.timewarpAttributes.some(attr =>
          key.toLowerCase().includes(attr.toLowerCase()))) {
        elements.push({
          path: currentPath,
          element: key,
          value: value,
          type: 'element_name'
        });
      }

      // 递归搜索
      if (typeof value === 'object') {
        elements.push(...this.findTimewarpElements(value, currentPath));
      }
    }

    return elements;
  }

  // 辅助方法：查找缩放元素
  findResizeElements(obj, path = '') {
    const elements = [];
    if (typeof obj !== 'object' || obj === null) return elements;

    for (const [key, value] of Object.entries(obj)) {
      const currentPath = path ? `${path}/${key}` : key;

      // 检查元素名是否包含缩放关键词
      if (this.config.resizeAttributes.some(attr =>
          key.toLowerCase().includes(attr.toLowerCase()))) {
        elements.push({
          path: currentPath,
          element: key,
          value: value,
          type: 'element_name'
        });
      }

      // 递归搜索
      if (typeof value === 'object') {
        elements.push(...this.findResizeElements(value, currentPath));
      }
    }

    return elements;
  }

  // 分析变速段
  analyzeTimewarpSegments(timeOrderedPoints) {
    const segments = [];

    for (let i = 1; i < timeOrderedPoints.length; i++) {
      const prevPoint = timeOrderedPoints[i - 1];
      const currPoint = timeOrderedPoints[i];

      const speedChange = currPoint.value - prevPoint.value;
      const timeSpan = currPoint.time - prevPoint.time;

      if (Math.abs(speedChange) > 0.01) { // 有明显速度变化
        segments.push({
          startTime: prevPoint.time,
          endTime: currPoint.time,
          startSpeed: prevPoint.value,
          endSpeed: currPoint.value,
          speedChange: speedChange,
          changePercentage: (speedChange / prevPoint.value) * 100,
          duration: timeSpan,
          type: speedChange > 0 ? 'acceleration' : 'deceleration'
        });
      }
    }

    return segments;
  }

  // 确定变速类型
  determineTimewarpType(points) {
    if (!Array.isArray(points) || points.length === 0) return 'none';

    if (points.length === 1) return 'uniform';

    const speeds = points.map(p => typeof p.value === 'number' ? p.value : parseFloat(p.value) || 1.0);
    const uniqueSpeeds = [...new Set(speeds)];

    if (uniqueSpeeds.length === 1) return 'uniform';
    if (uniqueSpeeds.length <= 3) return 'segmented';
    return 'curve';
  }

  // 确定缩放类型
  determineResizeType(points) {
    if (!Array.isArray(points) || points.length === 0) return 'none';

    const hasX = points.some(p => p.attribute.toLowerCase().includes('x'));
    const hasY = points.some(p => p.attribute.toLowerCase().includes('y'));
    const hasZ = points.some(p => p.attribute.toLowerCase().includes('z'));
    const hasUniform = points.some(p => p.attribute.toLowerCase() === 'scale' || p.attribute.toLowerCase() === 'size');

    if (hasUniform) return 'uniform';
    if ((hasX && hasY && hasZ) || (hasX && hasY)) return 'non_uniform';
    if (points.some(p => p.time !== null)) return 'animated';
    return 'uniform';
  }

  // 描述速度
  describeSpeed(speed) {
    if (speed === 1.0) return '正常速度';
    if (speed > 1.0) return `${speed}倍速 (加速 ${((speed - 1) * 100).toFixed(1)}%)`;
    if (speed < 1.0 && speed > 0) return `${speed}倍速 (减速 ${((1 - speed) * 100).toFixed(1)}%)`;
    if (speed === 0) return '暂停';
    if (speed < 0) return `${Math.abs(speed)}倍倒放`;
    return `${speed}倍速`;
  }

  // 描述缩放
  describeScale(scale) {
    if (scale === 1.0) return '原始大小';
    if (scale > 1.0) return `放大 ${((scale - 1) * 100).toFixed(1)}% (${scale}倍)`;
    if (scale < 1.0 && scale > 0) return `缩小 ${((1 - scale) * 100).toFixed(1)}% (${scale}倍)`;
    if (scale === 0) return '隐藏 (缩放为0)';
    return `缩放 ${scale}倍`;
  }

  generateRecommendations(dataPoints, anomalies, validation, fileAnalysis, timewarpAnalysis, resizeAnalysis) {
    const recommendations = [];

    if (dataPoints.length === 0) {
      recommendations.push({
        type: 'file_analysis',
        title: 'VFX文件分析',
        description: `检测到这是一个${fileAnalysis.fileType}类型的${fileAnalysis.detectedSoftware}文件，可能不包含数值属性`,
        priority: 'info'
      });
    }

    if (anomalies.length > 0) {
      const highSeverityCount = anomalies.filter(a => a.severity === 'high').length;
      if (highSeverityCount > 0) {
        recommendations.push({
          type: 'data_quality',
          title: '检查数据异常',
          description: `发现 ${highSeverityCount} 个高优先级异常，建议检查数据范围和有效性`,
          priority: 'high'
        });
      }
    }

    if (validation.warnings.length > 0) {
      recommendations.push({
        type: 'data_improvement',
        title: '数据质量改进',
        description: `发现 ${validation.warnings.length} 个改进建议，可以提升数据分析质量`,
        priority: 'medium'
      });
    }

    if (dataPoints.length > 0) {
      const categories = this.categorizeData(dataPoints);
      const categoryCount = Object.keys(categories).length;

      recommendations.push({
        type: 'data_summary',
        title: 'VFX数据概览',
        description: `成功提取 ${dataPoints.length} 个数据点，涵盖 ${categoryCount} 个类别：${Object.keys(categories).join(', ')}`,
        priority: 'info'
      });
    }

    if (fileAnalysis.hasAnimationData) {
      recommendations.push({
        type: 'animation_analysis',
        title: '动画数据检测',
        description: '文件包含动画相关数据，建议进行时间序列分析',
        priority: 'medium'
      });
    }

    // 变速分析建议
    if (timewarpAnalysis && timewarpAnalysis.hasTimewarp) {
      const speedDesc = timewarpAnalysis.timewarpType === 'uniform' ? '整段变速' : '分段变速';
      const speedRange = `${timewarpAnalysis.speedRange.min}x - ${timewarpAnalysis.speedRange.max}x`;

      recommendations.push({
        type: 'timewarp_analysis',
        title: '变速效果检测',
        description: `检测到${speedDesc}，速度范围：${speedRange}，整体变化：${timewarpAnalysis.overallSpeedChange.toFixed(1)}%`,
        priority: 'high'
      });

      if (timewarpAnalysis.segments.length > 0) {
        recommendations.push({
          type: 'timewarp_segments',
          title: '变速段分析',
          description: `发现 ${timewarpAnalysis.segments.length} 个变速段，建议检查变速曲线的平滑性`,
          priority: 'medium'
        });
      }
    }

    // 缩放分析建议
    if (resizeAnalysis && resizeAnalysis.hasResize) {
      const resizeDesc = resizeAnalysis.isEnlarged ? '放大' : resizeAnalysis.isReduced ? '缩小' : '无变化';
      const scaleInfo = `${resizeDesc} ${Math.abs(resizeAnalysis.scaleChange).toFixed(1)}%`;

      recommendations.push({
        type: 'resize_analysis',
        title: '缩放效果检测',
        description: `检测到${resizeAnalysis.resizeType}缩放，${scaleInfo}，整体缩放：${resizeAnalysis.overallScale.toFixed(2)}倍`,
        priority: 'high'
      });

      if (resizeAnalysis.resizeType === 'non_uniform') {
        recommendations.push({
          type: 'resize_distortion',
          title: '非均匀缩放警告',
          description: `X:${resizeAnalysis.scaleFactors.x}, Y:${resizeAnalysis.scaleFactors.y}, Z:${resizeAnalysis.scaleFactors.z} - 可能导致画面变形`,
          priority: 'medium'
        });
      }
    }

    return recommendations;
  }
}

// 导出单例实例
const detector = new VFXDataDetector();

module.exports = {
  analyzeXML: (xmlContent, fileName) => detector.analyzeXML(xmlContent, fileName)
};

@echo off
title XML Tool Simple Launcher

echo XML Tool Simple Launcher
echo ========================
echo.

cd /d "%~dp0"
echo Current directory: %CD%
echo.

echo Checking Node.js...
node --version
if errorlevel 1 (
    echo ERROR: Node.js not found
    echo Please install Node.js from https://nodejs.org
    echo.
    pause
    exit
)
echo Node.js OK
echo.

echo Checking files...
if not exist "package.json" (
    echo ERROR: package.json not found
    pause
    exit
)
if not exist "server.js" (
    echo ERROR: server.js not found
    pause
    exit
)
echo Files OK
echo.

echo Installing dependencies if needed...
if not exist "node_modules" (
    echo Running npm install...
    npm install
    if errorlevel 1 (
        echo ERROR: npm install failed
        pause
        exit
    )
)
echo Dependencies OK
echo.

echo Starting server...
echo Visit http://localhost:3001 after startup
echo Press Ctrl+C to stop
echo.

node server.js

echo.
echo Server stopped
pause

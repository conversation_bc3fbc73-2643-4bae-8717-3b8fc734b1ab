@echo off
title XML Tool Simple Launcher

echo XML Tool Simple Launcher
echo ========================
echo.

cd /d "%~dp0"
echo Current directory: %CD%
echo.

echo Checking Node.js...
node --version
if errorlevel 1 (
    echo ERROR: Node.js not found
    echo Please install Node.js from https://nodejs.org
    echo.
    pause
    exit
)
echo Node.js OK
echo.

echo Checking files...
if not exist "package.json" (
    echo ERROR: package.json not found
    pause
    exit
)
if not exist "server.js" (
    echo ERROR: server.js not found
    pause
    exit
)
echo Files OK
echo.

echo Installing dependencies if needed...
if not exist "node_modules" (
    echo Running npm install...
    npm install
    if errorlevel 1 (
        echo ERROR: npm install failed
        pause
        exit
    )
)
echo Dependencies OK
echo.

echo Checking for running servers...
tasklist /FI "IMAGENAME eq node.exe" 2>nul | find /I "node.exe" >nul
if not errorlevel 1 (
    echo WARNING: Node.js processes are already running
    echo Attempting to stop existing servers...
    taskkill /F /IM node.exe >nul 2>&1
    timeout /t 2 /nobreak >nul
)

echo Checking port availability...
netstat -an | findstr ":3001" >nul 2>&1
if not errorlevel 1 (
    echo WARNING: Port 3001 is in use, trying port 3002...
    set PORT=3002
    set SERVER_URL=http://localhost:3002
) else (
    set PORT=3001
    set SERVER_URL=http://localhost:3001
)

netstat -an | findstr ":%PORT%" >nul 2>&1
if not errorlevel 1 (
    echo WARNING: Port %PORT% is also in use, trying port 3003...
    set PORT=3003
    set SERVER_URL=http://localhost:3003
)

echo Using port: %PORT%
echo Server URL: %SERVER_URL%
echo.

echo Starting server...
echo Visit %SERVER_URL% after startup
echo Press Ctrl+C to stop
echo.

set PORT=%PORT%
node server.js

if errorlevel 1 (
    echo.
    echo ERROR: Server failed to start
    echo This might be due to:
    echo 1. Port still in use
    echo 2. Permission issues
    echo 3. Missing dependencies
    echo.
    echo Trying alternative startup method...
    echo.
    set PORT=3004
    echo Using emergency port 3004...
    node server.js
)

echo.
echo Server stopped
echo.
echo Press any key to exit...
pause >nul

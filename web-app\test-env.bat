@echo off
title Environment Test

echo Environment Test
echo =================
echo.

echo Current directory: %CD%
echo Script directory: %~dp0
echo.

echo Testing Node.js...
node --version
if errorlevel 1 (
    echo Node.js NOT FOUND
    echo Please install from https://nodejs.org
) else (
    echo Node.js OK
)
echo.

echo Testing npm...
npm --version
if errorlevel 1 (
    echo npm NOT FOUND
) else (
    echo npm OK
)
echo.

echo Checking files...
if exist "package.json" (
    echo package.json FOUND
) else (
    echo package.json NOT FOUND
)

if exist "server.js" (
    echo server.js FOUND
) else (
    echo server.js NOT FOUND
)
echo.

echo Test completed
pause

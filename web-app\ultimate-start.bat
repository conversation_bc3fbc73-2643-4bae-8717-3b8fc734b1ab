@echo off
title VFX XML Data Analysis Tool

echo ========================================
echo   VFX XML Data Analysis Tool
echo ========================================
echo.
echo Professional VFX XML file analysis platform
echo Supports Maya, Blender, Cinema 4D and more!
echo.

:: Change to script directory
cd /d "%~dp0"

:: Step 1: Environment check
echo [1/6] Checking environment...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found!
    echo Please install Node.js from https://nodejs.org
    echo.
    pause
    exit /b 1
)
echo Node.js: OK

:: Step 2: File check
if not exist "server.js" (
    echo ERROR: server.js not found!
    echo Please run this script in the correct directory.
    pause
    exit /b 1
)
echo Files: OK

:: Step 3: Dependencies
echo.
echo [2/6] Checking dependencies...
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
)
echo Dependencies: OK

:: Step 4: Kill existing processes
echo.
echo [3/6] Stopping existing servers...
powershell -Command "Get-Process -Name node -ErrorAction SilentlyContinue | Stop-Process -Force" >nul 2>&1
timeout /t 2 /nobreak >nul
echo Cleanup: OK

:: Step 5: Find available port
echo.
echo [4/6] Finding available port...
set "FINAL_PORT="

:: Check ports 3001-3020
for /L %%p in (3001,1,3020) do (
    if not defined FINAL_PORT (
        netstat -an | findstr ":%%p " >nul 2>&1
        if errorlevel 1 (
            set "FINAL_PORT=%%p"
        )
    )
)

if not defined FINAL_PORT (
    echo ERROR: No available ports found!
    echo Please restart your computer.
    pause
    exit /b 1
)

echo Available port found: %FINAL_PORT%

:: Step 6: Start server
echo.
echo [5/6] Starting server...
echo.
echo ========================================
echo   VFX Analysis Server Starting
echo   Port: %FINAL_PORT%
echo   URL: http://localhost:%FINAL_PORT%
echo   Features: Drag & Drop, Timewarp, Resize
echo ========================================
echo.
echo Available Pages:
echo    Main Page (Drag & Drop): http://localhost:%FINAL_PORT%
echo    Professional Analysis: http://localhost:%FINAL_PORT%/demo-stable.html
echo    XML Diagnostic: http://localhost:%FINAL_PORT%/xml-diagnostic.html
echo    UNC Reader: http://localhost:%FINAL_PORT%/unc-reader.html
echo    API Documentation: http://localhost:%FINAL_PORT%/api-docs.html
echo.
echo Press Ctrl+C to stop the server
echo.

set PORT=%FINAL_PORT%
node server.js

:: Step 7: Cleanup
echo.
echo [6/6] Server stopped
echo.
echo Thank you for using VFX XML Data Analysis Tool!
pause

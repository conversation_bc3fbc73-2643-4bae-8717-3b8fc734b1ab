#!/bin/bash

# XML检测工具权限修复脚本
# 如果启动工具.command无法执行，请运行此脚本

echo "========================================="
echo "  XML检测工具权限修复脚本"
echo "========================================="
echo ""

# 获取脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cd "$SCRIPT_DIR"

echo "当前目录: $SCRIPT_DIR"
echo ""

echo "正在修复文件权限..."

# 修复启动脚本权限
if [ -f "启动工具.command" ]; then
    chmod +x "启动工具.command"
    echo "✅ 启动工具.command 权限已修复"
else
    echo "❌ 未找到启动工具.command文件"
fi

# 修复其他脚本权限
if [ -f "修复权限.command" ]; then
    chmod +x "修复权限.command"
    echo "✅ 修复权限.command 权限已修复"
fi

echo ""
echo "权限修复完成！"
echo ""
echo "现在您可以："
echo "1. 双击运行 '启动工具.command'"
echo "2. 或者右键选择 '打开'"
echo ""
echo "如果仍有问题，请尝试以下方法："
echo "1. 在访达中右键点击文件 → 显示简介 → 共享与权限"
echo "2. 点击锁图标解锁，输入密码"
echo "3. 将权限改为 '读与写'"
echo ""

read -p "按回车键关闭窗口..."

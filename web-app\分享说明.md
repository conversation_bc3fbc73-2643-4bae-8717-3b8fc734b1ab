# 📤 XML变速数据检测工具 - 分享说明

## 🎯 **给接收者的重要说明**

### ⚠️ **这不是普通的网页文件！**

**不能直接双击HTML文件使用！**

这是一个完整的Web应用程序，需要：
1. 安装Node.js运行环境
2. 启动后端服务器
3. 在浏览器中访问

## 🚀 **最简单的使用方法**

### **第一步：安装Node.js**
1. 访问：https://nodejs.org
2. 下载并安装LTS版本（推荐）
3. 安装完成后重启电脑

### **第二步：启动应用**

#### **macOS用户**
双击运行：`启动工具.command`

#### **Windows用户**
双击运行：`启动工具.bat`

#### **Linux用户**
在终端中执行：
```bash
chmod +x 启动工具.command
./启动工具.command
```

### **第三步：使用应用**
- 启动脚本会自动打开浏览器
- 如果没有自动打开，请访问：http://localhost:3001

## 📋 **详细使用指南**

### **系统要求**
- **Node.js** 16.0 或更高版本
- **现代浏览器**（Chrome、Safari、Firefox、Edge）
- **操作系统**：Windows 10+、macOS 10.15+、Linux

### **功能说明**
- **XML文件分析**：上传XML文件进行速度数据分析
- **文本输入分析**：直接输入XML内容进行分析
- **UNC路径读取**：读取网络共享文件夹中的XML文件
- **示例演示**：内置示例数据用于功能演示

### **主要页面**
- **主页**：http://localhost:3001
- **功能演示**：http://localhost:3001/demo-stable.html
- **UNC路径读取**：http://localhost:3001/unc-reader.html

## 🔧 **故障排除**

### **常见问题**

**Q1: 双击启动脚本没反应？**
- 确保已安装Node.js
- Windows用户：右键选择"以管理员身份运行"
- macOS用户：右键选择"打开"

**Q2: 提示"command not found: node"？**
- Node.js未正确安装，请重新安装
- 重启电脑后重试

**Q3: 端口被占用？**
- 启动脚本会自动尝试其他端口
- 或手动关闭占用端口的程序

**Q4: 浏览器无法访问？**
- 确认启动脚本显示"服务器启动成功"
- 尝试访问：http://127.0.0.1:3001
- 检查防火墙设置

**Q5: 页面按钮无响应？**
- 刷新浏览器页面（F5或Ctrl+R）
- 清除浏览器缓存
- 尝试使用其他浏览器

## 🛑 **停止应用**

- **启动脚本窗口**：按任意键或关闭窗口
- **手动停止**：在终端按Ctrl+C

## 📱 **移动设备访问**

如果需要在手机/平板上使用：

1. **确保设备在同一WiFi网络**
2. **查找电脑IP地址**：
   - Windows：在命令提示符输入 `ipconfig`
   - macOS/Linux：在终端输入 `ifconfig`
3. **在移动设备浏览器访问**：
   ```
   http://[电脑IP地址]:3001
   ```
   例如：http://*************:3001

## 🔒 **安全提醒**

- 仅在受信任的网络环境中使用
- 不要在公共WiFi上启动服务器
- 使用完毕后及时停止服务器
- 上传的文件仅在内存中处理，不会保存到硬盘

## 📞 **需要帮助？**

如果遇到问题：

1. **查看错误信息**：注意启动脚本中的错误提示
2. **重新安装Node.js**：从官网下载最新版本
3. **重启电脑**：安装Node.js后重启
4. **检查网络**：确保网络连接正常
5. **尝试其他浏览器**：Chrome、Firefox、Safari等

## 📁 **文件说明**

- `启动工具.command` - macOS/Linux启动脚本
- `启动工具.bat` - Windows启动脚本
- `README.md` - 详细使用说明
- `iOS用户使用指南.md` - iOS用户专用指南
- `server.js` - 后端服务器程序
- `public/` - 前端页面文件
- `src/` - 后端逻辑代码

## 🎉 **开始使用**

1. **安装Node.js** → https://nodejs.org
2. **双击启动脚本** → 等待自动安装和启动
3. **打开浏览器** → 访问 http://localhost:3001
4. **体验功能** → 上传XML文件或使用演示功能

**祝您使用愉快！** 🚀

---

**如果这个说明对您有帮助，请给开发者一个好评！** ⭐️

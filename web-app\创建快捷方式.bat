@echo off
title 创建启动快捷方式

echo ========================================
echo   创建XML检测工具启动快捷方式
echo ========================================
echo.

:: 获取当前目录
set "CURRENT_DIR=%~dp0"
set "SHORTCUT_NAME=XML检测工具.lnk"
set "DESKTOP=%USERPROFILE%\Desktop"

echo 当前目录: %CURRENT_DIR%
echo 桌面路径: %DESKTOP%
echo.

:: 创建VBS脚本来生成快捷方式
echo 正在创建快捷方式...

echo Set WshShell = WScript.CreateObject("WScript.Shell") > temp_shortcut.vbs
echo Set oShellLink = WshShell.CreateShortcut("%DESKTOP%\%SHORTCUT_NAME%") >> temp_shortcut.vbs
echo oShellLink.TargetPath = "cmd.exe" >> temp_shortcut.vbs
echo oShellLink.Arguments = "/k cd /d ""%CURRENT_DIR%"" && node server.js" >> temp_shortcut.vbs
echo oShellLink.WorkingDirectory = "%CURRENT_DIR%" >> temp_shortcut.vbs
echo oShellLink.Description = "XML变速数据检测工具" >> temp_shortcut.vbs
echo oShellLink.Save >> temp_shortcut.vbs

:: 执行VBS脚本
cscript //nologo temp_shortcut.vbs

:: 删除临时文件
del temp_shortcut.vbs

if exist "%DESKTOP%\%SHORTCUT_NAME%" (
    echo.
    echo ✅ 快捷方式创建成功！
    echo.
    echo 📁 位置: %DESKTOP%\%SHORTCUT_NAME%
    echo.
    echo 💡 使用方法:
    echo 1. 双击桌面上的"XML检测工具"快捷方式
    echo 2. 等待服务器启动
    echo 3. 在浏览器中访问 http://localhost:3001
    echo.
    echo 🔧 快捷方式功能:
    echo - 自动进入正确的目录
    echo - 直接启动服务器
    echo - 保持命令窗口打开
    echo - 显示详细的启动信息
    echo.
) else (
    echo.
    echo ❌ 快捷方式创建失败
    echo.
    echo 请尝试手动创建:
    echo 1. 在桌面右键 → 新建 → 快捷方式
    echo 2. 位置输入: cmd.exe /k cd /d "%CURRENT_DIR%" ^&^& node server.js
    echo 3. 名称输入: XML检测工具
    echo.
)

echo 按任意键退出...
pause >nul

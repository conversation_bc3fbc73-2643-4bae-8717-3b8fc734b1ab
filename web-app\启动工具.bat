@echo off
chcp 65001 >nul
title XML变速数据检测工具启动器

echo.
echo 🚀 XML变速数据检测工具启动脚本
echo ==================================
echo.

:: 获取当前目录
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

echo 📁 当前目录: %SCRIPT_DIR%
echo.

:: 检查Node.js是否安装
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Node.js
    echo.
    echo 请先安装Node.js:
    echo 1. 访问 https://nodejs.org
    echo 2. 下载并安装LTS版本
    echo 3. 重新运行此脚本
    echo.
    pause
    exit /b 1
)

:: 显示Node.js版本
for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js版本: %NODE_VERSION%
echo.

:: 检查npm是否可用
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到npm
    echo 请重新安装Node.js
    echo.
    pause
    exit /b 1
)

:: 检查是否已安装依赖
if not exist "node_modules" (
    echo 📦 首次运行，正在安装依赖...
    echo 这可能需要几分钟时间，请耐心等待...
    echo.
    
    call npm install
    
    if errorlevel 1 (
        echo.
        echo ❌ 依赖安装失败
        echo 请检查网络连接或手动运行: npm install
        echo.
        pause
        exit /b 1
    )
    
    echo.
    echo ✅ 依赖安装完成
    echo.
)

:: 检查端口是否被占用
netstat -an | findstr ":3001" >nul 2>&1
if not errorlevel 1 (
    echo ⚠️  警告: 端口3001已被占用
    echo 将尝试使用端口3002...
    set PORT=3002
    set "SERVER_URL=http://localhost:3002"
) else (
    set "SERVER_URL=http://localhost:3001"
)

echo 🚀 正在启动服务器...
echo.

:: 启动服务器
start /b node server.js

:: 等待服务器启动
timeout /t 3 /nobreak >nul

:: 检查服务器是否成功启动
curl -s %SERVER_URL%/api/health >nul 2>&1
if not errorlevel 1 (
    echo ✅ 服务器启动成功!
    echo.
    echo 🌐 访问地址: %SERVER_URL%
    echo.
    echo 📋 功能页面:
    echo    主页: %SERVER_URL%
    echo    演示: %SERVER_URL%/demo-stable.html
    echo    UNC读取: %SERVER_URL%/unc-reader.html
    echo.
    echo 💡 提示:
    echo    - 服务器将在后台运行
    echo    - 关闭此窗口将停止服务器
    echo    - 按 Ctrl+C 可手动停止服务器
    echo.
    
    :: 尝试自动打开浏览器
    echo 🔗 正在打开浏览器...
    timeout /t 2 /nobreak >nul
    start "" "%SERVER_URL%"
    
    echo.
    echo 🎉 应用已启动，请在浏览器中使用!
    echo.
    echo 按任意键停止服务器...
    pause >nul
    
    :: 停止服务器
    taskkill /f /im node.exe >nul 2>&1
    
) else (
    echo ❌ 服务器启动失败
    echo.
    echo 请检查:
    echo 1. Node.js是否正确安装
    echo 2. 端口是否被占用
    echo 3. 是否有足够的权限
    echo.
    pause
    exit /b 1
)

echo.
echo 👋 服务器已停止，感谢使用!
echo.
pause

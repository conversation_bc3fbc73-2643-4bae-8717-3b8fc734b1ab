#!/bin/bash

# VFX XML数据分析工具 - macOS启动脚本
# 双击此文件即可启动应用

echo "🎬 VFX XML数据分析工具启动脚本"
echo "=================================="
echo "专业的视效XML文件数据分析平台"
echo "支持Maya、Blender、Cinema 4D等VFX软件"
echo ""

# 获取脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cd "$SCRIPT_DIR"

echo "📁 当前目录: $SCRIPT_DIR"
echo ""

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 未找到Node.js"
    echo ""
    echo "请先安装Node.js:"
    echo "1. 访问 https://nodejs.org"
    echo "2. 下载并安装LTS版本"
    echo "3. 重新运行此脚本"
    echo ""
    echo "按任意键退出..."
    read -n 1
    exit 1
fi

# 显示Node.js版本
NODE_VERSION=$(node --version)
echo "✅ Node.js版本: $NODE_VERSION"
echo ""

# 检查npm是否可用
if ! command -v npm &> /dev/null; then
    echo "❌ 错误: 未找到npm"
    echo "请重新安装Node.js"
    echo ""
    echo "按任意键退出..."
    read -n 1
    exit 1
fi

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 首次运行，正在安装依赖..."
    echo "这可能需要几分钟时间，请耐心等待..."
    echo ""
    
    npm install
    
    if [ $? -ne 0 ]; then
        echo ""
        echo "❌ 依赖安装失败"
        echo "请检查网络连接或手动运行: npm install"
        echo ""
        echo "按任意键退出..."
        read -n 1
        exit 1
    fi
    
    echo ""
    echo "✅ 依赖安装完成"
    echo ""
fi

# 检查端口是否被占用
if lsof -Pi :3001 -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  警告: 端口3001已被占用"
    echo "将尝试使用端口3002..."
    export PORT=3002
    SERVER_URL="http://localhost:3002"
else
    SERVER_URL="http://localhost:3001"
fi

echo "🚀 正在启动服务器..."
echo ""

# 启动服务器
node server.js &
SERVER_PID=$!

# 等待服务器启动
sleep 3

# 检查服务器是否成功启动
if kill -0 $SERVER_PID 2>/dev/null; then
    echo "✅ VFX分析服务器启动成功!"
    echo ""
    echo "🌐 访问地址: $SERVER_URL"
    echo ""
    echo "📋 功能页面:"
    echo "   🏠 主页 (拖拽上传): $SERVER_URL"
    echo "   🎬 专业分析: $SERVER_URL/demo-stable.html"
    echo "   🔍 XML诊断: $SERVER_URL/xml-diagnostic.html"
    echo "   📁 UNC读取: $SERVER_URL/unc-reader.html"
    echo "   📚 API文档: $SERVER_URL/api-docs.html"
    echo ""
    echo "💡 提示:"
    echo "   - 服务器将在后台运行"
    echo "   - 关闭此窗口将停止服务器"
    echo "   - 按 Ctrl+C 可手动停止服务器"
    echo ""
    
    # 尝试自动打开浏览器
    if command -v open &> /dev/null; then
        echo "🔗 正在打开浏览器..."
        sleep 2
        open "$SERVER_URL"
    fi
    
    echo ""
    echo "🎉 VFX分析工具已启动，支持拖拽上传!"
    echo ""
    echo "按 Ctrl+C 停止服务器，或直接关闭此窗口"
    
    # 等待用户停止
    wait $SERVER_PID
    
else
    echo "❌ 服务器启动失败"
    echo ""
    echo "请检查:"
    echo "1. Node.js是否正确安装"
    echo "2. 端口是否被占用"
    echo "3. 是否有足够的权限"
    echo ""
    echo "按任意键退出..."
    read -n 1
    exit 1
fi

echo ""
echo "👋 VFX分析服务器已停止，感谢使用!"
echo ""
echo "按任意键关闭窗口..."
read -n 1

# VFX XML数据分析工具 - PowerShell启动脚本
# 使用方法: 右键点击 -> "使用PowerShell运行"

# 设置控制台编码
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$Host.UI.RawUI.WindowTitle = "VFX XML数据分析工具启动器"

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   VFX XML数据分析工具启动脚本 v2.0" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "专业的视效XML文件数据分析平台" -ForegroundColor Green
Write-Host "支持Maya、Blender、Cinema 4D、After Effects等" -ForegroundColor Green
Write-Host "功能：拖拽分析、变速检测、缩放检测" -ForegroundColor Green
Write-Host ""

# 获取脚本所在目录
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $ScriptDir

Write-Host "[信息] 当前目录: $ScriptDir" -ForegroundColor Green
Write-Host ""

# 检查Node.js
Write-Host "[检查] 正在检查Node.js安装..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "[成功] Node.js版本: $nodeVersion" -ForegroundColor Green
    } else {
        throw "Node.js未找到"
    }
} catch {
    Write-Host "[错误] 未找到Node.js" -ForegroundColor Red
    Write-Host ""
    Write-Host "请按照以下步骤安装Node.js:" -ForegroundColor Yellow
    Write-Host "1. 访问 https://nodejs.org"
    Write-Host "2. 下载Windows版本的LTS版本"
    Write-Host "3. 运行安装程序，使用默认设置"
    Write-Host "4. 安装完成后重启电脑"
    Write-Host "5. 重新运行此脚本"
    Write-Host ""
    Write-Host "按任意键打开Node.js下载页面..." -ForegroundColor Cyan
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    Start-Process "https://nodejs.org"
    exit 1
}

# 检查npm
Write-Host "[检查] 正在检查npm..." -ForegroundColor Yellow
try {
    $npmVersion = npm --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "[成功] npm版本: $npmVersion" -ForegroundColor Green
    } else {
        throw "npm未找到"
    }
} catch {
    Write-Host "[错误] npm不可用，请重新安装Node.js" -ForegroundColor Red
    Read-Host "按Enter键退出"
    exit 1
}

# 检查package.json
if (-not (Test-Path "package.json")) {
    Write-Host "[错误] 未找到package.json文件" -ForegroundColor Red
    Write-Host "[信息] 请确保在正确的项目目录中运行此脚本" -ForegroundColor Yellow
    Write-Host "[信息] 当前目录: $ScriptDir" -ForegroundColor Yellow
    Read-Host "按Enter键退出"
    exit 1
}

Write-Host "[信息] 找到package.json文件" -ForegroundColor Green
Write-Host ""

# 检查依赖
if (-not (Test-Path "node_modules")) {
    Write-Host "[安装] 首次运行，正在安装依赖..." -ForegroundColor Yellow
    Write-Host "[安装] 这可能需要几分钟时间，请耐心等待..." -ForegroundColor Yellow
    Write-Host ""
    
    Write-Host "[执行] npm install" -ForegroundColor Cyan
    & npm install
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host ""
        Write-Host "[错误] 依赖安装失败，尝试其他方法..." -ForegroundColor Red
        Write-Host "[尝试] 清除npm缓存..." -ForegroundColor Yellow
        & npm cache clean --force
        Write-Host "[尝试] 重新安装..." -ForegroundColor Yellow
        & npm install --no-optional
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "[错误] 依赖安装仍然失败" -ForegroundColor Red
            Write-Host "[建议] 请检查网络连接或手动运行: npm install" -ForegroundColor Yellow
            Read-Host "按Enter键退出"
            exit 1
        }
    }
    
    Write-Host ""
    Write-Host "[成功] 依赖安装完成" -ForegroundColor Green
    Write-Host ""
} else {
    Write-Host "[信息] 依赖已安装，跳过安装步骤" -ForegroundColor Green
    Write-Host ""
}

# 智能端口选择
$Port = 3001
$ServerUrl = "http://localhost:$Port"

Write-Host "[检查] 检查端口占用情况..." -ForegroundColor Yellow

# 检查端口3001
$portInUse = Get-NetTCPConnection -LocalPort 3001 -ErrorAction SilentlyContinue
if ($portInUse) {
    Write-Host "[警告] 端口3001已被占用，尝试端口3002..." -ForegroundColor Yellow
    $Port = 3002
    $ServerUrl = "http://localhost:$Port"
    
    # 检查端口3002
    $portInUse = Get-NetTCPConnection -LocalPort 3002 -ErrorAction SilentlyContinue
    if ($portInUse) {
        Write-Host "[警告] 端口3002也被占用，尝试端口3003..." -ForegroundColor Yellow
        $Port = 3003
        $ServerUrl = "http://localhost:$Port"
    }
}

Write-Host "[信息] 使用端口: $Port" -ForegroundColor Green
Write-Host "[信息] 访问地址: $ServerUrl" -ForegroundColor Green
Write-Host ""

# 启动服务器
Write-Host "[启动] 正在启动VFX分析服务器..." -ForegroundColor Yellow
Write-Host "[启动] 请稍候..." -ForegroundColor Yellow
Write-Host ""

# 设置环境变量并启动
$env:PORT = $Port
$serverProcess = Start-Process -FilePath "node" -ArgumentList "server.js" -PassThru -WindowStyle Hidden

# 等待服务器启动
Write-Host "[等待] 等待服务器启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# 检查服务器状态
$serverStarted = $false
try {
    $response = Invoke-WebRequest -Uri "$ServerUrl/api/health" -UseBasicParsing -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        $serverStarted = $true
        Write-Host "[成功] 服务器启动成功" -ForegroundColor Green
    }
} catch {
    Write-Host "[重试] 服务器可能还在启动，再等待5秒..." -ForegroundColor Yellow
    Start-Sleep -Seconds 5
    
    try {
        $response = Invoke-WebRequest -Uri "$ServerUrl/api/health" -UseBasicParsing -TimeoutSec 5
        if ($response.StatusCode -eq 200) {
            $serverStarted = $true
            Write-Host "[成功] 服务器启动成功 (重试成功)" -ForegroundColor Green
        }
    } catch {
        Write-Host "[错误] 服务器启动失败或无法连接" -ForegroundColor Red
    }
}

if (-not $serverStarted) {
    Write-Host ""
    Write-Host "[诊断] 可能的原因:" -ForegroundColor Yellow
    Write-Host "1. 端口被其他程序占用"
    Write-Host "2. 防火墙阻止了连接"
    Write-Host "3. Node.js权限不足"
    Write-Host "4. 项目文件损坏"
    Write-Host ""
    Write-Host "[尝试] 手动启动服务器进行诊断..." -ForegroundColor Yellow
    Write-Host "[执行] node server.js" -ForegroundColor Cyan
    Write-Host ""
    & node server.js
    Read-Host "按Enter键退出"
    exit 1
}

# 服务器启动成功
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "   VFX分析服务器启动成功！" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "[访问] 主页 (拖拽上传): $ServerUrl" -ForegroundColor Cyan
Write-Host "[访问] 专业分析: $ServerUrl/demo-stable.html" -ForegroundColor Cyan
Write-Host "[访问] XML诊断: $ServerUrl/xml-diagnostic.html" -ForegroundColor Cyan
Write-Host "[访问] UNC读取: $ServerUrl/unc-reader.html" -ForegroundColor Cyan
Write-Host "[访问] API文档: $ServerUrl/api-docs.html" -ForegroundColor Cyan
Write-Host ""
Write-Host "[提示] 服务器将在后台运行" -ForegroundColor Yellow
Write-Host "[提示] 关闭此窗口将停止服务器" -ForegroundColor Yellow
Write-Host "[提示] 按 Ctrl+C 可手动停止服务器" -ForegroundColor Yellow
Write-Host ""

# 打开浏览器
Write-Host "[浏览器] 正在打开浏览器..." -ForegroundColor Yellow
Start-Sleep -Seconds 2

try {
    Start-Process $ServerUrl
} catch {
    Write-Host "[警告] 无法自动打开浏览器，请手动访问: $ServerUrl" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "   VFX分析工具已启动，支持拖拽上传！" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "[操作] 按任意键停止服务器..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# 停止服务器
Write-Host ""
Write-Host "[停止] 正在停止服务器..." -ForegroundColor Yellow
try {
    Stop-Process -Id $serverProcess.Id -Force -ErrorAction SilentlyContinue
    Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
} catch {
    # 忽略错误
}

Start-Sleep -Seconds 2
Write-Host "[完成] 服务器已停止，感谢使用！" -ForegroundColor Green
Write-Host ""
Read-Host "按Enter键退出"

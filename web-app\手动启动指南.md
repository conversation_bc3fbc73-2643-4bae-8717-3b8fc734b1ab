# 🔧 手动启动指南 - 解决bat文件闪退问题

## ❗ **如果bat文件闪退，请按以下步骤操作**

### 🎯 **第一步：测试基础环境**

1. **按 Win+R 键**
2. **输入 `cmd` 并按回车**
3. **在命令提示符中输入以下命令**：

```cmd
node --version
```

**期望结果**：显示版本号，如 `v18.17.0`
**如果显示错误**：说明Node.js未安装或未添加到PATH

### 🎯 **第二步：进入项目目录**

在命令提示符中执行：

```cmd
cd /d "C:\path\to\your\web-app"
```

**注意**：将路径替换为您实际的web-app文件夹路径

**验证**：执行 `dir` 命令，应该能看到 `package.json` 和 `server.js` 文件

### 🎯 **第三步：安装依赖**

```cmd
npm install
```

**等待安装完成**（可能需要几分钟）

### 🎯 **第四步：启动服务器**

```cmd
node server.js
```

**成功标志**：看到类似以下信息：
```
🚀 XML变速数据检测工具Web服务器启动成功
📡 服务器地址: http://localhost:3001
```

### 🎯 **第五步：访问应用**

打开浏览器，访问：http://localhost:3001

## 🔍 **故障排除**

### **问题1：node不是内部或外部命令**

**解决方案**：
1. 重新安装Node.js：https://nodejs.org
2. 安装时确保勾选"Add to PATH"选项
3. 安装完成后重启电脑
4. 重新打开命令提示符测试

### **问题2：npm install失败**

**解决方案**：
```cmd
# 清除缓存
npm cache clean --force

# 使用国内镜像
npm install --registry https://registry.npmmirror.com

# 或者跳过可选依赖
npm install --no-optional
```

### **问题3：端口被占用**

**解决方案**：
```cmd
# 使用其他端口
set PORT=3002
node server.js
```

然后访问：http://localhost:3002

### **问题4：权限不足**

**解决方案**：
1. 右键点击命令提示符
2. 选择"以管理员身份运行"
3. 重新执行上述步骤

## 🎯 **一键复制命令**

如果您熟悉命令行，可以复制以下命令：

```cmd
# 检查Node.js
node --version

# 进入项目目录（请修改路径）
cd /d "C:\path\to\your\web-app"

# 安装依赖
npm install

# 启动服务器
node server.js
```

## 🎯 **创建自己的启动脚本**

如果手动启动成功，您可以创建一个简单的bat文件：

1. **在web-app文件夹中新建文本文件**
2. **重命名为 `我的启动.bat`**
3. **编辑文件，输入以下内容**：

```bat
@echo off
cd /d "%~dp0"
echo 正在启动XML检测工具...
node server.js
pause
```

4. **保存并双击运行**

## 🎯 **为什么bat文件会闪退？**

常见原因：
1. **Node.js未安装**：系统找不到node命令
2. **路径问题**：脚本无法找到正确的文件
3. **权限问题**：没有足够的权限执行
4. **编码问题**：中文字符导致脚本解析错误
5. **语法错误**：脚本中有语法错误

## 🎯 **最终解决方案**

如果所有方法都不行：

1. **使用手动启动**（最可靠）
2. **或者使用PowerShell**：
   - 右键点击 `启动工具.ps1`
   - 选择"使用PowerShell运行"
3. **或者使用Node.js直接运行**：
   - 在文件管理器中按住Shift+右键
   - 选择"在此处打开PowerShell窗口"
   - 输入：`node server.js`

## 🎉 **成功启动后**

访问以下页面：
- **主页**：http://localhost:3001
- **演示**：http://localhost:3001/demo-stable.html
- **UNC读取**：http://localhost:3001/unc-reader.html

**记住**：服务器运行时不要关闭命令提示符窗口！

---

**如果仍有问题，请查看命令提示符中的具体错误信息。** 🔧

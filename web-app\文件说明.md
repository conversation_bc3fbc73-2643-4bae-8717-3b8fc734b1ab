# 📁 XML变速数据检测工具 - 文件说明

## 🚀 **启动脚本（推荐使用顺序）**

### **Windows用户**
1. **`ultimate-start.bat`** - 终极启动脚本（推荐，自动处理所有问题）
2. **`force-start.bat`** - 强制启动脚本（自动找端口）
3. **`start-simple.bat`** - 简化启动脚本（已修复端口冲突）
4. **`debug-start.bat`** - 调试启动脚本（详细信息）
5. **`clear-ports.bat`** - 端口清理工具
6. **`test-env.bat`** - 环境测试脚本
7. **`启动工具.ps1`** - PowerShell版本

### **macOS/Linux用户**
1. **`启动工具.command`** - macOS/Linux启动脚本
2. **`start.sh`** - Shell脚本版本

### **通用启动**
- **`start.bat`** - 基础启动脚本

## 📚 **说明文档**

- **`README.md`** - 完整使用说明和功能介绍
- **`TROUBLESHOOTING.md`** - 英文版故障排除指南
- **`简单说明.txt`** - 中文简要说明
- **`文件说明.md`** - 本文件，说明各文件用途

## 🌐 **Web应用文件**

### **后端服务器**
- **`server.js`** - 主服务器文件
- **`package.json`** - 项目配置和依赖
- **`package-lock.json`** - 依赖版本锁定

### **前端页面（public文件夹）**
- **`index.html`** - 主页
- **`demo-stable.html`** - 功能演示页面
- **`unc-reader.html`** - UNC路径文件读取页面
- **`unc-help.html`** - UNC功能使用帮助
- **`api-docs.html`** - API文档页面
- **`status.html`** - 服务状态检查页面

### **后端逻辑（src文件夹）**
- **`xmlDetector.js`** - XML分析引擎

### **依赖包**
- **`node_modules/`** - npm安装的依赖包（自动生成）

## 🎯 **使用建议**

### **首次使用**
1. 确保已安装Node.js（https://nodejs.org）
2. 双击运行 `debug-start.bat`（Windows）或 `启动工具.command`（macOS）
3. 等待自动安装依赖并启动服务器
4. 访问 http://localhost:3001

### **日常使用**
- 直接双击启动脚本即可
- 或者手动运行：`node server.js`

### **遇到问题**
1. 查看 `TROUBLESHOOTING.md`
2. 使用 `test-env.bat` 检查环境
3. 尝试手动启动方法

## 🗑️ **已删除的文件**

为了简化分享包，已删除以下无用文件：
- 有编码问题的中文bat文件
- 重复的演示页面
- 多余的说明文档
- 测试用的临时文件

## 📦 **分享准备**

当前文件夹已经过清理，包含：
- ✅ 必要的启动脚本
- ✅ 完整的应用程序代码
- ✅ 核心功能页面
- ✅ 重要的说明文档
- ✅ 故障排除指南

可以直接压缩整个 `web-app` 文件夹进行分享。

## 🎉 **功能特性**

- **XML文件分析**：上传或输入XML内容进行分析
- **UNC路径读取**：直接读取网络共享文件夹中的XML文件
- **异常检测**：自动识别速度数据中的异常模式
- **可视化报告**：生成详细的分析报告
- **API接口**：提供完整的RESTful API
- **跨平台支持**：Windows、macOS、Linux

---

**使用愉快！如有问题请查看相关说明文档。** 🚀

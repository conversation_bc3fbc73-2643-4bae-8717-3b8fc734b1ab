Mac权限问题解决方法
==================

如果双击"启动工具.command"提示权限问题：

最简单解决方法：
1. 右键点击"启动工具.command"文件
2. 选择"打开"
3. 在弹出对话框中点击"打开"

备用方法：
1. 双击"修复权限.command"
2. 如果这个文件也有权限问题，同样右键选择"打开"

终端方法（适合熟悉电脑的用户）：
1. 打开终端应用
2. 输入：cd ~/Desktop/web-app
3. 输入：chmod +x "启动工具.command"
4. 输入：./启动工具.command

成功标志：
看到"服务器启动成功"和"访问地址: http://localhost:3001"

iOS设备访问：
1. 确保iPhone/iPad连接同一WiFi
2. 在Mac的系统偏好设置→网络中查看IP地址
3. 在iPhone Safari中访问：http://[Mac的IP地址]:3001

重要：启动后不要关闭终端窗口，否则服务器会停止。

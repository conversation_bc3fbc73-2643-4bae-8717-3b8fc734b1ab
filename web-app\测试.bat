@echo off
title 基础测试

echo 基础测试开始...
echo.

echo 当前目录: %CD%
echo 脚本目录: %~dp0
echo.

echo 测试Node.js...
node --version
if errorlevel 1 (
    echo Node.js未安装或不在PATH中
) else (
    echo Node.js正常
)
echo.

echo 测试npm...
npm --version
if errorlevel 1 (
    echo npm不可用
) else (
    echo npm正常
)
echo.

echo 检查文件...
if exist "package.json" (
    echo 找到package.json
) else (
    echo 未找到package.json
)

if exist "server.js" (
    echo 找到server.js
) else (
    echo 未找到server.js
)
echo.

echo 测试完成
echo.
pause

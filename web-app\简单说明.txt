XML检测工具启动说明
==================

问题原因：
之前的bat文件包含中文字符，导致编码问题。

解决方案：
1. 双击运行：debug-start.bat（英文版调试脚本）
2. 或者双击：start-simple.bat（简化版启动脚本）
3. 或者双击：test-env.bat（环境测试脚本）

手动启动方法：
1. 按Win+R，输入cmd，按回车
2. 输入：cd /d "你的web-app文件夹路径"
3. 输入：npm install（首次运行）
4. 输入：node server.js
5. 访问：http://localhost:3001

重要提醒：
- 必须先安装Node.js（https://nodejs.org）
- 安装时要勾选"Add to PATH"选项
- 安装完成后重启电脑

成功标志：
看到服务器启动信息，然后在浏览器访问http://localhost:3001

详细说明请查看：TROUBLESHOOTING.md

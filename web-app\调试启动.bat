@echo off
:: 调试版启动脚本 - 不会闪退，显示所有错误信息
title XML检测工具调试启动器

:: 确保窗口不会自动关闭
setlocal

echo ========================================
echo   XML检测工具调试启动器
echo ========================================
echo.
echo 此脚本会显示详细的调试信息
echo 如果出现错误，窗口不会自动关闭
echo.

:: 显示当前目录
echo [调试] 当前目录: %CD%
echo [调试] 脚本位置: %~dp0
echo.

:: 切换到脚本所在目录
echo [调试] 切换到脚本目录...
cd /d "%~dp0"
echo [调试] 切换后目录: %CD%
echo.

:: 检查关键文件
echo [调试] 检查关键文件...
if exist "package.json" (
    echo [成功] 找到 package.json
) else (
    echo [错误] 未找到 package.json
    echo [错误] 请确保在正确的项目目录中运行
    goto :error_exit
)

if exist "server.js" (
    echo [成功] 找到 server.js
) else (
    echo [错误] 未找到 server.js
    echo [错误] 项目文件不完整
    goto :error_exit
)
echo.

:: 检查Node.js - 基础检查
echo [调试] 检查Node.js安装...
echo [调试] 执行命令: node --version

node --version >nul 2>&1
if errorlevel 1 (
    echo [错误] Node.js命令执行失败
    echo [调试] 尝试显示详细错误...
    node --version
    echo.
    echo [解决] 请按照以下步骤安装Node.js:
    echo 1. 访问 https://nodejs.org
    echo 2. 下载Windows版本的LTS版本
    echo 3. 运行安装程序，选择"Add to PATH"选项
    echo 4. 安装完成后重启电脑
    echo 5. 重新运行此脚本
    echo.
    goto :error_exit
) else (
    for /f "tokens=*" %%i in ('node --version 2^>nul') do set NODE_VERSION=%%i
    echo [成功] Node.js版本: %NODE_VERSION%
)
echo.

:: 检查npm
echo [调试] 检查npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo [错误] npm不可用
    echo [调试] 尝试显示npm错误...
    npm --version
    goto :error_exit
) else (
    for /f "tokens=*" %%i in ('npm --version 2^>nul') do set NPM_VERSION=%%i
    echo [成功] npm版本: %NPM_VERSION%
)
echo.

:: 检查依赖
echo [调试] 检查项目依赖...
if exist "node_modules" (
    echo [信息] 依赖已安装
) else (
    echo [信息] 需要安装依赖
    echo [执行] npm install
    echo [提示] 这可能需要几分钟，请耐心等待...
    echo.
    
    call npm install
    if errorlevel 1 (
        echo.
        echo [错误] 依赖安装失败
        echo [调试] 尝试显示详细错误...
        echo [重试] 使用详细模式重新安装...
        call npm install --verbose
        if errorlevel 1 (
            echo [错误] 依赖安装仍然失败
            goto :error_exit
        )
    )
    echo [成功] 依赖安装完成
)
echo.

:: 尝试启动服务器
echo [调试] 准备启动服务器...
echo [信息] 如果启动成功，请访问: http://localhost:3001
echo [信息] 按 Ctrl+C 可以停止服务器
echo.
echo [执行] node server.js
echo ========================================
echo.

:: 直接启动服务器（前台模式，便于查看错误）
node server.js

:: 如果到达这里，说明服务器已停止
echo.
echo ========================================
echo [信息] 服务器已停止
goto :normal_exit

:error_exit
echo.
echo ========================================
echo [错误] 启动过程中遇到错误
echo ========================================
echo.
echo 常见解决方案:
echo 1. 确保已正确安装Node.js
echo 2. 重启电脑后重试
echo 3. 以管理员身份运行此脚本
echo 4. 检查网络连接
echo 5. 查看上方的详细错误信息
echo.
goto :pause_exit

:normal_exit
echo [信息] 脚本正常结束
echo.

:pause_exit
echo 按任意键关闭窗口...
pause >nul
exit /b

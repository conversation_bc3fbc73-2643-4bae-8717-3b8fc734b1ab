# 🍎 Mac用户快速指南 - XML变速数据检测工具

## ⚡ **一键启动（推荐）**

### **双击启动**
1. **双击运行**：`启动工具.command`
2. **如果提示权限问题**：
   - 右键点击`启动工具.command`
   - 选择"打开"
   - 在弹出对话框中点击"打开"
3. **等待启动完成**：看到"✅ 服务器启动成功!"
4. **访问应用**：http://localhost:3001

## 📋 **前置要求**

### **安装Node.js**
1. **访问**：https://nodejs.org
2. **下载macOS版本**：选择LTS版本（推荐）
3. **安装**：双击.pkg文件，按向导完成安装
4. **重启Mac**（重要！）

### **验证安装**
打开终端，输入：
```bash
node --version
```
应该显示版本号，如：`v18.17.0`

## 🚀 **启动方法**

### **方法1：双击启动（最简单）**
- 双击：`启动工具.command`
- 自动安装依赖并启动服务器
- 浏览器自动打开应用页面

### **方法2：终端启动**
```bash
# 进入项目目录
cd /path/to/web-app

# 安装依赖（首次运行）
npm install

# 启动服务器
node server.js
```

### **方法3：PowerShell启动**
- 右键点击：`启动工具.ps1`
- 选择："使用PowerShell运行"

## 🌟 **启动成功标志**

看到以下信息说明启动成功：
```
✅ 服务器启动成功!
🌐 访问地址: http://localhost:3001
📱 移动设备访问: http://[你的IP]:3001
```

## 🔗 **功能页面**

启动后可访问：
- **主页**：http://localhost:3001
- **功能演示**：http://localhost:3001/demo-stable.html
- **UNC路径读取**：http://localhost:3001/unc-reader.html
- **API文档**：http://localhost:3001/api-docs.html

## 📱 **iOS设备访问**

如果需要在iPhone/iPad上使用：

### **1. 查找Mac的IP地址**
**方法A（图形界面）**：
- 系统偏好设置 → 网络 → WiFi
- 查看右侧显示的IP地址

**方法B（终端）**：
```bash
ifconfig | grep "inet " | grep -v 127.0.0.1
```

### **2. 在iOS设备访问**
- 确保iOS设备连接同一WiFi
- 在Safari中访问：`http://[Mac的IP地址]:3001`
- 例如：`http://*************:3001`

## 🔧 **常见问题**

### **Q: 双击.command文件提示权限问题？**
**快速解决**：
1. **双击运行**：`修复权限.command`
2. **或右键点击**：`启动工具.command` → "打开"
3. **或终端修复**：`chmod +x "启动工具.command"`

**详细解决方案**：查看 `🔐Mac权限问题解决指南.md`

### **Q: 提示"node命令未找到"？**
**解决**：
1. 重新安装Node.js
2. 重启Mac
3. 确认安装时选择了"Add to PATH"

### **Q: 端口被占用？**
**解决**：
- `启动工具.command`会自动处理端口冲突
- 或手动指定端口：`PORT=3002 node server.js`

### **Q: 防火墙阻止连接？**
**解决**：
1. 系统偏好设置 → 安全性与隐私 → 防火墙
2. 点击"防火墙选项"
3. 允许Node.js通过防火墙

## 🛑 **停止服务器**

- **在终端窗口按**：`Cmd+C`
- **或直接关闭**终端窗口

## 🎯 **使用技巧**

### **开发者模式**
如果需要修改代码：
```bash
# 监听文件变化自动重启
npm install -g nodemon
nodemon server.js
```

### **后台运行**
如果需要后台运行：
```bash
# 后台启动
nohup node server.js &

# 查看进程
ps aux | grep node

# 停止后台进程
pkill -f "node server.js"
```

### **性能监控**
查看服务器状态：
- 访问：http://localhost:3001/api/health
- 查看CPU和内存使用情况

## 📦 **文件说明**

- `启动工具.command` - Mac一键启动脚本
- `启动工具.ps1` - PowerShell启动脚本
- `server.js` - 主服务器文件
- `package.json` - 项目配置
- `public/` - 前端页面文件
- `src/` - 后端逻辑代码

## 🎉 **开始使用**

1. **确保已安装Node.js**
2. **双击启动脚本**
3. **等待自动安装和启动**
4. **在浏览器中享受XML分析功能**

---

**Mac用户现在可以轻松启动和使用XML检测工具了！** 🍎✨

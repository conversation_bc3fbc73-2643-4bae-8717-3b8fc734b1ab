# 📋 iOS用户快速设置卡片

## 🎯 **5分钟快速设置**

### **Mac端设置（3分钟）**

1. **安装Node.js**
   - 访问：https://nodejs.org
   - 下载并安装LTS版本
   - 重启Mac

2. **启动工具**
   - 解压web-app文件夹到桌面
   - 双击：`启动工具.command`
   - 等待显示"服务器启动成功"

3. **获取IP地址**
   - 系统偏好设置 → 网络 → WiFi
   - 记录IP地址（如：*************）

### **iOS端访问（2分钟）**

1. **连接同一WiFi**
   - 确保iPhone/iPad连接相同WiFi

2. **打开Safari**
   - 输入：`http://[Mac IP地址]:3001`
   - 例如：`http://*************:3001`

3. **验证成功**
   - 看到XML检测工具主页
   - 状态显示绿色"服务正常运行"

## ✅ **成功检查清单**

- [ ] Mac已安装Node.js
- [ ] 启动脚本运行成功
- [ ] 获得Mac的IP地址
- [ ] iOS设备连接同一WiFi
- [ ] iOS浏览器能访问工具主页
- [ ] 主页状态显示正常

## 🔗 **常用地址**

替换`[IP]`为您Mac的实际IP地址：

- **主页**：`http://[IP]:3001`
- **演示**：`http://[IP]:3001/demo-stable.html`
- **UNC读取**：`http://[IP]:3001/unc-reader.html`

## 🆘 **快速故障排除**

**问题**：iOS无法访问
**解决**：
1. 检查WiFi连接
2. 确认Mac服务器运行中
3. 验证IP地址正确
4. 检查Mac防火墙设置

**问题**：页面显示异常
**解决**：
1. 刷新Safari页面
2. 清除浏览器缓存
3. 尝试其他浏览器

---

**详细说明请查看：📱iOS用户使用指南.md** 📱

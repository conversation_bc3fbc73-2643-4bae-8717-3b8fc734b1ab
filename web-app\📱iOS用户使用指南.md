# 📱 iOS用户使用指南 - XML变速数据检测工具

## 🎯 **重要说明**

**⚠️ 这不是iOS原生应用，而是需要在Mac上运行的Web应用程序！**

iOS设备（iPhone/iPad）需要通过Mac电脑运行服务器，然后在iOS设备的浏览器中访问。

## 📋 **系统要求**

### **Mac电脑端（必需）**
- **macOS** 10.15 或更高版本
- **Node.js** 16.0 或更高版本
- **网络连接**：Mac和iOS设备在同一WiFi网络

### **iOS设备端**
- **iOS** 12.0 或更高版本
- **Safari浏览器**（推荐）或其他现代浏览器
- **WiFi连接**：与Mac在同一网络

## 🚀 **完整使用步骤**

### **第一步：在Mac上安装Node.js**

1. **打开Safari浏览器**
2. **访问**：https://nodejs.org
3. **下载macOS版本**：选择LTS版本（推荐）
4. **安装Node.js**：
   - 双击下载的.pkg文件
   - 按照安装向导完成安装
   - 选择默认设置即可
5. **重启Mac**（重要！）

### **第二步：解压和准备项目**

1. **解压文件**：
   - 双击压缩包解压到桌面
   - 确保文件夹名为"web-app"

2. **验证文件**：
   - 打开web-app文件夹
   - 确认包含以下文件：
     - `启动工具.command`
     - `server.js`
     - `package.json`
     - `public/`文件夹

### **第三步：启动服务器**

1. **双击运行**：`启动工具.command`
   
2. **如果提示权限问题**：
   - 右键点击`启动工具.command`
   - 选择"打开"
   - 在弹出对话框中点击"打开"

3. **等待启动完成**：
   - 终端窗口会显示安装和启动过程
   - 看到"服务器启动成功"表示完成
   - **不要关闭终端窗口！**

4. **记录服务器地址**：
   - 终端会显示类似：`服务器地址: http://localhost:3001`
   - 记住这个地址

### **第四步：查找Mac的IP地址**

1. **打开系统偏好设置**
2. **点击"网络"**
3. **选择当前连接的WiFi**
4. **查看IP地址**：
   - 右侧会显示类似：`IP地址: *************`
   - **记录这个IP地址**

**或者使用终端查找**：
1. 打开"终端"应用
2. 输入：`ifconfig | grep "inet " | grep -v 127.0.0.1`
3. 找到类似`*************`的地址

### **第五步：在iOS设备上访问**

1. **确保iOS设备连接同一WiFi**
2. **打开Safari浏览器**
3. **输入地址**：
   ```
   http://[Mac的IP地址]:3001
   ```
   例如：`http://*************:3001`

4. **访问成功标志**：
   - 看到XML检测工具主页
   - 页面显示"服务正常运行"（绿色状态）

## 🌟 **iOS设备上的功能使用**

### **主要功能页面**
- **主页**：`http://[Mac IP]:3001`
- **功能演示**：`http://[Mac IP]:3001/demo-stable.html`
- **UNC路径读取**：`http://[Mac IP]:3001/unc-reader.html`

### **在iOS上使用XML分析**

1. **文本输入方式**：
   - 点击"功能演示"
   - 点击"加载示例XML"
   - 点击"分析XML内容"
   - 查看分析结果

2. **文件上传方式**：
   - 在主页点击"选择文件"
   - 从iOS设备选择XML文件
   - 点击"开始分析"

## 🔧 **常见问题解决**

### **Q1: iOS设备无法访问Mac服务器**

**检查清单**：
- ✅ Mac和iOS设备在同一WiFi网络
- ✅ Mac的防火墙允许连接
- ✅ 使用正确的IP地址和端口
- ✅ Mac上的服务器正在运行

**解决方法**：
1. 在Mac的Safari中访问`http://localhost:3001`确认服务器正常
2. 检查Mac的防火墙设置：系统偏好设置 → 安全性与隐私 → 防火墙
3. 尝试重启Mac的WiFi连接

### **Q2: 页面显示不正常**

**解决方法**：
- 刷新iOS浏览器页面
- 清除Safari缓存
- 尝试使用Chrome或Firefox浏览器

### **Q3: 文件上传失败**

**解决方法**：
- 确保XML文件小于10MB
- 检查文件格式是否为.xml
- 尝试使用文本输入方式

## 📱 **iOS设备优化建议**

### **Safari浏览器设置**
1. **添加到主屏幕**：
   - 在Safari中打开工具主页
   - 点击分享按钮
   - 选择"添加到主屏幕"
   - 创建快捷方式图标

2. **全屏模式**：
   - 横屏使用获得更好体验
   - 隐藏Safari地址栏获得更多空间

### **网络优化**
- 使用5GHz WiFi频段（如果可用）
- 确保Mac和iOS设备距离路由器较近
- 避免网络拥堵时段使用

## 🛑 **停止和清理**

### **停止服务器**
1. **在Mac的终端窗口**按：`Ctrl+C`
2. **或直接关闭**终端窗口

### **清理步骤**
1. 停止Mac上的服务器
2. iOS设备可以关闭浏览器
3. 下次使用时重复启动步骤

## 🎉 **成功使用的标志**

- ✅ Mac终端显示"服务器启动成功"
- ✅ iOS设备能访问工具主页
- ✅ 主页显示绿色"服务正常运行"
- ✅ 能够上传和分析XML文件
- ✅ 功能演示页面正常工作

## 💡 **使用技巧**

1. **保持Mac不休眠**：使用期间保持Mac开启
2. **网络稳定**：确保WiFi连接稳定
3. **浏览器书签**：将常用页面添加到Safari书签
4. **多设备使用**：同一网络下的多个iOS设备都可以访问

---

**现在您可以在iOS设备上享受完整的XML变速数据检测功能了！** 📱✨

# 🚀 XML检测工具 - 快速启动指南

## ⚡ **一键启动（推荐）**

### **Windows用户**
**双击运行**：`ultimate-start.bat`

这个脚本会：
- ✅ 自动检查环境
- ✅ 自动安装依赖
- ✅ 自动停止冲突进程
- ✅ 自动找到可用端口
- ✅ 自动启动服务器
- ✅ 显示访问地址

### **macOS用户**
**双击运行**：`启动工具.command`

## 🔧 **如果一键启动失败**

### **方案1：端口冲突解决**
1. **双击运行**：`clear-ports.bat`（清理端口）
2. **然后运行**：`force-start.bat`（强制启动）

### **方案2：手动启动**
1. **按Win+R**，输入`cmd`，按回车
2. **输入命令**：
   ```cmd
   cd /d "你的web-app文件夹路径"
   npm install
   node server.js
   ```

### **方案3：使用PowerShell**
1. **右键点击**：`启动工具.ps1`
2. **选择**："使用PowerShell运行"

## 🎯 **成功启动的标志**

看到以下信息说明启动成功：
```
========================================
  Server starting on port 3001
  URL: http://localhost:3001
========================================
```

然后在浏览器访问显示的URL地址。

## 🌟 **主要功能**

启动成功后，您可以：

- **上传XML文件**进行速度数据分析
- **输入XML内容**进行实时分析
- **使用UNC路径**读取网络文件
- **查看详细报告**和异常检测结果
- **使用API接口**进行集成开发

## 📱 **移动设备访问**

如果需要在手机/平板访问：
1. 确保设备在同一WiFi网络
2. 查找电脑IP地址
3. 在移动设备访问：`http://[电脑IP]:端口号`

## 🛑 **停止服务器**

- **在启动窗口按**：`Ctrl+C`
- **或直接关闭**：启动窗口

## 📞 **需要帮助？**

如果仍有问题：
1. 查看 `TROUBLESHOOTING.md`（详细故障排除）
2. 使用 `test-env.bat`（环境测试）
3. 确保已安装Node.js（https://nodejs.org）

---

**现在就试试 `ultimate-start.bat`，一键启动您的XML检测工具！** 🚀
